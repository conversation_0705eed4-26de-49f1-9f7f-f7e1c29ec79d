using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;
using System.Linq;

public class CardAction<PERSON>uttonUI
{
    private List<Button> buttons;
    private List<TextMeshProUGUI> buttonTexts;
    private Transform actionButtonContainer;

    public CardActionButtonUI(
        Button primaryActionButton, Button secondaryActionButton, Button tertiaryActionButton,
        Button button4, Button button5, Button button6, But<PERSON> button7, Button button8,
        TextMeshProUGUI primaryActionText, TextMeshProUGUI secondaryActionText,
        TextMeshProUGUI tertiaryActionText, TextMeshProUGUI button4Text,
        TextMeshP<PERSON>UGUI button5Text, TextMeshProUGUI button6Text,
        TextMeshP<PERSON>UGUI button7Text, TextMeshP<PERSON>UGUI button8Text,
        Transform actionButtonContainer)
    {
        buttons = new List<Button> {
            primaryActionButton, secondaryActionButton, tertiaryActionButton,
            button4, button5, button6, button7, button8
        };

        buttonTexts = new List<TextMeshProUGUI> {
            primaryActionText, secondaryActionText, tertiaryActionText,
            button4Text, button5Text, button6Text, button7Text, button8Text
        };

        this.actionButtonContainer = actionButtonContainer;
        
        EnsureTextReferences();
    }

    private void EnsureTextReferences()
    {
        for (int i = 0; i < buttons.Count; i++)
        {
            if (buttons[i] != null && buttonTexts[i] == null)
            {
                buttonTexts[i] = buttons[i].GetComponentInChildren<TextMeshProUGUI>();
            }
        }
    }

    public void HideAllButtons()
    {
        if (buttonTexts[0] != null) buttonTexts[0].fontSize = 17;
        CleanupProcessorIcons();

        foreach (var button in buttons.Where(b => b != null))
        {
            button.gameObject.SetActive(false);
        }
    }

    public void ConfigureButton(int index, string text, UnityEngine.Events.UnityAction action, bool isEnabled = true, float width = 200f)
    {
        if (index < 0 || index >= buttons.Count || buttons[index] == null) return;

        buttons[index].gameObject.SetActive(true);
        
        if (buttonTexts[index] != null)
        {
            buttonTexts[index].text = text;
        }

        buttons[index].onClick.RemoveAllListeners();
        if (action != null)
        {
            buttons[index].onClick.AddListener(action);
        }

        buttons[index].interactable = isEnabled;

        // Set button width
        RectTransform buttonRect = buttons[index].GetComponent<RectTransform>();
        if (buttonRect != null)
        {
            Vector2 sizeDelta = buttonRect.sizeDelta;
            sizeDelta.x = width;
            buttonRect.sizeDelta = sizeDelta;
        }
    }

    public void SetButtonText(int index, string text, int fontSize = 17)
    {
        if (index < 0 || index >= buttonTexts.Count || buttonTexts[index] == null) return;
        
        buttonTexts[index].text = text;
        buttonTexts[index].fontSize = fontSize;
    }

    public Button GetButton(int index)
    {
        return (index >= 0 && index < buttons.Count) ? buttons[index] : null;
    }

    public void CleanupProcessorIcons()
    {
        foreach (Button button in buttons.Where(b => b != null))
        {
            Transform layoutTransform = button.transform.Find("IconLayout");
            if (layoutTransform != null)
            {
                GameObject.Destroy(layoutTransform.gameObject);
            }

            TextMeshProUGUI text = button.GetComponentInChildren<TextMeshProUGUI>();
            if (text != null)
            {
                text.gameObject.SetActive(true);
                text.text = ""; // Clear any text that might be left
            }
        }
    }


    public void ResetDetailPanelUI()
    {
        // Reset any UI modifications that might have been made
    }
}