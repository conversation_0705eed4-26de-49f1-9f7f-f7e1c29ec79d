using System.Collections.Generic;
using UnityEngine; 

public class GenericEffect : ICardEffect
{
    private string name;
    private string description;
    
    public GenericEffect(string name, string description)
    {
        this.name = name;
        this.description = description;
    }
    
    public bool CanExecute(Player player, GameObject location, Dictionary<string, object> parameters)
    {
        return true;
    }
    
    public bool Execute(Player player, GameObject location, Dictionary<string, object> parameters)
    {
        Debug.Log($"Player {player.PlayerId} executed effect: {name}");
        return true;
    }
    
    public string GetDescription()
    {
        return description;
    }
}