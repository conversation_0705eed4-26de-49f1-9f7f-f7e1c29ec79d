using UnityEngine;
using UnityEngine.EventSystems;
using System.Collections.Generic;

/// <summary>
/// Detects interactions with cards and UI elements
/// </summary>
public class CardInteractionDetector
{
    // Layer mask for card raycast detection
    private LayerMask cardLayerMask = -1;
    
    // Reference to the main card detail display
    private CardDetailDisplay cardDetailDisplay;
    
    // Camera references
    private Camera mainCamera;
    
    public CardInteractionDetector(CardDetailDisplay cardDetailDisplay, LayerMask cardLayerMask)
    {
        this.cardDetailDisplay = cardDetailDisplay;
        this.cardLayerMask = cardLayerMask;
        
        // Get the main camera
        mainCamera = Camera.main;
    }
    
    /// <summary>
    /// Update the main camera reference (useful if camera changes during gameplay)
    /// </summary>
    public void UpdateMainCamera()
    {
        mainCamera = Camera.main;
    }

    /// <summary>
    /// Check if a click should close the card detail panel
    /// </summary>
    public bool ShouldCloseOnClick(Vector3 clickPosition)
    {
        // IMPORTANT: First check if pointer is over any UI element
        if (IsPointerOverUI(clickPosition))
        {
            // If it's over the DetailPanel or any of its children, don't close
            if (IsClickOnDetailPanel(clickPosition))
            {
                return false;
            }

            // If it's over a card in the card row, don't close
            if (IsClickOnCardRow(clickPosition))
            {
                return false;
            }

            // NEW: Check if it's a click on undo/redo buttons
            if (IsClickOnUndoRedoButtons(clickPosition))
            {
                return false;
            }
        }

        // If clicked on a 3D card in the scene, don't close
        if (IsClickOn3DCard(clickPosition))
        {
            return false;
        }

        // If clicked on a world in the scene, don't close
        if (IsClickOnWorld(clickPosition))
        {
            return false;
        }
        // If we get here, close the panel
        return true;
    }


    // Add this new method to check for undo/redo buttons:
    private bool IsClickOnUndoRedoButtons(Vector3 position)
    {
        // Check if EventSystem is available
        if (EventSystem.current == null)
            return false;

        PointerEventData eventData = new PointerEventData(EventSystem.current);
        eventData.position = position;

        var raycastResults = new List<RaycastResult>();
        EventSystem.current.RaycastAll(eventData, raycastResults);

        // Look for buttons with "Undo" or "Redo" in their name
        foreach (var result in raycastResults)
        {
            if (result.gameObject.name.Contains("Undo") ||
                result.gameObject.name.Contains("Redo"))
            {
                return true;
            }

            // Also check parent objects that might be the button
            Transform parent = result.gameObject.transform.parent;
            while (parent != null)
            {
                if (parent.gameObject.name.Contains("Undo") ||
                    parent.gameObject.name.Contains("Redo"))
                {
                    return true;
                }
                parent = parent.parent;
            }
        }

        return false;
    }


    /// <summary>
    /// Check if click is on a world object
    /// </summary>
    private bool IsClickOnWorld(Vector3 position)
    {
        Camera activeCamera = FindActiveCamera();
        if (activeCamera == null)
        {
            return false;
        }

        Ray ray = activeCamera.ScreenPointToRay(position);
        RaycastHit hit;

        // Perform the raycast - don't filter by layer to catch all world objects
        if (Physics.Raycast(ray, out hit))
        {
            // Check if the hit object is a celestial body/world
            if (hit.collider.GetComponent<PlanetBody>() != null ||
                hit.collider.GetComponentInParent<PlanetBody>() != null)
            {
                return true;
            }
        }

        return false;
    }


    
    /// <summary>
    /// Check if the pointer is over any UI element
    /// </summary>
    private bool IsPointerOverUI(Vector3 position)
    {
        // Check if EventSystem is available
        if (EventSystem.current == null)
            return false;
            
        PointerEventData eventData = new PointerEventData(EventSystem.current);
        eventData.position = position;
        
        var raycastResults = new List<RaycastResult>();
        EventSystem.current.RaycastAll(eventData, raycastResults);
        
        return raycastResults.Count > 0;
    }

    /// <summary>
    /// Check specifically if the click is on the DetailPanel or any of its children
    /// </summary>
    private bool IsClickOnDetailPanel(Vector3 position)
    {
        // Check if pointer is over any UI element
        if (EventSystem.current == null)
            return false;

        PointerEventData eventData = new PointerEventData(EventSystem.current);
        eventData.position = position;

        var raycastResults = new List<RaycastResult>();
        EventSystem.current.RaycastAll(eventData, raycastResults);

        // Find the DetailPanel GameObject
        GameObject detailPanel = GetDetailPanel();
        if (detailPanel == null)
        {
            Debug.LogWarning("DetailPanel not found!");
            return false;
        }

        // Check if any of the UI elements hit belong to our detail panel
        foreach (var result in raycastResults)
        {
            // First check the exact object
            if (result.gameObject == detailPanel)
            {
                return true;
            }

            // Then check if it's a child of DetailPanel
            Transform parent = result.gameObject.transform.parent;
            while (parent != null)
            {
                if (parent.gameObject == detailPanel)
                {
                    return true;
                }
                parent = parent.parent;
            }

            // Also check by name for robustness
            if (result.gameObject.name == "DetailPanel" ||
                result.gameObject.name == "CardImage" ||
                result.gameObject.name == "ButtonContainer" ||
                result.gameObject.name == "ResourceListParent" ||
                result.gameObject.name == "flavorcontainer" ||
                result.gameObject.name == "HandAreaPanel" ||
                result.gameObject.name == "WorldCardDetail") // Add this line for world cards
            {
                return true;
            }
        }

        return false;
    }

    
    /// <summary>
    /// Get the detail panel GameObject
    /// </summary>
    private GameObject GetDetailPanel()
    {
        // First try to use the method from CardDetailDisplay if available
        System.Reflection.MethodInfo method = cardDetailDisplay.GetType().GetMethod("GetDetailPanel");
        if (method != null)
        {
            return method.Invoke(cardDetailDisplay, null) as GameObject;
        }
        
        // If that doesn't work, try to find it in the hierarchy
        Transform detailPanel = cardDetailDisplay.transform.Find("DetailPanel");
        if (detailPanel != null)
        {
            return detailPanel.gameObject;
        }
        
        // Last resort - get the DetailPanel from the Canvas 
        Canvas canvas = cardDetailDisplay.GetComponentInParent<Canvas>();
        if (canvas != null)
        {
            Transform canvasDetailPanel = canvas.transform.Find("DetailPanel");
            if (canvasDetailPanel != null)
            {
                return canvasDetailPanel.gameObject;
            }
        }
        
        Debug.LogWarning("Could not locate DetailPanel!");
        return null;
    }

    /// <summary>
    /// Check if the click is on a 3D card
    /// </summary>
    private bool IsClickOn3DCard(Vector3 position)
    {
        Camera activeCamera = FindActiveCamera();
        if (activeCamera == null)
        {
            Debug.LogWarning("No active camera found!");
            return false;
        }

        Ray ray = activeCamera.ScreenPointToRay(position);
        RaycastHit hit;

        // Perform the raycast
        if (Physics.Raycast(ray, out hit, Mathf.Infinity, cardLayerMask))
        {
            // Check if the hit object is a card
            if (hit.collider.GetComponent<UniversalCardVisual3D>() != null ||
                hit.collider.GetComponent<Card3DClickHandler>() != null ||
                hit.collider.GetComponent<ClickableTechnologyCard3D>() != null ||
                hit.collider.GetComponent<WorldCardVisual>() != null) // Add this line for world cards
            {
                return true;
            }
        }

        return false;
    }

    
    /// <summary>
    /// Find the active camera
    /// </summary>
    private Camera FindActiveCamera()
    {
        PlayerCameraController cameraController = GameObject.FindFirstObjectByType<PlayerCameraController>();
        if (cameraController != null)
        {
            return cameraController.GetActiveCamera();
        }
        
        // Fall back to the main camera if no active camera is found
        return mainCamera;
    }
    
    /// <summary>
    /// Check if the click is on a card in the card row
    /// </summary>
    private bool IsClickOnCardRow(Vector3 position)
    {
        // Check if the EventSystem is available
        if (EventSystem.current == null)
            return false;

        // Create a PointerEventData object to simulate the mouse click
        PointerEventData eventData = new PointerEventData(EventSystem.current)
        {
            position = position
        };

        // Perform a raycast to detect UI elements under the mouse position
        var raycastResults = new List<RaycastResult>();
        EventSystem.current.RaycastAll(eventData, raycastResults);

        // Check if any of the raycast results belong to the card row
        foreach (var result in raycastResults)
        {
            if (result.gameObject.name == "CardRowPanel" ||
                result.gameObject.CompareTag("CardRowCard") ||
                result.gameObject.name.Contains("Card_"))
            {
                return true;
            }
            
            // Also check parent objects
            Transform parent = result.gameObject.transform.parent;
            while (parent != null)
            {
                if (parent.name == "CardRowPanel" || 
                    parent.CompareTag("CardRowCard") ||
                    parent.name.Contains("Card_"))
                {
                    return true;
                }
                parent = parent.parent;
            }
        }

        return false;
    }
}