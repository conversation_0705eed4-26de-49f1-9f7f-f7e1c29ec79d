using UnityEngine;

/// <summary>
/// Initializes the TechBuildHandler in the scene
/// </summary>
public class TechBuildHandlerInitializer : MonoBehaviour
{
    [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.AfterSceneLoad)]
    private static void InitializeTechBuildHandler()
    {
        // Force creation of TechBuildHandler instance
        var instance = TechBuildHandler.Instance;
    }

    private void Start()
    {
        // Check if TechBuildHandler already exists
        if (TechBuildHandler.Instance == null)
        {
            // Create a new GameObject for the TechBuildHandler
            GameObject techBuildHandlerObject = new GameObject("TechBuildHandler");
            techBuildHandlerObject.AddComponent<TechBuildHandler>();
        }
    }
}
