using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// Manages the effects of technologies that apply to multiple modules or ships
/// </summary>
public class TechnologyEffectsManager : MonoBehaviour
{
    // Singleton pattern
    public static TechnologyEffectsManager Instance { get; private set; }
    
    [System.Serializable]
    public class TechEffectMapping
    {
        public string technologyName;
        public TechEffectType effectType;
        public float effectValue = 1.0f;
    }
    
    public enum TechEffectType
    {
        PowerBonus,           // Additional power from power sources
        OreIceExtractionBonus, // Additional ore/ice from mining
        NonMetalExtractionBonus, // Additional carbon/silicon from mining
        RareEarthsExtractionBonus, // Additional rare earths from mining
        DeltaVPerFuelBonus,   // Additional delta-v per fuel for ships
        ActionBonus,          // Additional actions per turn
        OtherEffect           // For custom effects
    }
    
    [SerializeField] private List<TechEffectMapping> technologyEffects = new List<TechEffectMapping>();
    
    // Cached values for quick lookup
    private Dictionary<TechEffectType, float> activeEffectValues = new Dictionary<TechEffectType, float>();
    
    private void Awake()
    {
        // Singleton setup
        if (Instance != null && Instance != this)
        {
            Destroy(gameObject);
            return;
        }
        
        Instance = this;
        
        // Initialize with default values
        foreach (TechEffectType effectType in System.Enum.GetValues(typeof(TechEffectType)))
        {
            activeEffectValues[effectType] = 0.0f;
        }
    }
    
    private void Start()
    {
        // Register for player technology events
        GameManager gameManager = GameManager.Instance;
        if (gameManager != null)
        {
            // Setup initial technologies
            UpdateTechnologyEffects();
        }
    }
    
    // Call this when a player acquires a new technology
    public void OnTechnologyAcquired(TechnologyCardData technology)
    {
        // Find and apply any matching effects
        foreach (TechEffectMapping mapping in technologyEffects)
        {
            if (mapping.technologyName == technology.cardName)
            {
                // Add the effect value
                if (activeEffectValues.ContainsKey(mapping.effectType))
                {
                    activeEffectValues[mapping.effectType] += mapping.effectValue;
                    Debug.Log($"Applied tech effect: {mapping.effectType} +{mapping.effectValue} from {technology.cardName}");
                }
            }
        }
    }
    
    // Update effects based on all technologies owned by the current player
    public void UpdateTechnologyEffects()
    {
        // Reset all effects to 0
        foreach (TechEffectType effectType in System.Enum.GetValues(typeof(TechEffectType)))
        {
            activeEffectValues[effectType] = 0.0f;
        }
        
        // Get current player's technologies
        GameManager gameManager = GameManager.Instance;
        if (gameManager == null || gameManager.CurrentPlayer == null)
            return;
            
        Player currentPlayer = gameManager.CurrentPlayer;
        List<global::TechnologyCardData> playerTechs = currentPlayer.GetTechnologies();
        
        // Apply effects from each technology
        foreach (global::TechnologyCardData tech in playerTechs)
        {
            foreach (TechEffectMapping mapping in technologyEffects)
            {
                if (mapping.technologyName == tech.cardName)
                {
                    activeEffectValues[mapping.effectType] += mapping.effectValue;
                    Debug.Log($"Applied tech effect: {mapping.effectType} +{mapping.effectValue} from {tech.cardName}");
                }
            }
        }
        
        // Update player's max actions if that effect is active
        float actionBonus = GetEffectValue(TechEffectType.ActionBonus);
        if (actionBonus > 0)
        {
            int newMaxActions = 4 + Mathf.RoundToInt(actionBonus);
            if (currentPlayer.MaxActions != newMaxActions)
            {
                currentPlayer.MaxActions = newMaxActions;
                Debug.Log($"Updated player's max actions to {newMaxActions}");
                
                // Update UI if needed
                GameUI gameUI = FindFirstObjectByType<GameUI>();
                if (gameUI != null)
                {
                    gameUI.ReinitializeActionIndicators();
                }
            }
        }
    }
    
    // Get the current value of an effect
    public float GetEffectValue(TechEffectType effectType)
    {
        if (activeEffectValues.ContainsKey(effectType))
        {
            return activeEffectValues[effectType];
        }
        
        return 0.0f;
    }
    
    // Get additional power from solar panels
    public int GetAdditionalPower(int basePower)
    {
        return basePower + Mathf.RoundToInt(GetEffectValue(TechEffectType.PowerBonus));
    }
    
    // Get additional ore/ice from mining
    public int GetAdditionalOreIce(int baseAmount)
    {
        return baseAmount + Mathf.RoundToInt(GetEffectValue(TechEffectType.OreIceExtractionBonus));
    }
    
    // Get additional carbon/silicon from mining
    public int GetAdditionalNonMetal(int baseAmount)
    {
        return baseAmount + Mathf.RoundToInt(GetEffectValue(TechEffectType.NonMetalExtractionBonus));
    }
    
    // Get additional rare earths from mining
    public int GetAdditionalRareEarths(int baseAmount)
    {
        return baseAmount + Mathf.RoundToInt(GetEffectValue(TechEffectType.RareEarthsExtractionBonus));
    }
    
    // Get additional delta-v per fuel for ships
    public float GetAdditionalDeltaVPerFuel(float baseValue)
    {
        return baseValue + GetEffectValue(TechEffectType.DeltaVPerFuelBonus);
    }
    
    // Add a technology effect mapping at runtime
    public void AddTechnologyEffect(string techName, TechEffectType effectType, float value)
    {
        TechEffectMapping mapping = new TechEffectMapping
        {
            technologyName = techName,
            effectType = effectType,
            effectValue = value
        };
        
        technologyEffects.Add(mapping);
        Debug.Log($"Added technology effect mapping: {techName} -> {effectType} ({value})");
    }
    
    // Initialize default technology effects
    public void InitializeDefaultEffects()
    {
        // Clear existing mappings
        technologyEffects.Clear();
        
        // Add default mappings for Tier 0 and Tier 1 technologies
        
        // Tier 1 technology effects
        AddTechnologyEffect("Advanced Photovoltaics", TechEffectType.PowerBonus, 1.0f);
        AddTechnologyEffect("Advanced Ore/Ice Extraction", TechEffectType.OreIceExtractionBonus, 1.0f);
        AddTechnologyEffect("Advanced Non-metal Extraction", TechEffectType.NonMetalExtractionBonus, 1.0f);
        AddTechnologyEffect("Advanced Rare Earths Extraction", TechEffectType.RareEarthsExtractionBonus, 1.0f);
        AddTechnologyEffect("Advanced Ion Propulsion", TechEffectType.DeltaVPerFuelBonus, 2.0f);
        AddTechnologyEffect("AI Logistics", TechEffectType.ActionBonus, 1.0f);
        
        Debug.Log("Initialized default technology effects");
    }
}