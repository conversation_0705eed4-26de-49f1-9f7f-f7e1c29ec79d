using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// Undoable action for building structures (modules/ships)
/// </summary>
public class BuildStructureAction : UndoableAction
{
    private CardData cardData;
    private List<Resource> buildCost;
    private int playerId;
    private GameObject location;
    private bool isShip;
    private string structureName;

    public BuildStructureAction(CardData cardData, List<Resource> buildCost, int playerId, GameObject location, bool isShip)
    {
        this.cardData = cardData;
        this.buildCost = new List<Resource>(buildCost);
        this.playerId = playerId;
        this.location = location;
        this.isShip = isShip;
        this.structureName = cardData.Name;
    }

    public override void Undo()
    {
        GameManager gameManager = GameManager.Instance;
        if (gameManager == null || playerId >= gameManager.Players.Count)
            return;

        Player player = gameManager.Players[playerId];
        if (player == null)
            return;

        // Refund resources
        foreach (Resource resource in buildCost)
        {
            player.AddResource(location, resource.Type, resource.Amount);
        }

        // Remove the built structure
        if (isShip)
        {
            // Remove ship from player's fleet
            if (player.RemoveShip(structureName, location))
            {
                player.ShipsInPlay--;

                // Unregister from planet
                PlanetBody planetBody = location.GetComponent<PlanetBody>();
                if (planetBody != null)
                {
                    // Find the ship to unregister
                    List<Ship> ships = player.GetShips();
                    foreach (Ship ship in ships)
                    {
                        if (ship.Name == structureName && ship.CurrentLocation == location)
                        {
                            planetBody.RegisterShipDeparture(ship, player);
                            break;
                        }
                    }
                }
            }
        }
        else
        {
            // Remove module from player's modules
            player.RemoveModule(location, structureName);
        }

        // Refund the action
        if (gameManager.CurrentPlayerIndex == playerId)
        {
            gameManager.RefundAction();
        }

        GameLogManager logManager = GameLogManager.Instance;
        if (logManager != null)
        {
            logManager.RemoveLastLog();
        }

        // Update UI
        PlayAreaManager playAreaManager = PlayAreaManager.Instance;
        if (playAreaManager != null)
        {
            playAreaManager.RefreshPlayerPlayArea(playerId);
        }

        GameUI gameUI = UnityEngine.Object.FindFirstObjectByType<GameUI>();
        if (gameUI != null)
        {
            gameUI.RefreshUI();
        }
    }
    
    public override void Redo()
    {
        GameManager gameManager = GameManager.Instance;
        if (gameManager == null || playerId >= gameManager.Players.Count)
            return;

        Player player = gameManager.Players[playerId];
        if (player == null)
            return;

        // Use action
        if (gameManager.CurrentPlayerIndex == playerId)
        {
            gameManager.SetActionsRemaining(gameManager.ActionsRemaining - 1);
        }

        // Deduct resources
        foreach (Resource resource in buildCost)
        {
            player.UseResource(location, resource.Type, resource.Amount);
        }

        // Recreate the structure
        if (isShip)
        {
            Ship ship = new Ship
            {
                Name = structureName,
                CurrentLocation = location,
                DeltaVPerFuel = GetFloatFromParams(cardData, "deltaVPerFuel", 4f),
                CargoCapacity = GetIntFromParams(cardData, "cargoCapacity", 1),
                Strength = GetIntFromParams(cardData, "shipStrength", 0),
                IsConsumedOnSurvey = GetBoolFromParams(cardData, "consumedOnSurvey", false)
            };

            player.AddShip(ship);
            player.ShipsInPlay++;

            PlanetBody planetBody = location.GetComponent<PlanetBody>();
            if (planetBody != null)
            {
                planetBody.RegisterShipArrival(ship, player);
            }
        }
        else
        {
            Module module = new Module
            {
                Name = structureName,
                Type = GetModuleTypeFromSubType(cardData.SubType),
                PowerOutput = cardData.PowerOutput,
                PowerRequired = cardData.PowerRequired,
                IsWonder = cardData.IsWonder || cardData.Type?.ToLower().Contains("wonder") == true,
                VictoryPointValue = GetVPFromEffect(cardData.Effect),
                ProcessorDescription = cardData.Effect,
                Tier = cardData.Tier
            };

            player.AddModule(location, module);
        }

        GameLogManager logManager = GameLogManager.Instance;
        if (logManager != null)
        {
            string type = isShip ? "ship" : "module";
            logManager.AddLog($"Player {playerId + 1} built {structureName} ({type})");
        }

        // Update UI
        PlayAreaManager playAreaManager = PlayAreaManager.Instance;
        if (playAreaManager != null)
        {
            playAreaManager.RefreshPlayerPlayArea(playerId);
        }

        GameUI gameUI = UnityEngine.Object.FindFirstObjectByType<GameUI>();
        if (gameUI != null)
        {
            gameUI.RefreshUI();
        }
    }

    private float GetFloatFromParams(CardData cardData, string key, float defaultValue)
    {
        if (cardData.EffectParams != null && cardData.EffectParams.ContainsKey(key))
        {
            return System.Convert.ToSingle(cardData.EffectParams[key]);
        }
        return defaultValue;
    }
    
    private int GetIntFromParams(CardData cardData, string key, int defaultValue)
    {
        if (cardData.EffectParams != null && cardData.EffectParams.ContainsKey(key))
        {
            return System.Convert.ToInt32(cardData.EffectParams[key]);
        }
        return defaultValue;
    }
    
    private bool GetBoolFromParams(CardData cardData, string key, bool defaultValue)
    {
        if (cardData.EffectParams != null && cardData.EffectParams.ContainsKey(key))
        {
            return System.Convert.ToBoolean(cardData.EffectParams[key]);
        }
        return defaultValue;
    }
    
    private ModuleType GetModuleTypeFromSubType(string subType)
    {
        if (string.IsNullOrEmpty(subType))
            return ModuleType.Other;
            
        string lower = subType.ToLower();
        if (lower.Contains("power"))
            return ModuleType.Power;
        if (lower.Contains("extractor"))
            return ModuleType.Extractor;
        if (lower.Contains("processor"))
            return ModuleType.Processor;
        if (lower.Contains("wonder"))
            return ModuleType.Wonder;
            
        return ModuleType.Other;
    }
    
    private int GetVPFromEffect(string effect)
    {
        if (string.IsNullOrEmpty(effect))
            return 0;
            
        System.Text.RegularExpressions.Match match = 
            System.Text.RegularExpressions.Regex.Match(effect, @"(\d+)\s*(?:VP|Victory\s*Points?)", 
                System.Text.RegularExpressions.RegexOptions.IgnoreCase);
                
        if (match.Success)
        {
            return int.Parse(match.Groups[1].Value);
        }
        
        return 0;
    }
}