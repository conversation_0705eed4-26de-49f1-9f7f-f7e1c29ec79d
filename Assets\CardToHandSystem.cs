using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using System.Collections;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using TMPro;

/// <summary>
/// All-in-one system to handle taking cards from row to hand with animation
/// </summary>
public class CardToHandSystem : MonoBehaviour
{
    [Header("References")]
    [SerializeField] private GameObject detailPanel; 
    [SerializeField] private Button takeCardButton;  
    [SerializeField] private CardDetailDisplay cardDetailDisplay;
    [SerializeField] private Canvas mainCanvas;
    [SerializeField] private TechnologyDeckManager deckManager;
    [SerializeField] private CardRowUI cardRowUI;
    [SerializeField] private bool enableDebugLogging = false; // Reduced logging for performance
   
    [Header("Hand Area")]
    [SerializeField] private Transform handAreaParent;
    [SerializeField] private GameObject cardUIPrefab;
    [SerializeField] private int maxCardsInHand = 15; // Limit cards for performance
   
    [Header("Animation")]
    [SerializeField] private float animationDuration = 0.15f; // Faster animation
    [SerializeField] private AnimationCurve animationCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
    [SerializeField] private bool enableAnimation = true; // Option to disable animation

    // Preloaded resources for performance
    private Dictionary<string, Sprite> cardSprites = new Dictionary<string, Sprite>();
    private Queue<GameObject> cardUIPool = new Queue<GameObject>(); // Object pool for UI cards
   
    // Cache common references
    private Camera mainCamera;
   
    // Track the current card information - set directly by CardDetailDisplay
    private CardData currentCardData;
    private int currentCardIndex = -1;
    private GameObject currentSourceObject;
   
    // List of cards in hand
    private List<CardData> cardsInHand = new List<CardData>();
    private List<GameObject> cardUIElements = new List<GameObject>();
   
    private Coroutine currentAnimation;
    private float lastScreenWidth = 0f;
    private bool isInitialized = false;
   
    // Singleton pattern for easy access
    public static CardToHandSystem Instance { get; private set; }
   
    private void Awake()
    {
        // Singleton setup
        if (Instance != null && Instance != this)
        {
            Destroy(gameObject);
            return;
        }
        Instance = this;
       
        mainCamera = Camera.main;
       
        // Get manager references if not set
        if (deckManager == null)
            deckManager = TechnologyDeckManager.Instance;
            
        // Initialize object pool for UI cards
        InitializeCardPool(10); // Pre-create 10 card objects
    }
   
    private void Start()
    {
        FindMissingReferences();
        
        // Override any existing listeners on the button
        if (takeCardButton != null)
        {
            takeCardButton.onClick.RemoveAllListeners();
            takeCardButton.onClick.AddListener(OnTakeCardButtonClicked);
            DebugLog("CardToHandSystem initialized with button: " + takeCardButton.name);
        }
        else
        {
            Debug.LogWarning("Take Card button not assigned or found!");
        }
        
        // Preload card textures when the game starts
        StartCoroutine(PreloadCardTextures());
        
        isInitialized = true;
    }
    
    private void FindMissingReferences()
    {
        // Find references if not assigned in inspector
        if (detailPanel == null && cardDetailDisplay != null)
        {
            detailPanel = cardDetailDisplay.gameObject;
            foreach (Transform child in cardDetailDisplay.transform)
            {
                if (child.name.Contains("Panel") || child.name.Contains("Detail"))
                {
                    detailPanel = child.gameObject;
                    break;
                }
            }
        }
       
        // Find Canvas if not assigned
        if (mainCanvas == null)
        {
            // Try to get the Canvas from parent hierarchy
            mainCanvas = GetComponentInParent<Canvas>();
            
            // If still not found, try to find in the scene
            if (mainCanvas == null)
            {
                Canvas[] canvases = FindObjectsByType<Canvas>(FindObjectsSortMode.None);
                foreach (Canvas canvas in canvases)
                {
                    if (canvas.renderMode == RenderMode.ScreenSpaceOverlay || 
                        canvas.renderMode == RenderMode.ScreenSpaceCamera)
                    {
                        mainCanvas = canvas;
                        break;
                    }
                }
            }
        }
       
        // Find the Take Card button if not assigned
        if (takeCardButton == null && detailPanel != null)
        {
            // Look for a button with "Take" in its name or text
            Button[] buttons = detailPanel.GetComponentsInChildren<Button>(true);
            foreach (Button button in buttons)
            {
                // Check button name first
                if (button.name.Contains("Take") || button.name.Contains("primary"))
                {
                    takeCardButton = button;
                    break;
                }
               
                // Check button text
                TextMeshProUGUI buttonText = button.GetComponentInChildren<TextMeshProUGUI>();
                if (buttonText != null && buttonText.text.Contains("Take"))
                {
                    takeCardButton = button;
                    break;
                }
            }
        }
        
        // Find CardRowUI if not assigned
        if (cardRowUI == null)
        {
            cardRowUI = FindFirstObjectByType<CardRowUI>(FindObjectsInactive.Include);
        }
    }
    
    private void InitializeCardPool(int initialCount)
    {
        if (cardUIPrefab == null || handAreaParent == null) return;
        
        // Create a parent for pooled objects
        GameObject poolParent = new GameObject("CardPool");
        poolParent.transform.SetParent(transform);
        poolParent.SetActive(false);
        
        // Pre-create card objects
        for (int i = 0; i < initialCount; i++)
        {
            GameObject card = Instantiate(cardUIPrefab, poolParent.transform);
            card.SetActive(false);
            cardUIPool.Enqueue(card);
        }
    }
    
    // Get a card UI from the pool or create a new one
    private GameObject GetCardFromPool()
    {
        if (cardUIPool.Count > 0)
        {
            GameObject card = cardUIPool.Dequeue();
            card.SetActive(true);
            return card;
        }
        
        // Create a new card if pool is empty
        return Instantiate(cardUIPrefab);
    }
    
    // Return a card to the pool
    private void ReturnCardToPool(GameObject card)
    {
        if (card == null) return;
        
        // Reset the card
        card.transform.SetParent(transform);
        card.SetActive(false);
        
        // Remove all listeners from buttons
        Button button = card.GetComponent<Button>();
        if (button != null)
        {
            button.onClick.RemoveAllListeners();
        }
        
        // Add back to pool
        cardUIPool.Enqueue(card);
    }
   
    private IEnumerator PreloadCardTextures()
    {
        yield return new WaitForEndOfFrame(); // Wait for everything to be initialized
        
        DebugLog("Beginning texture preload...");
        int preloadedCount = 0;
        
        // First preload the card row
        if (deckManager != null)
        {
            for (int i = 0; i < deckManager.GetCardRowSize(); i++)
            {
                CardData cardData = deckManager.GetCardAtIndex(i);
                if (cardData != null)
                {
                    // Load each texture asynchronously with a small delay
                    bool loaded = LoadCardTexture(cardData);
                    if (loaded) preloadedCount++;
                    
                    // Yield every few textures to spread the load
                    if (i % 3 == 0) yield return null;
                }
            }
        }
        
        DebugLog($"Preloaded {preloadedCount} card textures");
    }
    
    // Helper method to load a card texture
    private bool LoadCardTexture(CardData cardData)
    {
        if (cardData == null) return false;
        
        // Skip if already loaded
        if (cardSprites.ContainsKey(cardData.Name)) return true;
        
        string cardNameFormatted = cardData.Name.ToLower().Replace(" ", "");
        string imagePath = $"Cards/{cardData.Tier}{cardNameFormatted}";
        Texture2D cardTexture = Resources.Load<Texture2D>(imagePath);
        
        if (cardTexture != null)
        {
            cardSprites[cardData.Name] = Sprite.Create(
                cardTexture,
                new Rect(0, 0, cardTexture.width, cardTexture.height),
                new Vector2(0.5f, 0.5f)
            );
            return true;
        }
        
        return false;
    }
   
    // Debug helper method
    private void DebugLog(string message)
    {
        if (enableDebugLogging)
        {
            Debug.Log("[CardToHandSystem] " + message);
        }
    }

    // Add these methods to CardToHandSystem.cs

    /// <summary>
    /// Set up card glow effect for hand cards
    /// </summary>
    private void SetupCardGlowEffect(GameObject cardUI, CardData cardData)
    {
        // Add hover effects to the card
        EventTrigger eventTrigger = cardUI.GetComponent<EventTrigger>();
        if (eventTrigger == null)
        {
            eventTrigger = cardUI.AddComponent<EventTrigger>();
        }

        // Clear any existing triggers
        eventTrigger.triggers.Clear();

        // Add pointer enter event
        EventTrigger.Entry enterEntry = new EventTrigger.Entry();
        enterEntry.eventID = EventTriggerType.PointerEnter;
        enterEntry.callback.AddListener((data) => OnCardHoverEnter(cardUI));
        eventTrigger.triggers.Add(enterEntry);

        // Add pointer exit event
        EventTrigger.Entry exitEntry = new EventTrigger.Entry();
        exitEntry.eventID = EventTriggerType.PointerExit;
        exitEntry.callback.AddListener((data) => OnCardHoverExit(cardUI));
        eventTrigger.triggers.Add(exitEntry);

        // Add this UI card to the action checker system to control when it should glow
        CardActionChecker actionChecker = cardUI.GetComponent<CardActionChecker>();
        if (actionChecker == null)
        {
            actionChecker = cardUI.AddComponent<CardActionChecker>();
            actionChecker.Initialize(cardData);
        }
    }


    /// <summary>
    /// Handle hover enter for hand card glow effect
    /// </summary>
    private void OnCardHoverEnter(GameObject cardUI)
    {
        CardActionChecker actionChecker = cardUI.GetComponent<CardActionChecker>();

        // For hand cards, we always want them to glow on hover, regardless of action checker

        // Apply glow effect by brightening the image
        Image cardImage = cardUI.transform.Find("CardImage")?.GetComponent<Image>();
        if (cardImage != null)
        {
            // Store original color in a component for restoration
            CardOriginalColor colorComponent = cardUI.GetComponent<CardOriginalColor>();
            if (colorComponent == null)
            {
                colorComponent = cardUI.AddComponent<CardOriginalColor>();
                colorComponent.OriginalColor = cardImage.color;
            }

            // Apply brighter color
            cardImage.color = new Color(1f, 1f, 1f, 1f); // Full bright
        }

        // Scale up slightly
        cardUI.transform.localScale = Vector3.one * 1.1f;
    }


    /// <summary>
    /// Handle hover exit for hand card glow effect
    /// </summary>
    private void OnCardHoverExit(GameObject cardUI)
    {
        // Reset image color
        Image cardImage = cardUI.transform.Find("CardImage")?.GetComponent<Image>();
        if (cardImage != null)
        {
            // Restore original color if we have it
            CardOriginalColor colorComponent = cardUI.GetComponent<CardOriginalColor>();
            if (colorComponent != null)
            {
                cardImage.color = colorComponent.OriginalColor;
            }
            else
            {
                cardImage.color = Color.white;
            }
        }

        // Reset scale
        cardUI.transform.localScale = Vector3.one;
    }


    /// <summary>
    /// Helper component to store original card color
    /// </summary>
    private class CardOriginalColor : MonoBehaviour
    {
        public Color OriginalColor = Color.white;
    }


    // This method is called directly by CardDetailDisplay when showing a card
    public void SetCardData(CardData cardData, int cardIndex, GameObject sourceObject)
    {
        this.currentCardData = cardData;
        this.currentCardIndex = cardIndex;
        this.currentSourceObject = sourceObject;

        DebugLog($"Received card data: {cardData?.Name}, index: {cardIndex}, source object: {(sourceObject != null ? sourceObject.name : "null")}");

        // Preload this card's texture immediately if not already loaded
        if (cardData != null && !cardSprites.ContainsKey(cardData.Name))
        {
            LoadCardTexture(cardData);
        }
    }
   
    public void OnTakeCardButtonClicked()
    {
        DebugLog("Take Card button clicked");

        // Validate data
        if (currentCardData == null)
        {
            Debug.LogWarning("No card data available!");
            return;
        }
        
        // Get the action cost based on card position
        int actionCost = 1; // Default cost
        if (deckManager != null && currentCardIndex >= 0)
        {
            actionCost = deckManager.GetCardCost(currentCardIndex);
        }
        
        // Check if player has enough actions
        GameManager gameManager = GameManager.Instance;
        if (gameManager != null)
        {
            if (gameManager.ActionsRemaining < actionCost)
            {
                Debug.LogWarning($"Not enough actions! Need {actionCost}, have {gameManager.ActionsRemaining}");
                return;
            }
            
            // Create the undoable action BEFORE consuming actions
            TakeCardAction takeCardAction = new TakeCardAction(
                currentCardData, 
                currentCardIndex, 
                currentSourceObject,
                actionCost // Pass the action cost to the TakeCardAction
            );
            
            // Use the required number of actions WITHOUT registering individual undo actions
            // This is the key fix - we need to directly modify the actions remaining without
            // letting GameManager register its own undo actions
            bool canTakeAction = true;
            for (int i = 0; i < actionCost; i++)
            {
                if (gameManager.ActionsRemaining <= 0)
                {
                    canTakeAction = false;
                    break;
                }
                // Directly reduce actions without registering separate undo actions
                gameManager.SetActionsRemaining(gameManager.ActionsRemaining - 1);
            }
            
            if (!canTakeAction)
            {
                Debug.LogWarning("Failed to consume all required actions!");
                return;
            }
            
            // Now register our single comprehensive undo action
            UndoManager undoManager = UndoManager.Instance;
            if (undoManager != null)
            {
                undoManager.RegisterAction(takeCardAction);
            }
        }
        else
        {
            Debug.LogWarning("GameManager not found! Cannot consume actions.");
            return;
        }

        // Tell the 3D deck manager to mark the card as taken
        if (deckManager != null && currentCardIndex >= 0)
        {
            deckManager.MarkCardAsTaken(currentCardIndex, currentCardData);
        }

        // Hide the panel immediately
        if (cardDetailDisplay != null)
        {
            cardDetailDisplay.Hide();
        }
        else if (detailPanel != null)
        {
            detailPanel.SetActive(false);
        }
        
        GameLogManager logManager = GameLogManager.Instance;
        if (logManager != null)
        {
            logManager.AddLog($"Player {gameManager.CurrentPlayerIndex + 1} took {currentCardData.Name}");
        }


        // Defer the rest of the card processing to avoid lag
        StartCoroutine(DeferredCardToHandProcess());
    }
    
    /// <summary>
    /// Remove a card from hand (for undo functionality)
    /// </summary>
    public void RemoveCardFromHand(CardData cardData)
    {
        if (cardData == null) return;
        
        // Find the card in the hand
        int cardIndex = -1;
        for (int i = 0; i < cardsInHand.Count; i++)
        {
            if (cardsInHand[i].Name == cardData.Name)
            {
                cardIndex = i;
                break;
            }
        }
        
        if (cardIndex >= 0 && cardIndex < cardsInHand.Count)
        {
            // Remove the card from data list
            cardsInHand.RemoveAt(cardIndex);
            
            // Remove the UI element
            if (cardIndex < cardUIElements.Count)
            {
                GameObject cardUI = cardUIElements[cardIndex];
                cardUIElements.RemoveAt(cardIndex);
                
                // Return to pool instead of destroying
                ReturnCardToPool(cardUI);
                
                // Reposition remaining cards
                RepositionHandCards();
            }
        }
    }

    private IEnumerator DeferredCardToHandProcess()
    {
        // Wait a frame to let other UI updates happen first
        yield return null;
        
        // Update UI to show correct action count
        GameUI gameUI = FindFirstObjectByType<GameUI>();
        if (gameUI != null)
        {
            gameUI.RefreshUI();
        }
        
        // Find source card - improved source card detection
        GameObject sourceCard = null;
        
        // First, check if currentSourceObject is pointing to a card or a container
        if (currentSourceObject != null)
        {
            // Check if it's a UI card directly
            if (currentSourceObject.name.Contains("CardUI_") || currentSourceObject.name.Contains("Card_"))
            {
                sourceCard = currentSourceObject;
                DebugLog($"Using current source object directly: {sourceCard.name}");
            }
            // If it's a container, look for child cards
            else if (currentSourceObject.name.Contains("Container") || currentSourceObject.name.Contains("Row"))
            {
                // Find card element matching our index
                if (currentCardIndex >= 0)
                {
                    // Look for a child with CardIndexTracker or with a name containing the card index
                    foreach (Transform child in currentSourceObject.transform)
                    {
                        CardIndexTracker tracker = child.GetComponent<CardIndexTracker>();
                        if (tracker != null && tracker.CardIndex == currentCardIndex)
                        {
                            sourceCard = child.gameObject;
                            DebugLog($"Found source card in container by index tracker: {sourceCard.name}");
                            break;
                        }
                        
                        if (child.name.Contains($"_{currentCardIndex}_") || 
                            child.name.EndsWith($"_{currentCardIndex}") ||
                            Regex.IsMatch(child.name, $@"_{currentCardIndex}(\D|$)"))
                        {
                            sourceCard = child.gameObject;
                            DebugLog($"Found source card in container by name: {sourceCard.name}");
                            break;
                        }
                    }
                }
            }
        }
    
        // If we still don't have a source card but have an index, try to find it via the UI or 3D manager
        if (sourceCard == null && currentCardIndex >= 0)
        {
            // Try from card row UI first
            if (cardRowUI != null)
            {
                // Look through card UI elements in CardRowUI
                Transform cardRowContainer = cardRowUI.transform.Find("CardRowContainer");
                if (cardRowContainer != null)
                {
                    foreach (Transform child in cardRowContainer)
                    {
                        CardIndexTracker tracker = child.GetComponent<CardIndexTracker>();
                        if (tracker != null && tracker.CardIndex == currentCardIndex)
                        {
                            sourceCard = child.gameObject;
                            DebugLog($"Found source card in CardRowUI: {sourceCard.name}");
                            break;
                        }
                    }
                }
            }
            
            // If still not found, try from deck manager
            if (sourceCard == null && deckManager != null)
            {
                sourceCard = deckManager.GetCardObject(currentCardIndex);
                if (sourceCard != null)
                {
                    DebugLog($"Found source card from deckManager: {sourceCard.name}");
                }
            }
        }
    
        if (sourceCard != null)
        {
            DebugLog($"Found source card: {sourceCard.name} for animation");
        
            // Make the source card invisible first (for 3D cards)
            if (sourceCard.GetComponent<UniversalCardVisual3D>() != null ||
                sourceCard.GetComponent<Card3DClickHandler>() != null)
            {
                MakeCardInvisible(sourceCard);
            }
            
            // Now handle animation if enabled
            if (enableAnimation)
            {
                if (currentAnimation != null)
                    StopCoroutine(currentAnimation);
                
                // Create a temporary copy for the animation - with proper parent handling
                GameObject animCard = Instantiate(sourceCard);
                
                // Make sure we're not creating a child of an inactive parent
                if (sourceCard.transform.parent != null && !sourceCard.transform.parent.gameObject.activeInHierarchy)
                {
                    animCard.transform.SetParent(mainCanvas.transform, false);
                }
                else
                {
                    animCard.transform.SetParent(sourceCard.transform.parent, false);
                }
                
                // Set position and scale to match original
                animCard.transform.position = sourceCard.transform.position;
                animCard.transform.rotation = sourceCard.transform.rotation;
                animCard.transform.localScale = sourceCard.transform.localScale;
                
                // Make sure it's visible and enabled
                animCard.SetActive(true);
                
                // Ensure UI components are visible
                Image[] images = animCard.GetComponentsInChildren<Image>(true);
                foreach (var img in images)
                {
                    Color c = img.color;
                    c.a = 1f;
                    img.color = c;
                    img.enabled = true;
                }
                
                currentAnimation = StartCoroutine(AnimateCardToHand(animCard, currentCardData));
            }
            else
            {
                // Skip animation and add directly to hand
                AddCardToHand(currentCardData);
            }
        }
        else
        {
            DebugLog("Could not find source card - adding to hand without animation");
        
            // Add directly to hand
            AddCardToHand(currentCardData);
        }
    
        // Update CardRowUI after a short delay
        yield return new WaitForSeconds(0.05f);
        UpdateCardRowUI(currentCardIndex);
    }
   
    // Update CardRowUI - now using direct reference instead of FindObjectOfType
    private void UpdateCardRowUI(int cardIndex)
    {
        if (cardRowUI != null)
        {
            cardRowUI.MarkCardAsTaken(cardIndex);
        }
        else
        {
            DebugLog("CardRowUI reference is missing");
        }
    }
   
    /// <summary>
    /// Makes a card invisible by disabling renderers or setting alpha to 0
    /// Works for both 3D and UI cards
    /// </summary>
    private void MakeCardInvisible(GameObject cardObject)
    {
        if (cardObject == null) return;
       
        // For 3D cards with Renderers
        Renderer[] renderers = cardObject.GetComponentsInChildren<Renderer>();
        foreach (Renderer renderer in renderers)
        {
            renderer.enabled = false;
        }
       
        // For UI cards with Images
        Image[] images = cardObject.GetComponentsInChildren<Image>();
        foreach (Image image in images)
        {
            Color color = image.color;
            color.a = 0f;
            image.color = color;
        }
       
        // For UI cards with Text
        TextMeshProUGUI[] texts = cardObject.GetComponentsInChildren<TextMeshProUGUI>();
        foreach (TextMeshProUGUI text in texts)
        {
            Color color = text.color;
            color.a = 0f;
            text.color = color;
        }
       
        // Disable any colliders to prevent further interaction
        Collider[] colliders = cardObject.GetComponentsInChildren<Collider>();
        foreach (Collider collider in colliders)
        {
            collider.enabled = false;
        }
       
        // Disable any button interaction
        Button button = cardObject.GetComponent<Button>();
        if (button != null)
        {
            button.interactable = false;
        }
       
        // Add a marker component instead of using a tag
        if (!cardObject.GetComponent<CardTakenMarker>())
        {
            cardObject.AddComponent<CardTakenMarker>();
        }
    }
   
    // Find a card by index from the deck manager
    private GameObject FindCardInRow(int cardIndex)
    {
        if (cardIndex < 0) return null;
       
        // Try to get card directly from the deckManager
        if (deckManager != null)
        {
            GameObject cardObj = deckManager.GetCardObject(cardIndex);
            if (cardObj != null)
            {
                return cardObj;
            }
        }
       
        return null;
    }
   
    private IEnumerator AnimateCardToHand(GameObject animCard, CardData cardData)
    {
        // Skip animation if we don't have what we need
        if (mainCanvas == null || cardData == null)
        {
            Destroy(animCard);
            AddCardToHand(cardData);
            yield break;
        }
        
        // Create a simpler UI object for animation
        GameObject uiAnimCard = new GameObject("UIAnimCard");
        uiAnimCard.transform.SetParent(mainCanvas.transform, false);
        RectTransform rectTransform = uiAnimCard.AddComponent<RectTransform>();
        Image image = uiAnimCard.AddComponent<Image>();
       
        // Set card size
        rectTransform.sizeDelta = new Vector2(150f, 210f);
        
        // Set starting position - improved position detection
        Vector3 startPosition;
        
        // Different handling based on card type
        RectTransform rectSource = animCard.GetComponent<RectTransform>();
        if (rectSource != null)
        {
            // It's a UI card
            // Get the world position of the card's center
            Canvas sourceCanvas = animCard.GetComponentInParent<Canvas>();
            if (sourceCanvas != null && sourceCanvas.renderMode == RenderMode.ScreenSpaceOverlay)
            {
                // For overlay canvas, we can use the rect position directly
                startPosition = rectSource.position;
            }
            else
            {
                // For world space or camera space canvas, we need to convert to screen position
                if (mainCamera == null) mainCamera = Camera.main;
                
                // Use corners to get accurate bounds
                Vector3[] corners = new Vector3[4];
                rectSource.GetWorldCorners(corners);
                
                // Calculate center from corners
                Vector3 center = (corners[0] + corners[1] + corners[2] + corners[3]) / 4f;
                startPosition = mainCamera.WorldToScreenPoint(center);
            }
            
            // Try to copy its appearance
            Image sourceImage = animCard.GetComponent<Image>();
            if (sourceImage != null && sourceImage.sprite != null)
            {
                image.sprite = sourceImage.sprite;
            }
            else
            {
                // Try to find an image in children
                Image[] childImages = animCard.GetComponentsInChildren<Image>();
                foreach (var childImg in childImages)
                {
                    if (childImg.sprite != null)
                    {
                        image.sprite = childImg.sprite;
                        break;
                    }
                }
            }
        }
        else
        {
            // It's a 3D card
            if (mainCamera == null) mainCamera = Camera.main;
            // Get the center of the card in world space
            Renderer renderer = animCard.GetComponent<Renderer>();
            if (renderer != null)
            {
                startPosition = mainCamera.WorldToScreenPoint(renderer.bounds.center);
            }
            else
            {
                // Fallback to transform position
                startPosition = mainCamera.WorldToScreenPoint(animCard.transform.position);
            }
        }
       
        // Clean up original animation card object early
        Destroy(animCard);
        
        // Apply the sprite from our preloaded dictionary if possible
        if (image.sprite == null && cardSprites.TryGetValue(cardData.Name, out Sprite sprite))
        {
            image.sprite = sprite;
        }
        
        // If we still don't have a sprite, use a placeholder color
        if (image.sprite == null)
        {
            image.color = new Color(0.8f, 0.8f, 1f);
        }
        
        // Position the animation card at the starting position
        rectTransform.position = startPosition;

        // Target position in bottom area - adjust for different screen sizes
        Vector2 targetPosition = new Vector2(Screen.width / 2f, 50f + (Screen.height * 0.1f));
        
        // Simplified animation
        float elapsed = 0f;
        
        while (elapsed < animationDuration)
        {
            float t = animationCurve.Evaluate(elapsed / animationDuration);
            
            // Simple linear motion with a slight arc
            Vector2 position = Vector2.Lerp(startPosition, targetPosition, t);
            position.y += Mathf.Sin(t * Mathf.PI) * 100f; // Simple arc with sin function
            
            // Apply position
            rectTransform.position = position;

            elapsed += Time.deltaTime;
            yield return null;
        }
       
        // Ensure final position
        rectTransform.position = targetPosition;
       
        // Cleanup and add to hand
        Destroy(uiAnimCard);
        AddCardToHand(currentCardData);
       
        currentAnimation = null;
    }
   
    /// <summary>
    /// Add a card to the hand
    /// </summary>
    public void AddCardToHand(CardData cardData)
    {
        if (cardData == null || handAreaParent == null)
            return;
                
        // Add to list - but enforce a maximum for performance
        if (cardsInHand.Count >= maxCardsInHand)
        {
            // Remove the oldest card if we hit the limit
            if (cardUIElements.Count > 0)
            {
                GameObject oldestCard = cardUIElements[0];
                cardUIElements.RemoveAt(0);
                cardsInHand.RemoveAt(0);
                ReturnCardToPool(oldestCard);
            }
        }
        
        cardsInHand.Add(cardData);
    
        // Get card UI from pool
        GameObject cardUI = GetCardFromPool();
        cardUI.transform.SetParent(handAreaParent, false);
        cardUI.name = $"HandCard_{cardData.Name}";
    
        // Set up visual (much simpler now with preloaded sprites)
        Image cardImage = cardUI.transform.Find("CardImage")?.GetComponent<Image>();
        if (cardImage != null)
        {
            // Use preloaded sprite if available
            if (cardSprites.TryGetValue(cardData.Name, out Sprite sprite))
            {
                cardImage.sprite = sprite;
            }
            else
            {
                // Fall back to loading if needed
                LoadCardTexture(cardData);
                if (cardSprites.TryGetValue(cardData.Name, out sprite))
                {
                    cardImage.sprite = sprite;
                }
            }
            
            // Make sure the image is visible with full opacity
            Color color = cardImage.color;
            color.a = 1f;
            cardImage.color = color;
        }
    
        // Add click handler
        Button button = cardUI.GetComponent<Button>();
        if (button != null)
        {
            button.onClick.RemoveAllListeners();
            button.onClick.AddListener(() => OnHandCardClicked(cardData));
            button.interactable = true;
        }
        
        SetupCardGlowEffect(cardUI, cardData);

        // Reset any size changes and configure for layout group
        ConfigureCardForLayoutGroup(cardUI);
        
        // Make sure the card is active and visible
        cardUI.SetActive(true);
    
        // Add to list
        cardUIElements.Add(cardUI);
    
        // Reposition all cards - but not on every frame for performance
        if (Time.frameCount % 2 == 0)
        {
            RepositionHandCards();
        }
        else
        {
            // Schedule repositioning for next frame
            StartCoroutine(DelayedRepositioning());
        }
    }
    
    private IEnumerator DelayedRepositioning()
    {
        yield return null;
        RepositionHandCards();
    }

    /// <summary>
    /// Reposition all cards in the hand
    /// </summary>
    private void RepositionHandCards()
    {
        if (cardUIElements.Count == 0 || handAreaParent == null)
            return;

        // Remove any existing HorizontalLayoutGroup
        HorizontalLayoutGroup existingLayout = handAreaParent.GetComponent<HorizontalLayoutGroup>();
        if (existingLayout != null)
        {
            Destroy(existingLayout);
        }

        int cardCount = cardUIElements.Count;
        float cardWidth = 130f;
        float maxRotationAngle = 8f; // Maximum rotation angle for edge cards
        float overlap = 0.25f; // Cards overlap (higher = more overlap)

        // Calculate effective card width (accounting for overlap)
        float effectiveCardWidth = cardWidth * (1 - overlap);

        // Calculate total width needed
        float totalWidth = cardCount > 1 ? effectiveCardWidth * (cardCount - 1) + cardWidth : cardWidth;

        // Calculate center point (midpoint between cards or middle card position)
        float centerX = 0;
        float middleIndex = (cardCount - 1) / 2f; // Can be a fraction for even number of cards

        // Position each card
        for (int i = 0; i < cardCount; i++)
        {
            GameObject card = cardUIElements[i];
            RectTransform rectTransform = card.GetComponent<RectTransform>();
            if (rectTransform == null) continue;

            // Calculate position - distribute evenly across total width
            float positionX = centerX + (i - middleIndex) * effectiveCardWidth;

            // Calculate rotation - based on distance from middle
            float distanceFromMiddle = i - middleIndex;

            // Normalize to -1 to 1 range (with sign indicating left or right of center)
            float normalizedDistance = cardCount > 1 ? distanceFromMiddle / (cardCount / 2f) : 0;

            // Calculate rotation - middle card(s) should be straight, outer cards tilted
            float rotation = normalizedDistance * maxRotationAngle;

            // Add slight upward curve for middle cards (optional)
            float yOffset = -Mathf.Abs(normalizedDistance) * 10f; // Higher in middle

            // Apply position and rotation
            rectTransform.anchoredPosition = new Vector2(positionX, yOffset);
            rectTransform.localRotation = Quaternion.Euler(0, 0, -rotation);

            // Set sibling index to ensure proper layering (rightmost on top)
            rectTransform.SetSiblingIndex(i);

            // Make sure the card is visible
            card.SetActive(true);
        }
    }

   
    // Add this coroutine to ensure cards stay visible
    private IEnumerator EnsureCardsVisible()
    {
        // Wait for one frame
        yield return null;

        // Check if any cards are invisible and fix them
        foreach (GameObject cardUI in cardUIElements)
        {
            if (cardUI != null && !cardUI.activeSelf)
            {
                cardUI.SetActive(true);

                // Re-enable all graphic components
                Graphic[] graphics = cardUI.GetComponentsInChildren<Graphic>();
                foreach (Graphic graphic in graphics)
                {
                    if (graphic != null)
                    {
                        graphic.enabled = true;
                    }
                }
            }
        }
    }

    // Add this method to be called when adding a card to the hand
    private void ConfigureCardForLayoutGroup(GameObject cardUI)
    {
        if (cardUI == null) return;

        // Set proper anchors for positioning
        RectTransform rectTransform = cardUI.GetComponent<RectTransform>();
        if (rectTransform != null)
        {
            // Configure anchors for our card positioning system
            rectTransform.anchorMin = new Vector2(0.5f, 0.5f);
            rectTransform.anchorMax = new Vector2(0.5f, 0.5f);
            rectTransform.pivot = new Vector2(0.5f, 0.5f);

            // Reset position (will be set by RepositionHandCards)
            rectTransform.anchoredPosition = Vector2.zero;

            // Set proper size
            rectTransform.sizeDelta = new Vector2(130f, 180f);
        }
    }


    /// <summary>
    /// Handler for clicking a card in the hand
    /// </summary>
    private void OnHandCardClicked(CardData cardData)
    {
        if (cardDetailDisplay != null)
        {
            cardDetailDisplay.ShowCard(cardData, -1, CardDetailDisplay.CardSource.PlayerHand);
        }
    }
   
    private void Update()
    {
        // Check for screen size changes less frequently (every 10 frames)
        if (isInitialized && Time.frameCount % 10 == 0 && Mathf.Abs(Screen.width - lastScreenWidth) > 10f)
        {
            RepositionHandCards();
            lastScreenWidth = Screen.width;
        }
    }
   
    /// <summary>
    /// Restores a previously taken card's visual state
    /// </summary>
    public void RestoreCardVisibility(GameObject cardObject)
    {
        if (cardObject == null) return;
       
        // For 3D cards with Renderers
        Renderer[] renderers = cardObject.GetComponentsInChildren<Renderer>();
        foreach (Renderer renderer in renderers)
        {
            renderer.enabled = true;
        }
       
        // For UI cards with Images
        Image[] images = cardObject.GetComponentsInChildren<Image>();
        foreach (Image image in images)
        {
            Color color = image.color;
            color.a = 1f;
            image.color = color;
        }
       
        // For UI cards with Text
        TextMeshProUGUI[] texts = cardObject.GetComponentsInChildren<TextMeshProUGUI>();
        foreach (TextMeshProUGUI text in texts)
        {
            Color color = text.color;
            color.a = 1f;
            text.color = color;
        }
       
        // Re-enable any colliders
        Collider[] colliders = cardObject.GetComponentsInChildren<Collider>();
        foreach (Collider collider in colliders)
        {
            collider.enabled = true;
        }
       
        // Re-enable button interaction
        Button button = cardObject.GetComponent<Button>();
        if (button != null)
        {
            button.interactable = true;
        }
       
        // Remove the taken marker component
        CardTakenMarker marker = cardObject.GetComponent<CardTakenMarker>();
        if (marker != null)
        {
            Destroy(marker);
        }
    }
   
    // Method to manually set references (used for dependency injection from other scripts)
    public void SetReferences(CardRowUI rowUI, Canvas canvas)
    {
        if (this.cardRowUI == null) this.cardRowUI = rowUI;
        if (this.mainCanvas == null) this.mainCanvas = canvas;
    }
    
    // Clean up resources when destroyed
    private void OnDestroy()
    {
        StopAllCoroutines();
        
        // Clear sprite references
        cardSprites.Clear();
        
        // Clear object pool
        foreach (var card in cardUIPool)
        {
            if (card != null)
            {
                Destroy(card);
            }
        }
        cardUIPool.Clear();
    }
}