using System.Collections.Generic;
using UnityEngine;
using System.Text.RegularExpressions;

public class CardDataFactory
{
    private ResourceTypeConverter resourceConverter;
    private EffectTextParser effectParser;
    
    public CardDataFactory(ResourceTypeConverter resourceConverter, EffectTextParser effectParser)
    {
        this.resourceConverter = resourceConverter;
        this.effectParser = effectParser;
    }
    
    public CardData CreateCardData(List<string> fields, int tier)
    {
        CardData card = new CardData();
        
        // Map fields based on column order in the Excel file
        card.Count = ParseInt(fields[0], 1); // Parse count from column 0, default to 1 if invalid
        card.Name = fields[1];
        card.Type = fields[2];
        card.SubType = fields[3];
        card.Tier = tier;
        card.ScienceCost = ParseInt(fields[4], 0);
        card.BuildCost = resourceConverter.ParseResourceCost(fields[5]);
        card.Effect = fields[6];
        
        // Parse flavor texts if they exist in the CSV
        // Assuming they are the last 3 columns in the CSV
        if (fields.Count > 7) 
            card.SolEncyclopedia = fields[7].Trim();
        
        if (fields.Count > 8) 
        {
            // Preserve quotes for the RealQuote field
            card.RealQuote = fields[8].Trim();
        }
        
        if (fields.Count > 9) 
        {
            // Preserve quotes for the SciFiQuote field
            card.SciFiQuote = fields[9].Trim();
        }
        
        // For power output and power required, we need to parse from the effect text
        card.PowerOutput = effectParser.ParsePowerOutput(card.Effect);
        card.PowerRequired = effectParser.ParsePowerRequired(card.Effect);
        
        // Parse special flags from type/subtype
        card.IsWonder = card.Type.ToLower().Contains("wonder");
        card.IsInitiative = card.Type.ToLower().Contains("initiative");
        card.AutoActivate = false;
        card.RequiresAdvancedAssembler = card.SubType.ToLower().Contains("facility");
        
        // Parse effect parameters
        card.EffectParams = ParseEffectParameters(card.Effect);
        
        // Parse input and output resources from effect
        effectParser.ParseEffectResources(card);
        
        return card;
    }
    
    private int ParseInt(string text, int defaultValue = 0)
    {
        if (int.TryParse(text, out int result))
        {
            return result;
        }
        
        // Try to parse float and convert to int (for science values that might be "2.5")
        if (float.TryParse(text, out float floatResult))
        {
            return (int)floatResult;
        }
        
        return defaultValue;
    }
    
    private Dictionary<string, object> ParseEffectParameters(string effectText)
    {
        Dictionary<string, object> parameters = new Dictionary<string, object>();
        
        // Parse common effect patterns
        if (effectText.Contains("Activate:") || effectText.Contains("Activate ("))
        {
            parameters["isActivatable"] = true;
            
            // Handle both "Activate:" and "Activate (1 Power):" patterns
            Match activateMatch = Regex.Match(effectText, @"Activate(?:\s*\([^)]*\))?\s*:\s*(.+)");
            if (activateMatch.Success)
            {
                string resourcePart = activateMatch.Groups[1].Value;
                ParseActivationCosts(resourcePart, parameters);
            }
        }
        
        // Parse delta-v
        Match deltaVMatch = Regex.Match(effectText, @"(\d+)\s*delta-v per fuel");
        if (deltaVMatch.Success)
        {
            parameters["deltaVPerFuel"] = int.Parse(deltaVMatch.Groups[1].Value);
        }
        
        // Parse cargo capacity
        Match cargoMatch = Regex.Match(effectText, @"Cargo (\d+)x(\d+)");
        if (cargoMatch.Success)
        {
            int width = int.Parse(cargoMatch.Groups[1].Value);
            int height = int.Parse(cargoMatch.Groups[2].Value);
            parameters["cargoCapacity"] = width * height;
        }
        
        // Parse ship strength
        Match strengthMatch = Regex.Match(effectText, @"Strength (\d+)");
        if (strengthMatch.Success)
        {
            parameters["shipStrength"] = int.Parse(strengthMatch.Groups[1].Value);
        }
        
        // Parse special flags
        if (effectText.Contains("aerobrake"))
        {
            parameters["canAerobrake"] = true;
        }
        
        if (effectText.Contains("consumed on survey"))
        {
            parameters["consumedOnSurvey"] = true;
        }
        
        return parameters;
    }
    
    private void ParseActivationCosts(string resourceText, Dictionary<string, object> parameters)
    {
        List<Resource> inputs = new List<Resource>();
        List<Resource> outputs = new List<Resource>();
        
        // Split by commas and parse each resource
        string[] parts = resourceText.Split(',');
        foreach (string part in parts)
        {
            string trimmed = part.Trim();
            
            // Check if it's an input (negative) or output (positive)
            bool isInput = trimmed.StartsWith("-");
            bool isOutput = trimmed.StartsWith("+");
            
            // Remove +/- symbols
            trimmed = trimmed.TrimStart('-', '+').Trim();
            
            Match match = Regex.Match(trimmed, @"(\d+)\s+(.+)");
            if (match.Success)
            {
                int amount = int.Parse(match.Groups[1].Value);
                string resourceName = match.Groups[2].Value;
                ResourceType resourceType = resourceConverter.ParseResourceType(resourceName);
                
                if (isInput)
                {
                    inputs.Add(new Resource(resourceType, amount));
                }
                else if (isOutput)
                {
                    outputs.Add(new Resource(resourceType, amount));
                }
                else
                {
                    // If no sign, assume it's an output
                    outputs.Add(new Resource(resourceType, amount));
                }
            }
        }
        
        parameters["inputResources"] = inputs;
        parameters["outputResources"] = outputs;
    }
}