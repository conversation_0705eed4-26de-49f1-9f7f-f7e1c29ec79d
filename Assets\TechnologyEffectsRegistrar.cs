using System.Collections.Generic;
using System.Text.RegularExpressions;
using UnityEngine;

/// <summary>
/// Automatically registers technology effects with the TechnologyEffectsManager
/// </summary>
public class TechnologyEffectsRegistrar : MonoBehaviour
{
    [SerializeField] private TechnologyDeckManager deckManager;
    [SerializeField] private bool registerOnStart = true;
    
    private void Start()
    {
        if (registerOnStart)
        {
            RegisterAllTechnologyEffects();
        }
    }
    
    /// <summary>
    /// Register all technology effects with the TechnologyEffectsManager
    /// </summary>
    public void RegisterAllTechnologyEffects()
    {
        // Make sure we have managers
        if (deckManager == null)
        {
            deckManager = FindFirstObjectByType<TechnologyDeckManager>();
            if (deckManager == null)
            {
                Debug.LogError("Cannot find TechnologyDeckManager!");
                return;
            }
        }
        
        TechnologyEffectsManager effectsManager = TechnologyEffectsManager.Instance;
        if (effectsManager == null)
        {
            Debug.LogError("TechnologyEffectsManager.Instance is null!");
            return;
        }
        
        // Get all tech cards
        List<TechnologyCard> allTechCards = deckManager.GetTechCardsForEffectsManager();

        // Register each tech card's effects
        int effectsRegistered = 0;
        foreach (TechnologyCard tech in allTechCards)
        {
            if (RegisterTechEffects(tech, effectsManager))
            {
                effectsRegistered++;
            }
        }
        
        Debug.Log($"Registered effects for {effectsRegistered} technology cards");
    }
    
    /// <summary>
    /// Register effects for a specific technology card
    /// </summary>
    private bool RegisterTechEffects(TechnologyCard tech, TechnologyEffectsManager effectsManager)
    {
        string description = tech.Description;
        string name = tech.Name;
        
        bool effectsAdded = false;
        
        // Advanced Photovoltaics
        if (name.Contains("Advanced Photovoltaics") || 
            (description.Contains("solar panels") && description.Contains("additional power")))
        {
            Match match = Regex.Match(description, @"(\d+) additional power");
            if (match.Success)
            {
                float value = float.Parse(match.Groups[1].Value);
                effectsManager.AddTechnologyEffect(name, TechnologyEffectsManager.TechEffectType.PowerBonus, value);
                effectsAdded = true;
            }
        }
        
        // Advanced Ore/Ice Extraction
        if (name.Contains("Advanced Ore/Ice Extraction") || 
            (description.Contains("ore") && description.Contains("ice") && 
             description.Contains("additional") && description.Contains("per deposit")))
        {
            Match match = Regex.Match(description, @"(\d+) additional");
            if (match.Success)
            {
                float value = float.Parse(match.Groups[1].Value);
                effectsManager.AddTechnologyEffect(name, TechnologyEffectsManager.TechEffectType.OreIceExtractionBonus, value);
                effectsAdded = true;
            }
        }
        
        // Advanced Non-metal Extraction
        if (name.Contains("Advanced Non-metal Extraction") || 
            (description.Contains("carbon") && description.Contains("silicon") && 
             description.Contains("additional") && description.Contains("per deposit")))
        {
            Match match = Regex.Match(description, @"(\d+) additional");
            if (match.Success)
            {
                float value = float.Parse(match.Groups[1].Value);
                effectsManager.AddTechnologyEffect(name, TechnologyEffectsManager.TechEffectType.NonMetalExtractionBonus, value);
                effectsAdded = true;
            }
        }
        
        // Advanced Rare Earths Extraction
        if (name.Contains("Advanced Rare Earths Extraction") || 
            (description.Contains("rare earth") && description.Contains("additional") && 
             description.Contains("per deposit")))
        {
            Match match = Regex.Match(description, @"(\d+) additional");
            if (match.Success)
            {
                float value = float.Parse(match.Groups[1].Value);
                effectsManager.AddTechnologyEffect(name, TechnologyEffectsManager.TechEffectType.RareEarthsExtractionBonus, value);
                effectsAdded = true;
            }
        }
        
        // Advanced Ion Propulsion
        if (name.Contains("Advanced Ion Propulsion") || 
            (description.Contains("delta-v per fuel") && description.Contains("for all ships")))
        {
            Match match = Regex.Match(description, @"\+(\d+) delta-v");
            if (match.Success)
            {
                float value = float.Parse(match.Groups[1].Value);
                effectsManager.AddTechnologyEffect(name, TechnologyEffectsManager.TechEffectType.DeltaVPerFuelBonus, value);
                effectsAdded = true;
            }
        }
        
        // AI Logistics
        if (name.Contains("AI Logistics") || 
            (description.Contains("action") && description.Contains("+1")))
        {
            effectsManager.AddTechnologyEffect(name, TechnologyEffectsManager.TechEffectType.ActionBonus, 1.0f);
            effectsAdded = true;
        }
        
        return effectsAdded;
    }
}