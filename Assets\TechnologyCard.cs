using System.Collections.Generic;
using UnityEngine;
using TMPro;
using UnityEngine.UI;

/// <summary>
/// Base class for all technology cards in the game
/// </summary>
public class TechnologyCard : MonoBehaviour
{
    [Header("Card Information")]
    [SerializeField] protected string cardName;
    [SerializeField] protected int tier = 0;
    [SerializeField] protected int scienceValue = 0;

    [Header("Resource Cost")]
    [SerializeField] protected List<Resource> resourceCost = new List<Resource>();

    [Header("UI References")]
    [SerializeField] protected TextMeshProUGUI titleText;
    [SerializeField] protected TextMeshProUGUI scienceText;
    [SerializeField] protected TextMeshProUGUI effectText;
    [SerializeField] protected Image backgroundImage;
    [SerializeField] protected Image iconImage;

    [Header("Card Description")]
    [SerializeField] [TextArea(3, 5)] protected string cardDescription;

    // Properties
    public string Name => cardName;
    public int Tier => tier;
    public int ScienceValue => scienceValue;
    public List<Resource> Cost => resourceCost;
    public string Description => cardDescription;
    
    private void Awake()
    {
        // Find text components if they're not already assigned
        FindTextComponents();
    }
    public TechnologyCardData GetDataCopy()
    {
        return CreateDataCopy();
    }
    // Find and assign text components if they're not set
    protected void FindTextComponents()
    {
        if (titleText == null)
        {
            titleText = transform.Find("TitleText")?.GetComponent<TextMeshProUGUI>();
            if (titleText == null)
            {
                // Try different naming conventions
                titleText = transform.Find("Title")?.GetComponent<TextMeshProUGUI>();
            }
            if (titleText == null)
            {
                // Last resort: find any text component that might be the title
                TextMeshProUGUI[] texts = GetComponentsInChildren<TextMeshProUGUI>();
                foreach (var text in texts)
                {
                    if (text.gameObject.name.Contains("Title") || text.gameObject.name.Contains("Name"))
                    {
                        titleText = text;
                        break;
                    }
                }
            }
        }
        
        if (scienceText == null)
        {
            scienceText = transform.Find("ScienceText")?.GetComponent<TextMeshProUGUI>();
            if (scienceText == null)
            {
                scienceText = transform.Find("Science")?.GetComponent<TextMeshProUGUI>();
            }
            if (scienceText == null)
            {
                // Last resort: find any text component that might be the science cost
                TextMeshProUGUI[] texts = GetComponentsInChildren<TextMeshProUGUI>();
                foreach (var text in texts)
                {
                    if (text.gameObject.name.Contains("Science") || text.gameObject.name.Contains("Cost"))
                    {
                        scienceText = text;
                        break;
                    }
                }
            }
        }
        
        if (effectText == null)
        {
            effectText = transform.Find("EffectText")?.GetComponent<TextMeshProUGUI>();
            if (effectText == null)
            {
                effectText = transform.Find("Description")?.GetComponent<TextMeshProUGUI>();
            }
            if (effectText == null)
            {
                // Last resort: find any text component that might be the effect text
                TextMeshProUGUI[] texts = GetComponentsInChildren<TextMeshProUGUI>();
                foreach (var text in texts)
                {
                    if (text.gameObject.name.Contains("Effect") || 
                        text.gameObject.name.Contains("Description") ||
                        text.gameObject.name.Contains("Text"))
                    {
                        // Skip if it's likely the title or science
                        if (text != titleText && text != scienceText)
                        {
                            effectText = text;
                            break;
                        }
                    }
                }
            }
        }
        
        // Find background image if not set
        if (backgroundImage == null)
        {
            // Try to find the main card background
            backgroundImage = GetComponent<Image>();
            if (backgroundImage == null)
            {
                backgroundImage = transform.Find("Background")?.GetComponent<Image>();
            }
        }
        
        // Find icon image if not set
        if (iconImage == null)
        {
            iconImage = transform.Find("Icon")?.GetComponent<Image>();
        }
    }
    
    // Virtual methods that can be overridden by subclasses
    public virtual void Initialize()
    {
        // Ensure UI components are found
        FindTextComponents();
        
        // Set the UI text elements if they exist
        if (titleText != null)
        {
            titleText.text = cardName;
        }
        else
        {
            //Debug.LogWarning($"TitleText component not found for {cardName}");
        }
            
        if (scienceText != null)
        {
            scienceText.text = scienceValue > 0 ? scienceValue.ToString() : "";
            //Debug.Log($"Set science text for {cardName}");
        }
        else if (scienceValue > 0)
        {
            //Debug.LogWarning($"ScienceText component not found for {cardName}, but scienceValue is {scienceValue}");
        }
            
        if (effectText != null)
        {
            effectText.text = cardDescription.Replace("\n", "<br>");
            //Debug.Log($"Set effect text for {cardName}");
        }
        else
        {
            //Debug.LogWarning($"EffectText component not found for {cardName}");
        }
        
        // Set tier-specific card appearance
        UpdateCardAppearance();
    }
    
    // Update card appearance based on tier
    protected virtual void UpdateCardAppearance()
    {
        if (backgroundImage != null)
        {
            // Set color based on tier
            Color tierColor = Color.white;
            switch (tier)
            {
                case 0:
                    tierColor = new Color(0.8f, 0.8f, 0.8f); // Gray for starting techs
                    break;
                case 1:
                    tierColor = new Color(0.8f, 0.9f, 1.0f); // Light blue for tier 1
                    break;
                case 2:
                    tierColor = new Color(0.7f, 0.8f, 1.0f); // Medium blue for tier 2
                    break;
                case 3:
                    tierColor = new Color(0.5f, 0.6f, 1.0f); // Darker blue for tier 3
                    break;
            }
            
            backgroundImage.color = tierColor;
        }
    }

    public virtual bool CanAcquire(Player player, GameObject location)
    {
        // Check if player has enough science
        if (player.ScienceValue < scienceValue)
            return false;
            
        // Check if player has enough resources at the location
        foreach (Resource resource in resourceCost)
        {
            if (player.GetResourceAmount(location, resource.Type) < resource.Amount)
                return false;
        }
        
        return true;
    }

    public virtual void OnAcquire(Player player, GameObject location)
    {
        // Deduct resources
        foreach (Resource resource in resourceCost)
        {
            player.UseResource(location, resource.Type, resource.Amount);
        }
        
        // Deduct science
        if (scienceValue > 0)
        {
            player.ScienceValue -= scienceValue;
        }
        
        // Add to player's technologies
        player.AddTechnology(CreateDataCopy());
        
        Debug.Log($"Player {player.PlayerId} acquired {cardName}");
        
        // Notify the tech effects manager
        TechnologyEffectsManager effectsManager = TechnologyEffectsManager.Instance;
        if (effectsManager != null)
        {
            effectsManager.OnTechnologyAcquired(this.CreateDataCopy() );
        }
    }
    
    // Create a data-only copy of this technology card
    protected virtual TechnologyCardData CreateDataCopy()
    {
        TechnologyCardData dataCopy = new TechnologyCardData
        {
            cardName = this.cardName,
            tier = this.tier,
            scienceValue = this.scienceValue,
            cardDescription = this.cardDescription,
            resourceCost = new List<Resource>()
        };
        
        // Copy resource cost
        foreach (Resource resource in this.resourceCost)
        {
            dataCopy.resourceCost.Add(new Resource(resource.Type, resource.Amount));
        }
        
        return dataCopy;
    }

    // Helper method to display resource cost as a string
    public string GetCostString()
    {
        string costString = "";
        
        foreach (Resource resource in resourceCost)
        {
            costString += $"{resource.Amount} {resource.Type}, ";
        }
        
        // Remove the trailing comma and space
        if (costString.Length > 2)
            costString = costString.Substring(0, costString.Length - 2);
            
        return costString;
    }
    
    // Called when card is clicked or hovered
    public virtual void OnCardHighlighted()
    {
        // Visual feedback for highlighting
        if (backgroundImage != null)
        {
            // Slightly brighten the card
            backgroundImage.color = new Color(
                backgroundImage.color.r + 0.1f,
                backgroundImage.color.g + 0.1f,
                backgroundImage.color.b + 0.1f,
                backgroundImage.color.a
            );
        }
    }
    
    // Called when card is no longer highlighted
    public virtual void OnCardUnhighlighted()
    {
        // Restore normal appearance
        UpdateCardAppearance();
    }
}