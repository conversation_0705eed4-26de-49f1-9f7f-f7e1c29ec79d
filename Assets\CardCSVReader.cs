using System.Collections.Generic;
using System.Text.RegularExpressions;

public class Card<PERSON><PERSON>eader
{
    public List<List<string>> ParseCSV(string csvContent)
    {
        List<List<string>> result = new List<List<string>>();
        
        // Split the content into lines
        string[] lines = csvContent.Split('\n');
        
        foreach (string line in lines)
        {
            if (string.IsNullOrWhiteSpace(line))
                continue;
                
            List<string> fields = ParseCsvLine(line);
            result.Add(fields);
        }
        
        return result;
    }

    private List<string> ParseCsvLine(string line)
    {
        List<string> fields = new List<string>();
        bool inQuotes = false;
        string currentField = "";

        for (int i = 0; i < line.Length; i++)
        {
            char c = line[i];

            if (c == '"')
            {
                if (inQuotes && i + 1 < line.Length && line[i + 1] == '"')
                {
                    // Double quote - add one quote and skip the next
                    currentField += '"';
                    i++; // Skip the next quote
                }
                else
                {
                    // Toggle quote state but don't add the quote to the field
                    inQuotes = !inQuotes;
                }
            }
            else if (c == ',' && !inQuotes)
            {
                // End of field reached
                fields.Add(currentField.Trim());
                currentField = "";
            }
            else
            {
                // Add character to current field
                currentField += c;
            }
        }

        // Add the last field
        fields.Add(currentField.Trim());

        return fields;
    }
}