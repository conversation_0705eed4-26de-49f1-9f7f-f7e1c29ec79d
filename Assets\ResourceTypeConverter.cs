using UnityEngine;
using System.Collections.Generic;

public class ResourceTypeConverter
{
    public ResourceType ParseResourceType(string resourceName)
    {
        // Clean up the resource name by removing quotes and extra spaces
        resourceName = resourceName.Trim('"', ' ');

        switch (resourceName.ToLower().Replace(" ", ""))
        {
            case "ore":
            case "iron": return ResourceType.Ore;
            case "ice":
            case "water": return ResourceType.Ice;
            case "carbon": return ResourceType.Carbon;
            case "silicon": return ResourceType.Silicon;
            case "rareearth":
            case "rareearths":
            case "rareminerals": return ResourceType.RareEarths;
            case "alloy":
            case "alloys": return ResourceType.Alloys;
            case "fuel": return ResourceType.Fuel;
            case "graphene": return ResourceType.Graphene;
            case "ceramic":
            case "ceramics": return ResourceType.Ceramics;
            case "microchip":
            case "microchips": return ResourceType.Microchips;
            case "superconductor":
            case "superconductors": return ResourceType.Superconductors;
            case "metallichydrogen": return ResourceType.MetallicHydrogen;
            case "antimatter": return ResourceType.Antimatter;
            case "helium3":
            case "helium-3": return ResourceType.Helium3;
            case "power": return ResourceType.Power;
            case "victorypoints":
            case "vp": return ResourceType.VP;
            case "$":
            case "dollars": return ResourceType.Dollars;
            case "science": return ResourceType.Science;
            case "strength": return ResourceType.Strength;
            default:
                // Check for Rare Earths as a special case
                if (resourceName.ToLower().Contains("rare") && (resourceName.ToLower().Contains("earth") || resourceName.ToLower().Contains("mineral")))
                    return ResourceType.RareEarths;

                return ResourceType.Unknown;
        }
    }
    
    public List<Resource> ParseResourceCost(string costText)
    {
        List<Resource> resources = new List<Resource>();
        
        if (string.IsNullOrEmpty(costText) || costText.ToLower() == "none" || costText == "-")
            return resources;
        
        // Handle quoted resource cost texts by first removing the quotes
        if (costText.StartsWith("\"") && costText.EndsWith("\""))
        {
            // Remove enclosing quotes
            costText = costText.Substring(1, costText.Length - 2);
            
            // Replace double quotes with single quotes if they exist
            costText = costText.Replace("\"\"", "\"");
        }
        
        // Parse format like "2 Alloy, 1 Microchip"
        string[] parts = costText.Split(',');
        foreach (string part in parts)
        {
            string trimmed = part.Trim();
            System.Text.RegularExpressions.Match match = System.Text.RegularExpressions.Regex.Match(trimmed, @"(\d+)\s+(.+)");
            
            if (match.Success)
            {
                int amount = int.Parse(match.Groups[1].Value);
                string resourceName = match.Groups[2].Value;
                ResourceType resourceType = ParseResourceType(resourceName);
                
                resources.Add(new Resource(resourceType, amount));
            }
        }
        
        return resources;
    }
}