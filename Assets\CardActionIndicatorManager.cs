using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

public class CardActionIndicatorManager
{
    private GameObject actionIndicatorPrefab;
    private List<Transform> indicatorContainers;
    private List<List<GameObject>> buttonActionIndicators;

    public CardActionIndicatorManager(
        GameObject actionIndicatorPrefab,
        Transform actionIndicatorContainer,
        Transform secondaryActionIndicatorContainer,
        Transform tertiaryActionIndicatorContainer,
        Transform button4ActionIndicatorContainer,
        Transform button5ActionIndicatorContainer,
        Transform button6ActionIndicatorContainer,
        Transform button7ActionIndicatorContainer,
        Transform button8ActionIndicatorContainer)
    {
        this.actionIndicatorPrefab = actionIndicatorPrefab;
        
        indicatorContainers = new List<Transform> {
            actionIndicatorContainer, secondaryActionIndicatorContainer,
            tertiaryActionIndicatorContainer, button4ActionIndicatorContainer,
            button5ActionIndicatorContainer, button6ActionIndicatorContainer,
            button7ActionIndicatorContainer, button8ActionIndicatorContainer
        };

        buttonActionIndicators = new List<List<GameObject>>();
        for (int i = 0; i < 8; i++)
        {
            buttonActionIndicators.Add(new List<GameObject>());
        }
    }

    public void CreateActionIndicators(int buttonIndex, int actionCost)
    {
        if (buttonIndex < 0 || buttonIndex >= buttonActionIndicators.Count) return;

        ClearActionIndicators(buttonIndex);

        Transform container = GetContainer(buttonIndex);
        if (container == null || actionIndicatorPrefab == null) return;

        // Special handling for Take Card button with 3 indicators
        if (buttonIndex == 0 && actionCost == 3)
        {
            HorizontalLayoutGroup layoutGroup = container.GetComponent<HorizontalLayoutGroup>();
            if (layoutGroup != null)
            {
                layoutGroup.padding.left = 30;
            }
        }
        else if (buttonIndex == 0)
        {
            HorizontalLayoutGroup layoutGroup = container.GetComponent<HorizontalLayoutGroup>();
            if (layoutGroup != null)
            {
                layoutGroup.padding.left = 20;
            }
        }

        for (int i = 0; i < actionCost; i++)
        {
            GameObject indicator = GameObject.Instantiate(actionIndicatorPrefab, container);
            indicator.SetActive(true);
            indicator.transform.localScale = new Vector3(1.5f, 1.5f, 1.5f);
            buttonActionIndicators[buttonIndex].Add(indicator);
        }
    }

    private void ClearActionIndicators(int buttonIndex)
    {
        if (buttonIndex < 0 || buttonIndex >= buttonActionIndicators.Count) return;

        foreach (GameObject indicator in buttonActionIndicators[buttonIndex])
        {
            if (indicator != null)
                GameObject.Destroy(indicator);
        }
        buttonActionIndicators[buttonIndex].Clear();
    }

    private Transform GetContainer(int buttonIndex)
    {
        return (buttonIndex >= 0 && buttonIndex < indicatorContainers.Count) ? 
               indicatorContainers[buttonIndex] : null;
    }

    public void ClearAllIndicators()
    {
        for (int i = 0; i < buttonActionIndicators.Count; i++)
        {
            ClearActionIndicators(i);
        }
    }
}