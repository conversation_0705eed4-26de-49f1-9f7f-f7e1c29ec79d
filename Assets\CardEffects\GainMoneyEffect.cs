using System.Collections.Generic;
using UnityEngine; 

public class GainMoneyEffect : ICardEffect
{
    private string name;
    private int amount;
    
    public GainMoneyEffect(string name, int amount)
    {
        this.name = name;
        this.amount = amount;
    }
    
    public bool CanExecute(Player player, GameObject location, Dictionary<string, object> parameters)
    {
        return true;
    }
    
    public bool Execute(Player player, GameObject location, Dictionary<string, object> parameters)
    {
        player.Money += amount;
        Debug.Log($"Player {player.PlayerId} gained ${amount} from {name}");
        return true;
    }
    
    public string GetDescription()
    {
        return $"Initiative: Gain ${amount}.";
    }
}