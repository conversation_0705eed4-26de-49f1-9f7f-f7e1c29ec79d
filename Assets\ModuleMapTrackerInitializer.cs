using UnityEngine;

/// <summary>
/// Initializes the ModuleMapTracker in the scene and assigns module prefabs
/// </summary>
public class ModuleMapTrackerInitializer : MonoBehaviour
{
    [Header("Module Prefab References")]
    [SerializeField] private GameObject redModulePrefab;
    [SerializeField] private GameObject greenModulePrefab;
    [SerializeField] private GameObject blueModulePrefab;
    [SerializeField] private GameObject yellowModulePrefab;
    
    private void Start()
    {
        InitializeModuleMapTracker();
    }
    
    private void InitializeModuleMapTracker()
    {
        // Check if ModuleMapTracker already exists
        if (ModuleMapTracker.Instance != null)
        {
            Debug.Log("ModuleMapTracker already exists in scene");
            // Still initialize prefabs if they're not set
            ModuleMapTracker.Instance.InitializeModulePrefabs(redModulePrefab, greenModulePrefab, blueModulePrefab, yellowModulePrefab);
            return;
        }

        // Create ModuleMapTracker GameObject
        GameObject trackerObject = new GameObject("ModuleMapTracker");
        ModuleMapTracker tracker = trackerObject.AddComponent<ModuleMapTracker>();

        // Initialize prefabs
        tracker.InitializeModulePrefabs(redModulePrefab, greenModulePrefab, blueModulePrefab, yellowModulePrefab);
    }
}
