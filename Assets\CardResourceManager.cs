using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// Manages resource data and information for card display
/// </summary>
public class CardResourceManager
{
    // Resource data parsed from resources.txt
    public class ResourceInfo
    {
        public string DisplayName;
        public string ProductionText;
    }
    
    private Dictionary<ResourceType, ResourceInfo> resourceData = new Dictionary<ResourceType, ResourceInfo>();
    private ResourceManager resourceManagerCache;

    public CardResourceManager()
    {
        // Load resource data on initialization
        LoadResourceData();
    }

    /// <summary>
    /// Load resource data from resources.txt in the Resources folder
    /// </summary>
    private void LoadResourceData()
    {
        // Load the resources.txt file
        TextAsset resourcesFile = Resources.Load<TextAsset>("resources");
        if (resourcesFile == null)
        {
            Debug.LogError("CardResourceManager: Could not find resources.txt in Resources folder!");
            return;
        }
        
        // Parse the file
        string[] lines = resourcesFile.text.Split('\n');
        foreach (string line in lines)
        {
            if (string.IsNullOrWhiteSpace(line))
                continue;
                
            // Split by the first colon
            int colonIndex = line.IndexOf(':');
            if (colonIndex == -1)
                continue;
                
            string resourceName = line.Substring(0, colonIndex).Trim();
            string description = line.Substring(colonIndex + 1).Trim();
            
            // Parse the resource name to ResourceType
            ResourceType? resourceType = ParseResourceType(resourceName);
            if (resourceType == null)
            {
                Debug.LogWarning($"Could not parse resource type from: {resourceName}");
                continue;
            }
            
            // Create resource info
            ResourceInfo info = new ResourceInfo
            {
                DisplayName = resourceName,
                ProductionText = description
            };
            
            resourceData[resourceType.Value] = info;
        }
    }
    
    /// <summary>
    /// Parse a resource name to its ResourceType
    /// </summary>
    public ResourceType? ParseResourceType(string resourceName)
    {
        // Remove any hyphens for parsing
        string cleanName = resourceName.Replace("-", "").Replace(" ", "");
        
        switch (cleanName.ToLower())
        {
            case "ore":
            case "iron": return ResourceType.Ore;
            case "water":
            case "ice": return ResourceType.Ice;
            case "carbon": return ResourceType.Carbon;
            case "silicon": return ResourceType.Silicon;
            case "rareearth":
            case "rareminerals":
            case "rareearths": return ResourceType.RareEarths;
            case "alloy":
            case "alloys": return ResourceType.Alloys;
            case "fuel": return ResourceType.Fuel;
            case "graphene": return ResourceType.Graphene;
            case "ceramic":
            case "ceramics": return ResourceType.Ceramics;
            case "microchip":
            case "microchips": return ResourceType.Microchips;
            case "superconductor":
            case "superconductors": return ResourceType.Superconductors;
            case "metallichydrogen": return ResourceType.MetallicHydrogen;
            case "antimatter": return ResourceType.Antimatter;
            case "helium3":
            case "helium-3": return ResourceType.Helium3;
            case "power": return ResourceType.Power;
            case "victorypoints":
            case "vp": return ResourceType.VP;
            case "$":
            case "dollar":
            case "dollars": return ResourceType.Dollars;
            case "science": return ResourceType.Science;
            case "strength": return ResourceType.Strength;
            default:
                // Check for Rare Earths as a special case
                if (resourceName.ToLower().Contains("rare") && resourceName.ToLower().Contains("earth"))
                    return ResourceType.RareEarths;
                    
                Debug.LogWarning($"Unknown resource type: {resourceName}");
                return null;
        }
    }
    
    /// <summary>
    /// Get resource information for a specific resource type
    /// </summary>
    public ResourceInfo GetResourceInfo(ResourceType resourceType)
    {
        if (resourceData.TryGetValue(resourceType, out ResourceInfo info))
        {
            return info;
        }
        
        // Return a default resource info if not found
        return new ResourceInfo
        {
            DisplayName = resourceType.ToString(),
            ProductionText = ""
        };
    }
    
    /// <summary>
    /// Get the resource icon sprite for a specific resource type
    /// </summary>
    public Sprite GetResourceIcon(ResourceType resourceType)
    {
        // Cache the resource manager reference to avoid repeated lookups
        if (resourceManagerCache == null)
        {
            resourceManagerCache = ResourceManager.Instance;
        }
        
        return resourceManagerCache?.GetResourceIcon(resourceType);
    }
    
    /// <summary>
    /// Get the resource color for a specific resource type
    /// </summary>
    public Color GetResourceColor(ResourceType resourceType)
    {
        // Cache the resource manager reference to avoid repeated lookups
        if (resourceManagerCache == null)
        {
            resourceManagerCache = ResourceManager.Instance;
        }
        
        return resourceManagerCache?.GetResourceColor(resourceType) ?? Color.white;
    }
    
    /// <summary>
    /// Check if resource data has been loaded for a specific resource type
    /// </summary>
    public bool HasResourceData(ResourceType resourceType)
    {
        return resourceData.ContainsKey(resourceType);
    }
}