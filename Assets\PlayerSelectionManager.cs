using UnityEngine;
using UnityEngine.UI;
using TMPro;

/// <summary>
/// Manages player selection and updates the central UI
/// </summary>
public class PlayerSelectionManager : MonoBehaviour
{
    // Singleton pattern
    public static PlayerSelectionManager Instance { get; private set; }
    
    [Header("Central UI Elements")]
    [SerializeField] private GameObject playerInfoPanel;
    [SerializeField] private TextMeshProUGUI playerNameText;
    [SerializeField] private TextMeshProUGUI resourcesText;
    [SerializeField] private TextMeshProUGUI victoryPointsText;
    [SerializeField] private TextMeshProUGUI shipsText;
    
    [Header("Action Buttons")]
    [SerializeField] private Button mineButton;
    [SerializeField] private Button buildButton;
    [SerializeField] private Button processButton;
    [SerializeField] private Button moveButton;
    [SerializeField] private Button tradeButton;
    
    // Currently selected player
    private int selectedPlayerId = -1;
    private Player selectedPlayer;
    
    private void Awake()
    {
        // Singleton setup
        if (Instance != null && Instance != this)
        {
            Destroy(gameObject);
            return;
        }
        
        Instance = this;
    }
    
    private void Start()
    {
        // Initially hide the player info panel
        if (playerInfoPanel != null)
            playerInfoPanel.SetActive(false);
            
        // Setup button listeners
        SetupButtonListeners();
    }
    
    /// <summary>
    /// Set up button click listeners
    /// </summary>
    private void SetupButtonListeners()
    {
        if (mineButton != null)
            mineButton.onClick.AddListener(() => OnActionButtonClicked(ActionType.Extract));
            
        if (buildButton != null)
            buildButton.onClick.AddListener(() => OnActionButtonClicked(ActionType.Build));
            
        if (processButton != null)
            processButton.onClick.AddListener(() => OnActionButtonClicked(ActionType.Process));
            
        if (moveButton != null)
            moveButton.onClick.AddListener(() => OnActionButtonClicked(ActionType.Move));
            
        if (tradeButton != null)
            tradeButton.onClick.AddListener(() => OnActionButtonClicked(ActionType.Trade));
    }
    
    /// <summary>
    /// Called when a player area is selected
    /// </summary>
    public void OnPlayerSelected(int playerId, Player player)
    {
        selectedPlayerId = playerId;
        selectedPlayer = player;
        
        // Show and update the player info panel
        UpdatePlayerInfoPanel();
        
        // Show the panel if it was hidden
        if (playerInfoPanel != null)
            playerInfoPanel.SetActive(true);
    }
    
    /// <summary>
    /// Update the player info panel with current data
    /// </summary>
    private void UpdatePlayerInfoPanel()
    {
        if (selectedPlayer == null)
            return;
            
        if (playerNameText != null)
            playerNameText.text = $"Player {selectedPlayerId + 1}";
            
        if (resourcesText != null)
            resourcesText.text = $"Money: ${selectedPlayer.Money}\nScience: {selectedPlayer.ScienceValue}";
            
        if (victoryPointsText != null)
            victoryPointsText.text = $"Victory Points: {selectedPlayer.VictoryPoints}";
            
        if (shipsText != null)
            shipsText.text = $"Ships: {selectedPlayer.ShipsInPlay}/{selectedPlayer.MaxShips}";
    }
    
    /// <summary>
    /// Handle action button clicks
    /// </summary>
    private void OnActionButtonClicked(ActionType actionType)
    {
        if (selectedPlayer == null)
            return;
            
        // Check if this is the current player's turn
        GameManager gameManager = GameManager.Instance;
        if (gameManager == null)
            return;
            
        bool isCurrentPlayersTurn = gameManager.CurrentPlayerIndex == selectedPlayerId;
        
        if (!isCurrentPlayersTurn)
        {
            Debug.LogWarning($"Cannot take action - it's not Player {selectedPlayerId}'s turn!");
            return;
        }
        
        // Forward the action to the GameController
        GameController gameController = FindFirstObjectByType<GameController>();
        if (gameController != null)
        {
            // Call appropriate method based on action type
            switch (actionType)
            {
                case ActionType.Extract:
                    // Start mining sequence
                    break;
                case ActionType.Build:
                    // Start building sequence
                    break;
                case ActionType.Process:
                    // Start processing sequence
                    break;
                case ActionType.Move:
                    // Start movement sequence
                    break;
                case ActionType.Trade:
                    // Start trading sequence
                    break;
            }
        }
    }
}