using UnityEngine;
using UnityEngine.UI;
using TMPro;

/// <summary>
/// Visual representation of an Earth card
/// </summary>
public class EarthCardVisual : MonoBehaviour
{
    [Header("Card References")]
    [SerializeField] private TextMeshProUGUI titleText;
    [SerializeField] private TextMeshProUGUI descriptionText;
    [SerializeField] private TextMeshProUGUI tierText;
    [SerializeField] private Image backgroundImage;
    [SerializeField] private Image cardFrame;
    
    [Header("Option Buttons (for Election Cards)")]
    [SerializeField] private GameObject optionsPanel;
    [SerializeField] private Button option1Button;
    [SerializeField] private Button option2Button;
    [SerializeField] private TextMeshProUGUI option1Text;
    [SerializeField] private TextMeshProUGUI option2Text;
    
    // The card this visual represents
    private EarthCard card;
    
    /// <summary>
    /// Set up this visual to represent an Earth card
    /// </summary>
    public void SetupCard(EarthCard card)
    {
        this.card = card;
        
        // Set text elements
        if (titleText != null)
            titleText.text = card.Name;
            
        if (descriptionText != null)
            descriptionText.text = card.Description;
            
        if (tierText != null)
            tierText.text = $"Tier {card.Tier}";
            
        // Set card color based on tier
        Color tierColor = card.GetTierColor();
        
        if (backgroundImage != null)
            backgroundImage.color = tierColor;
            
        if (cardFrame != null)
            cardFrame.color = Color.Lerp(tierColor, Color.white, 0.5f);
            
        // Setup option buttons for election cards
        bool isElectionCard = card.EffectType == EarthCardEffectType.ElectionChoice;
        
        if (optionsPanel != null)
            optionsPanel.SetActive(isElectionCard);
            
        if (isElectionCard)
        {
            // Set option texts
            if (option1Text != null)
                option1Text.text = card.Option1Text;
                
            if (option2Text != null)
                option2Text.text = card.Option2Text;
                
            // Set button listeners
            if (option1Button != null)
                option1Button.onClick.AddListener(OnOption1Selected);
                
            if (option2Button != null)
                option2Button.onClick.AddListener(OnOption2Selected);
        }
    }
    
    /// <summary>
    /// Called when option 1 is selected for an election card
    /// </summary>
    private void OnOption1Selected()
    {
        // Apply the effect of option 1
        ApplyElectionOption(card.Option1Effect);
        
        // Hide the options panel
        if (optionsPanel != null)
            optionsPanel.SetActive(false);
    }
    
    /// <summary>
    /// Called when option 2 is selected for an election card
    /// </summary>
    private void OnOption2Selected()
    {
        // Apply the effect of option 2
        ApplyElectionOption(card.Option2Effect);
        
        // Hide the options panel
        if (optionsPanel != null)
            optionsPanel.SetActive(false);
    }
    
    /// <summary>
    /// Apply the effect of an election option
    /// </summary>
    private void ApplyElectionOption(EarthCardEffectType effectType)
    {
        // Create a temporary card with the selected effect
        EarthCard tempCard = new EarthCard
        {
            Name = card.Name,
            Description = card.Description,
            Tier = card.Tier,
            EffectType = effectType
        };
        
        // Resolve the effect
        EarthCardManager cardManager = EarthCardManager.Instance;
        if (cardManager != null)
        {
            // We would need a public method in EarthCardManager to resolve a card effect directly
            // cardManager.ResolveCardEffect(tempCard);
            
            // For now, just log the selected option
            Debug.Log($"Selected option with effect: {effectType}");
        }
    }
}