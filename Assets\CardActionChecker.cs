using UnityEngine;

public class CardActionChecker : MonoBehaviour
{
    private CardData cardData;
    private CardGlowEffect glowEffect;
    
    private void Awake()
    {
        glowEffect = GetComponent<CardGlowEffect>();
        if (glowEffect == null)
        {
            glowEffect = gameObject.AddComponent<CardGlowEffect>();
        }
    }
    
    public void Initialize(CardData data)
    {
        cardData = data;
        UpdateGlowState();
    }
    
    public void UpdateGlowState()
    {
        if (cardData == null || glowEffect == null)
            return;
        
        bool canGlow = ShouldCardGlow();
        glowEffect.SetCanGlow(canGlow);
    }
    
    private bool ShouldCardGlow()
    {
        // All cards should glow on hover regardless of location or activation abilities
        return true;
    }

    /// <summary>
    /// Check if the card should glow (public accessor for UI system)
    /// </summary>
    public bool ShouldGlow()
    {
        return ShouldCardGlow();
    }
}