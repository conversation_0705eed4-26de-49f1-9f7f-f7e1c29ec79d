using System.Collections.Generic;
using System.Linq;
using UnityEngine; // Added for GameObject

public class WonderEffect : ICardEffect
{
    private string wonderName;
    private int victoryPoints;
    private bool isPerTurn;
    private List<Resource> buildCost;
    
    public WonderEffect(string wonderName, int victoryPoints, bool isPerTurn, List<Resource> buildCost)
    {
        this.wonderName = wonderName;
        this.victoryPoints = victoryPoints;
        this.isPerTurn = isPerTurn;
        this.buildCost = buildCost;
    }
    
    public bool CanExecute(Player player, GameObject location, Dictionary<string, object> parameters)
    {
        // Check resources
        foreach (var resource in buildCost)
        {
            if (player.GetResourceAmount(location, resource.Type) < resource.Amount)
                return false;
        }
        
        // Wonders require Advanced Assembler
        bool hasAdvancedAssembler = parameters.ContainsKey("hasAdvancedAssembler") && 
            (bool)parameters["hasAdvancedAssembler"];
        
        if (!hasAdvancedAssembler && location.name != "Earth")
            return false;
        
        return true;
    }
    
    public bool Execute(Player player, GameObject location, Dictionary<string, object> parameters)
    {
        if (!CanExecute(player, location, parameters))
            return false;
            
        // Deduct resources
        foreach (var resource in buildCost)
        {
            player.UseResource(location, resource.Type, resource.Amount);
        }
        
        // Create wonder module
        Module wonder = new Module
        {
            Name = wonderName,
            Type = ModuleType.Wonder,
            IsWonder = true,
            VictoryPointValue = victoryPoints
        };
        
        player.AddModule(location, wonder);
        
        // If not per turn, award VP immediately
        if (!isPerTurn)
        {
            player.AddVictoryPoints(victoryPoints);
        }
        
        return true;
    }
    
    public string GetDescription()
    {
        return $"Wonder: {victoryPoints} VP" + (isPerTurn ? " per turn" : "");
    }
}