using System.Collections.Generic;
using System.Linq;
using UnityEngine; // Added for GameObject

// Technology upgrade effects
public class TechnologyUpgradeEffect : ICardEffect
{
    private string name;
    private TechnologyEffectsManager.TechEffectType effectType;
    private float effectValue;
    
    public TechnologyUpgradeEffect(string name, TechnologyEffectsManager.TechEffectType effectType, float effectValue)
    {
        this.name = name;
        this.effectType = effectType;
        this.effectValue = effectValue;
    }
    
    public bool CanExecute(Player player, GameObject location, Dictionary<string, object> parameters)
    {
        // Technology upgrades are passive, so they can always "execute"
        return true;
    }
    
    public bool Execute(Player player, GameObject location, Dictionary<string, object> parameters)
    {
        // Technology upgrades are applied automatically when acquired
        // The effect might be registered with TechnologyEffectsManager
        TechnologyEffectsManager effectsManager = TechnologyEffectsManager.Instance;
        if (effectsManager != null)
        {
            effectsManager.AddTechnologyEffect(name, effectType, effectValue);
            effectsManager.UpdateTechnologyEffects();
        }
        
        Debug.Log($"Player {player.PlayerId} applied technology upgrade: {name}");
        return true;
    }
    
    public string GetDescription()
    {
        switch (effectType)
        {
            case TechnologyEffectsManager.TechEffectType.PowerBonus:
                return $"+{effectValue} additional power from all power sources";
                
            case TechnologyEffectsManager.TechEffectType.OreIceExtractionBonus:
                return $"+{effectValue} additional Ore/Ice per deposit when mining";
                
            case TechnologyEffectsManager.TechEffectType.NonMetalExtractionBonus:
                return $"+{effectValue} additional Carbon/Silicon per deposit when mining";
                
            case TechnologyEffectsManager.TechEffectType.RareEarthsExtractionBonus:
                return $"+{effectValue} additional Rare Earths per deposit when mining";
                
            case TechnologyEffectsManager.TechEffectType.DeltaVPerFuelBonus:
                return $"+{effectValue} delta-v per fuel for all ships";
                
            case TechnologyEffectsManager.TechEffectType.ActionBonus:
                return $"+{effectValue} action per turn";
                
            default:
                return "Technology upgrade";
        }
    }
}