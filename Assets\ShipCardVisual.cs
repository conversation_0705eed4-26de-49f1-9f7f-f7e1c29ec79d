using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;

/// <summary>
/// Handles the visual representation of a ship card in a player's play area
/// </summary>
public class ShipCardVisual : MonoBehaviour
{
    [Header("Card References")]
    [SerializeField] private TextMeshProUGUI nameText;
    [SerializeField] private TextMeshP<PERSON>UGUI statsText;
    [SerializeField] private Image shipImage;
    [SerializeField] private TextMeshProUGUI strengthText;
    [SerializeField] private Transform cargoGridParent;
    [SerializeField] private GridLayoutGroup cargoGrid;
    
    [Header("Resource Icons")]
    [SerializeField] private GameObject oreIconPrefab;
    [SerializeField] private GameObject iceIconPrefab;
    [SerializeField] private GameObject carbonIconPrefab;
    [SerializeField] private GameObject siliconIconPrefab;
    [SerializeField] private GameObject rareEarthsIconPrefab;
    [SerializeField] private GameObject alloyIconPrefab;
    [SerializeField] private GameObject fuelIconPrefab;
    [SerializeField] private GameObject grapheneIconPrefab;
    [SerializeField] private GameObject ceramicsIconPrefab;
    [SerializeField] private GameObject microchipsIconPrefab;
    
    // Reference to the ship this card represents
    private Ship ship;
    
    // Dictionary mapping resource types to their icon prefabs
    private Dictionary<ResourceType, GameObject> resourcePrefabs = new Dictionary<ResourceType, GameObject>();
    
    // Cargo grid cells
    private List<GameObject> cargoGridCells = new List<GameObject>();
    private int cargoWidth = 2;
    private int cargoHeight = 4;
    
    private void Awake()
    {
        // Set up resource prefab dictionary
        resourcePrefabs[ResourceType.Ore] = oreIconPrefab;
        resourcePrefabs[ResourceType.Ice] = iceIconPrefab;
        resourcePrefabs[ResourceType.Carbon] = carbonIconPrefab;
        resourcePrefabs[ResourceType.Silicon] = siliconIconPrefab;
        resourcePrefabs[ResourceType.RareEarths] = rareEarthsIconPrefab;
        resourcePrefabs[ResourceType.Alloys] = alloyIconPrefab;
        resourcePrefabs[ResourceType.Fuel] = fuelIconPrefab;
        resourcePrefabs[ResourceType.Graphene] = grapheneIconPrefab;
        resourcePrefabs[ResourceType.Ceramics] = ceramicsIconPrefab;
        resourcePrefabs[ResourceType.Microchips] = microchipsIconPrefab;
    }
    
    /// <summary>
    /// Set up this card to represent a ship
    /// </summary>
    public void SetupCard(Ship ship)
    {
        this.ship = ship;
        
        // Set the name
        if (nameText != null)
            nameText.text = ship.Name;
            
        // Set the stats text
        if (statsText != null)
        {
            string cargoSizeText = GetCargoSizeText(ship.CargoCapacity);
            statsText.text = $"Cargo {cargoSizeText}\n{ship.DeltaVPerFuel} delta-v per fuel";
        }
        
        // Set the strength indicator if applicable
        if (strengthText != null)
        {
            strengthText.text = ship.Strength > 0 ? $"Strength {ship.Strength}" : "";
            strengthText.gameObject.SetActive(ship.Strength > 0);
        }
        
        // Set image based on ship type
        if (shipImage != null)
        {
            // Could use a dictionary or resource manager to get the appropriate sprite
            string imagePath = $"ShipIcons/{ship.Name.Replace(" ", "")}";
            Sprite sprite = Resources.Load<Sprite>(imagePath);
            if (sprite != null)
                shipImage.sprite = sprite;
        }
        
        // Setup cargo grid
        SetupCargoGrid(ship.CargoCapacity);
        
        // Update cargo display with current cargo
        UpdateCargoDisplay();
    }
    
    /// <summary>
    /// Get a text representation of cargo size (e.g., "2x4")
    /// </summary>
    private string GetCargoSizeText(int totalCapacity)
    {
        // Try to determine width and height from total capacity
        // This assumes cargo is always in a rectangular grid
        
        // Start with default values
        cargoWidth = 2;
        cargoHeight = totalCapacity / 2;
        
        // Some common cargo configurations
        if (totalCapacity == 2) { cargoWidth = 1; cargoHeight = 2; }     // 1x2
        else if (totalCapacity == 4) { cargoWidth = 2; cargoHeight = 2; } // 2x2
        else if (totalCapacity == 8) { cargoWidth = 2; cargoHeight = 4; } // 2x4
        else if (totalCapacity == 16) { cargoWidth = 4; cargoHeight = 4; } // 4x4
        
        return $"{cargoWidth}x{cargoHeight}";
    }
    
    /// <summary>
    /// Set up the cargo grid based on ship capacity
    /// </summary>
    private void SetupCargoGrid(int totalCapacity)
    {
        // Clear existing grid
        foreach (GameObject cell in cargoGridCells)
        {
            if (cell != null)
                Destroy(cell);
        }
        cargoGridCells.Clear();
        
        // Skip if we don't have a cargo grid parent
        if (cargoGridParent == null)
            return;
            
        // Get cargo width/height
        GetCargoSizeText(totalCapacity);
        
        // Configure grid layout
        if (cargoGrid != null)
        {
            float cellSize = 20f;
            cargoGrid.cellSize = new Vector2(cellSize, cellSize);
            cargoGrid.constraintCount = cargoWidth;
        }
        
        // Create grid cells
        for (int i = 0; i < totalCapacity; i++)
        {
            GameObject cell = new GameObject($"CargoCell_{i}");
            cell.transform.SetParent(cargoGridParent, false);
            
            // Add a background image
            Image cellImage = cell.AddComponent<Image>();
            cellImage.color = new Color(0.2f, 0.2f, 0.2f, 0.5f);
            
            // Add to our list
            cargoGridCells.Add(cell);
        }
    }
    
    /// <summary>
    /// Update the cargo display based on the ship's current cargo
    /// </summary>
    private void UpdateCargoDisplay()
    {
        if (ship == null)
            return;
            
        // Clear existing cargo icons
        foreach (GameObject cell in cargoGridCells)
        {
            // Remove any resource icons but keep the cell itself
            foreach (Transform child in cell.transform)
            {
                Destroy(child.gameObject);
            }
        }
        
        // Get the ship's cargo
        Dictionary<ResourceType, int> cargo = ship.GetAllCargo();
        
        // Place resource icons in the grid
        int cellIndex = 0;
        foreach (var kvp in cargo)
        {
            ResourceType type = kvp.Key;
            int count = kvp.Value;
            
            for (int i = 0; i < count; i++)
            {
                if (cellIndex < cargoGridCells.Count)
                {
                    AddResourceIconToCell(type, cargoGridCells[cellIndex]);
                    cellIndex++;
                }
                else
                {
                    Debug.LogWarning($"Ship {ship.Name} has more cargo than cargo grid cells!");
                    break;
                }
            }
        }
    }
    
    /// <summary>
    /// Add a resource icon to a cargo grid cell
    /// </summary>
    private void AddResourceIconToCell(ResourceType type, GameObject cell)
    {
        if (!resourcePrefabs.ContainsKey(type) || resourcePrefabs[type] == null)
        {
            Debug.LogWarning($"No prefab found for resource type {type}");
            return;
        }
        
        // Create the icon as a child of the cell
        GameObject icon = Instantiate(resourcePrefabs[type], cell.transform);
        
        // Adjust size to fit cell
        RectTransform rectTransform = icon.GetComponent<RectTransform>();
        if (rectTransform != null)
        {
            rectTransform.anchorMin = Vector2.zero;
            rectTransform.anchorMax = Vector2.one;
            rectTransform.sizeDelta = Vector2.zero;
            rectTransform.anchoredPosition = Vector2.zero;
        }
    }
    
    /// <summary>
    /// Get the ship this card represents
    /// </summary>
    public Ship GetShip()
    {
        return ship;
    }
    
    /// <summary>
    /// Update this card after the ship's cargo has changed
    /// </summary>
    public void OnCargoChanged()
    {
        UpdateCargoDisplay();
    }
}