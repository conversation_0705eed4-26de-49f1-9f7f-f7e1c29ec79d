using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;

/// <summary>
/// Manages resource UI elements in the card detail display
/// </summary>
public class CardResourceUIManager
{
    // References to UI components
    private Transform resourceListParent;
    private GameObject resourceItemPrefab;
    private TextMeshProUGUI resourceSectionHeaderCost;
    private TextMeshProUGUI resourceSectionHeaderEffect;
    
    // Resource manager instance
    private CardResourceManager resourceManager;
    
    // Active resource items (for cleanup)
    private List<GameObject> activeResourceItems = new List<GameObject>();
    
    // Current card data
    private CardData currentCard;
    
    public CardResourceUIManager(
        Transform resourceListParent,
        GameObject resourceItemPrefab,
        TextMeshProUGUI resourceSectionHeaderCost,
        TextMeshProUGUI resourceSectionHeaderEffect,
        CardResourceManager resourceManager)
    {
        this.resourceListParent = resourceListParent;
        this.resourceItemPrefab = resourceItemPrefab;
        this.resourceSectionHeaderCost = resourceSectionHeaderCost;
        this.resourceSectionHeaderEffect = resourceSectionHeaderEffect;
        this.resourceManager = resourceManager;
        
        // Create section headers if they don't exist
        CreateSectionHeadersIfNeeded();
    }
    
    /// <summary>
    /// Set the current card data
    /// </summary>
    public void SetCardData(CardData cardData)
    {
        this.currentCard = cardData;
    }
    
    /// <summary>
    /// Create section headers if they don't exist
    /// </summary>
    private void CreateSectionHeadersIfNeeded()
    {
        // Create cost header if it doesn't exist
        if (resourceSectionHeaderCost == null && resourceListParent != null)
        {
            GameObject costHeader = new GameObject("ResourceSectionHeaderCost");
            costHeader.transform.SetParent(resourceListParent, false);
            resourceSectionHeaderCost = costHeader.AddComponent<TextMeshProUGUI>();
            resourceSectionHeaderCost.text = "";
            resourceSectionHeaderCost.fontSize = 16;
            resourceSectionHeaderCost.fontStyle = FontStyles.Bold;
            resourceSectionHeaderCost.color = new Color(0.9f, 0.8f, 0.3f); // Gold color
            resourceSectionHeaderCost.alignment = TextAlignmentOptions.Left;
            
            // Create a RectTransform to position the header
            RectTransform rectTransform = costHeader.GetComponent<RectTransform>();
            rectTransform.anchorMin = new Vector2(0, 1);
            rectTransform.anchorMax = new Vector2(1, 1);
            rectTransform.pivot = new Vector2(0.5f, 1);
            rectTransform.sizeDelta = new Vector2(0, 20);
        }
        
        // Create effect header if it doesn't exist
        if (resourceSectionHeaderEffect == null && resourceListParent != null)
        {
            GameObject effectHeader = new GameObject("ResourceSectionHeaderEffect");
            effectHeader.transform.SetParent(resourceListParent, false);
            resourceSectionHeaderEffect = effectHeader.AddComponent<TextMeshProUGUI>();
            resourceSectionHeaderEffect.text = "";
            resourceSectionHeaderEffect.fontSize = 16;
            resourceSectionHeaderEffect.fontStyle = FontStyles.Bold;
            resourceSectionHeaderEffect.color = new Color(0.3f, 0.9f, 0.8f); // Teal color
            resourceSectionHeaderEffect.alignment = TextAlignmentOptions.Left;
            
            // Create a RectTransform to position the header
            RectTransform rectTransform = effectHeader.GetComponent<RectTransform>();
            rectTransform.anchorMin = new Vector2(0, 1);
            rectTransform.anchorMax = new Vector2(1, 1);
            rectTransform.pivot = new Vector2(0.5f, 1);
            rectTransform.sizeDelta = new Vector2(0, 20);
        }
    }

    /// <summary>
    /// Update the resource lists based on the current card data
    /// </summary>
    public void UpdateResourceLists(bool hasAerobraking = false, bool hasHighRadiation = false)
    {
        // Clear existing resource items
        ClearResourceItems();

        // Check if we have a valid card and UI components
        if (currentCard == null || resourceListParent == null || resourceItemPrefab == null)
        {
            if (resourceListParent != null)
                resourceListParent.gameObject.SetActive(false);

            return;
        }

        // Check if we have resources to display
        bool hasBuildCost = currentCard.BuildCost != null && currentCard.BuildCost.Count > 0;
        bool hasEffectResources = (currentCard.InputResources != null && currentCard.InputResources.Count > 0) ||
                                (currentCard.OutputResources != null && currentCard.OutputResources.Count > 0);

        // Add special check for initiative cards
        bool isInitiative = currentCard.IsInitiative;

        // Add special check for aerobraking and high radiation
        bool showAerobraking = hasAerobraking;
        bool showHighRadiation = hasHighRadiation;

        // Check for world resource deposits (for planet cards)
        List<ResourceDeposit> deposits = null;
        bool hasDeposits = false;
        
        // Check if this is a world card by looking at the card type
        if (currentCard.Type == "World" && currentCard.Name != null)
        {
            // Find the planet body for this world
            WorldManager worldManager = WorldManager.Instance;
            if (worldManager != null)
            {
                GameObject celestialBody = worldManager.GetCelestialBodyByName(currentCard.Name);
                if (celestialBody != null)
                {
                    PlanetBody planetBody = celestialBody.GetComponent<PlanetBody>();
                    if (planetBody != null)
                    {
                        deposits = planetBody.GetDeposits();
                        hasDeposits = deposits != null && deposits.Count > 0;
                    }
                }
            }
        }

        bool hasResources = hasBuildCost || hasEffectResources || isInitiative || hasDeposits || showAerobraking || showHighRadiation;

        // If there are no resources to display, hide the resource list
        if (!hasResources)
        {
            if (resourceListParent != null)
                resourceListParent.gameObject.SetActive(false);

            if (resourceSectionHeaderCost != null)
                resourceSectionHeaderCost.gameObject.SetActive(false);

            if (resourceSectionHeaderEffect != null)
                resourceSectionHeaderEffect.gameObject.SetActive(false);

            return;
        }

        // Show the resource list
        resourceListParent.gameObject.SetActive(true);

        // Create position offset to properly order the items
        int resourceCount = 0;

        // HashSet to track which resource types we've already displayed
        HashSet<ResourceType> displayedResources = new HashSet<ResourceType>();

        // Show/hide section headers based on content
        if (resourceSectionHeaderCost != null)
        {
            resourceSectionHeaderCost.gameObject.SetActive(hasBuildCost || isInitiative || hasDeposits || showAerobraking || showHighRadiation);

            // If this is visible, it should be first in the list
            if (hasBuildCost || isInitiative || hasDeposits || showAerobraking || showHighRadiation)
            {
                resourceSectionHeaderCost.transform.SetSiblingIndex(resourceCount++);
                resourceSectionHeaderCost.text = hasDeposits ? "" : "";
            }
        }

        // Show deposit resources for world cards first
        if (hasDeposits)
        {
            // Create a dictionary to count resource types
            Dictionary<ResourceType, int> depositCounts = new Dictionary<ResourceType, int>();
            
            // Count the deposits by type
            foreach (ResourceDeposit deposit in deposits)
            {
                if (!depositCounts.ContainsKey(deposit.ResourceType))
                {
                    depositCounts[deposit.ResourceType] = 0;
                }
                depositCounts[deposit.ResourceType]++;
            }
            
            // Add resource items for each deposit type
            foreach (var kvp in depositCounts)
            {
                // Create resource for deposit
                Resource depositResource = new Resource(kvp.Key, kvp.Value);
                
                // Add to tracking set
                displayedResources.Add(kvp.Key);
                
                // Create resource item
                GameObject itemObj = CreateResourceItem(depositResource);
                if (itemObj != null)
                {
                    itemObj.transform.SetSiblingIndex(resourceCount++);
                }
            }
        }

        // Special handling for aerobraking - add reminder text
        if (showAerobraking)
        {
            // Create a "fake" resource item for the aerobrake reminder
            Resource aerobrakeResource = new Resource(ResourceType.Unknown, 0);
            GameObject itemObj = CreateResourceItem(aerobrakeResource, false, "\nAerobrake - It costs just 1 delta-v to move from low orbit to surface here");
            if (itemObj != null)
            {
                itemObj.transform.SetSiblingIndex(resourceCount++);
            }
        }
        
        // Special handling for high radiation - add reminder text
        if (showHighRadiation)
        {
            // Create a "fake" resource item for the high radiation reminder
            Resource radiationResource = new Resource(ResourceType.Unknown, 0);
            GameObject itemObj = CreateResourceItem(radiationResource, false, "\nHigh Radiation - Your first activation or movement here each turn costs 1 extra action");
            if (itemObj != null)
            {
                itemObj.transform.SetSiblingIndex(resourceCount++);
            }
        }

        // Special handling for initiative cards - add reminder text
        if (isInitiative)
        {
            // Create a "fake" resource item for the initiative reminder
            Resource initiativeResource = new Resource(ResourceType.Unknown, 0);
            GameObject itemObj = CreateResourceItem(initiativeResource, false, "Initiative: Take this card to immediately perform the described action");
            if (itemObj != null)
            {
                itemObj.transform.SetSiblingIndex(resourceCount++);
            }
        }

        // Create a resource item for each resource in the build cost
        if (hasBuildCost)
        {
            foreach (var resource in currentCard.BuildCost)
            {
                // Only continue if it's a valid resource type
                if (resource.Type == ResourceType.Unknown)
                    continue;

                // Add this resource type to our tracking set
                displayedResources.Add(resource.Type);

                GameObject itemObj = CreateResourceItem(resource);
                if (itemObj != null)
                {
                    itemObj.transform.SetSiblingIndex(resourceCount++);
                }
            }
        }

        // Add the effect header after the cost resources
        if (resourceSectionHeaderEffect != null)
        {
            resourceSectionHeaderEffect.gameObject.SetActive(hasEffectResources);

            if (hasEffectResources)
            {
                resourceSectionHeaderEffect.transform.SetSiblingIndex(resourceCount++);
                resourceSectionHeaderEffect.text = "";
            }
        }

        // Create resource items for inputs (with negative amounts)
        if (currentCard.InputResources != null)
        {
            foreach (var resource in currentCard.InputResources)
            {
                // Skip if resource type is unknown
                if (resource.Type == ResourceType.Unknown)
                    continue;

                // Skip if we've already displayed this resource type in the build cost
                if (displayedResources.Contains(resource.Type))
                    continue;

                // Add to our tracking set
                displayedResources.Add(resource.Type);

                // Create a copy of the resource with negative amount for display
                Resource displayResource = new Resource(resource.Type, -resource.Amount);
                GameObject itemObj = CreateResourceItem(displayResource, true);
                if (itemObj != null)
                {
                    itemObj.transform.SetSiblingIndex(resourceCount++);
                }
            }
        }

        // Create resource items for outputs
        if (currentCard.OutputResources != null)
        {
            foreach (var resource in currentCard.OutputResources)
            {
                // Skip if resource type is unknown
                if (resource.Type == ResourceType.Unknown)
                    continue;

                // Skip if we've already displayed this resource type
                if (displayedResources.Contains(resource.Type))
                    continue;

                // Add to our tracking set
                displayedResources.Add(resource.Type);

                GameObject itemObj = CreateResourceItem(resource, true);
                if (itemObj != null)
                {
                    itemObj.transform.SetSiblingIndex(resourceCount++);
                }
            }
        }
    }

    
    /// <summary>
    /// Create a resource item UI element
    /// </summary>
    private GameObject CreateResourceItem(Resource resource, bool isFromEffect = false, string customProducerText = null)
    {
        // Skip if resource type is unknown and no custom text is provided
        if (resource.Type == ResourceType.Unknown && customProducerText == null)
            return null;
            
        // Get the resource icon
        Sprite iconSprite = resourceManager.GetResourceIcon(resource.Type);
        
        // Skip creating the resource item if the icon is missing and we have no custom text
        if (iconSprite == null && customProducerText == null)
        {
            Debug.LogWarning($"Skipping resource item creation for {resource.Type} - no icon available");
            return null;
        }
        
        // Create the resource item
        GameObject itemObj = GameObject.Instantiate(resourceItemPrefab, resourceListParent);
        activeResourceItems.Add(itemObj);

        // Get references to UI components in the prefab
        Image resourceIcon = itemObj.transform.Find("ResourceIcon")?.GetComponent<Image>();
        TextMeshProUGUI resourceName = itemObj.transform.Find("ResourceName")?.GetComponent<TextMeshProUGUI>();
        TextMeshProUGUI amountText = itemObj.transform.Find("AmountText")?.GetComponent<TextMeshProUGUI>();
        TextMeshProUGUI producersText = itemObj.transform.Find("ProducersText")?.GetComponent<TextMeshProUGUI>();

        // Set the resource icon
        if (resourceIcon != null)
        {
            if (iconSprite != null)
            {
                resourceIcon.sprite = iconSprite;
                resourceIcon.color = Color.white; // Reset color
            }
            else
            {
                // For initiative cards or custom items without icons
                resourceIcon.sprite = null;
                resourceIcon.color = new Color(0, 0, 0, 0); // Make invisible
            }
        }

        // Set the resource name using display name from resources.txt
        if (resourceName != null)
        {
            if (customProducerText != null)
            {
                // For initiative cards or other special items
                resourceName.text = ""; // Hide the name for custom text items
            }
            else
            {
                CardResourceManager.ResourceInfo resourceInfo = resourceManager.GetResourceInfo(resource.Type);
                resourceName.text = resourceInfo.DisplayName;
            }
        }

        // Set the amount with appropriate sign for effect resources
        if (amountText != null)
        {
            if (customProducerText != null)
            {
                // Hide amount for initiative cards or other special items
                amountText.text = "";
            }
            else if (isFromEffect)
            {
                if (resource.Amount < 0)
                {
                    // For consumed resources, show as negative
                    amountText.text = $"{resource.Amount}";
                }
                else
                {
                    // For produced resources, show with a plus
                    amountText.text = $"+{resource.Amount}";
                }
            }
            else
            {
                // For cost resources, show as a count with "x"
                amountText.text = $"x{resource.Amount}";
            }
        }

        // Set the production text if available in the resource data
        if (producersText != null)
        {
            if (customProducerText != null)
            {
                // For initiative cards or other custom text items
                producersText.text = customProducerText;
            }
            else
            {
                CardResourceManager.ResourceInfo resourceInfo = resourceManager.GetResourceInfo(resource.Type);
                // Display the text exactly as written in resources.txt (which should include the resource name)
                producersText.text = $"{resourceInfo.DisplayName}: {resourceInfo.ProductionText}";
            }
        }
        
        return itemObj;
    }
    
    /// <summary>
    /// Clear all resource items
    /// </summary>
    public void ClearResourceItems()
    {
        // Destroy all active resource items
        foreach (var item in activeResourceItems)
        {
            if (item != null)
                GameObject.Destroy(item);
        }
        activeResourceItems.Clear();
    }
}