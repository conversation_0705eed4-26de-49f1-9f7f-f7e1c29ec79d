using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class UndoManager : MonoBehaviour
{
    // Singleton pattern
    public static UndoManager Instance { get; private set; }

    // The stack of undoable actions
    private Stack<UndoableAction> undoStack = new Stack<UndoableAction>();

    // Optional redo functionality
    private Stack<UndoableAction> redoStack = new Stack<UndoableAction>();

    // Counter for tracking number of undoable actions
    private int actionCounter = 0;

    // Direct reference to the undo button
    private Button undoButton;

    // Reference to GameManager and GameUI
    private GameManager gameManager;
    private GameUI gameUI;

    private void Awake()
    {
        // Singleton setup
        if (Instance != null && Instance != this)
        {
            Destroy(gameObject);
            return;
        }

        Instance = this;
    }

    private void Start()
    {
        // Find references
        gameManager = GameManager.Instance;
        gameUI = FindFirstObjectByType<GameUI>();

        // Find the undo button directly
        if (gameUI != null)
        {
            undoButton = gameUI.GetUndoButton();
        }

        if (undoButton == null)
        {
            // Direct fallback: try to find the button by name
            Button[] allButtons = GameObject.FindObjectsByType<Button>(FindObjectsSortMode.None);
            foreach (Button button in allButtons)
            {
                if (button != null && button.name.Contains("Undo"))
                {
                    undoButton = button;
                    break;
                }
            }
        }

        // Ensure the undo button starts disabled
        if (undoButton != null)
        {
            undoButton.interactable = false;
        }
        else
        {
            Debug.LogError("Could not find undo button!");
        }
    }

    private void Update()
    {
        // Constantly enforce the undo button state just to be absolutely sure
        if (undoButton != null)
        {
            undoButton.interactable = actionCounter > 0;
        }
    }

    /// <summary>
    /// Register an undoable action
    /// </summary>
    public void RegisterAction(UndoableAction action)
    {
        // Clear redo stack when a new action is performed
        redoStack.Clear();

        // Add action to undo stack
        undoStack.Push(action);

        // Increment action counter
        actionCounter++;

        // Directly enable the undo button
        if (undoButton != null)
        {
            undoButton.interactable = true;
        }
    }

    /// <summary>
    /// Undo the most recent action
    /// </summary>
    public void Undo()
    {
        if (undoStack.Count == 0 || actionCounter <= 0)
            return;

        // Check if the detail panel is currently active
        bool detailPanelWasActive = false;
        CardDetailDisplay detailDisplay = CardDetailDisplay.Instance;
        GameObject detailPanel = null;
        bool wasProcessorAction = false;
        bool wasMarketAction = false;

        if (detailDisplay != null)
        {
            detailPanel = detailDisplay.GetDetailPanel();
            if (detailPanel != null)
            {
                detailPanelWasActive = detailPanel.activeSelf;

                // Check if the next action is a process resource action or market transaction
                if (undoStack.Peek() is ProcessResourceAction)
                {
                    wasProcessorAction = true;
                }
                else if (undoStack.Peek() is MarketTransactionAction)
                {
                    wasMarketAction = true;
                }
            }
        }

        // Get the most recent action
        UndoableAction action = undoStack.Pop();

        // Execute the undo
        action.Undo();

        // Add to redo stack
        redoStack.Push(action);

        // Decrement action counter
        actionCounter--;

        // Directly update the undo button state
        if (undoButton != null)
        {
            undoButton.interactable = actionCounter > 0;
        }

        // Update the GameManager state if needed
        if (gameManager != null && gameUI != null)
        {
            gameUI.RefreshUI();
        }

        // Restore detail panel if it was active
        if (detailPanelWasActive && detailPanel != null)
        {
            detailPanel.SetActive(true);

            // If it was a processor action, show processor options again
            if (wasProcessorAction)
            {
                // Get the CardActionButtonManager and refresh the processor options
                if (detailDisplay != null)
                {
                    System.Reflection.FieldInfo buttonManagerField = typeof(CardDetailDisplay).GetField("buttonManager",
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                    if (buttonManagerField != null)
                    {
                        var buttonManager = buttonManagerField.GetValue(detailDisplay);
                        if (buttonManager != null)
                        {
                            // Use reflection to call ShowProcessorOptions
                            System.Reflection.MethodInfo showProcessorMethod = buttonManager.GetType().GetMethod("ShowProcessorOptions",
                                System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                            if (showProcessorMethod != null)
                            {
                                showProcessorMethod.Invoke(buttonManager, null);
                            }
                        }
                    }
                }
            }
            // If it was a market action, show market again and exit selection mode
            else if (wasMarketAction)
            {
                EarthMarketUI marketUI = detailDisplay.earthMarketUI;
                if (marketUI != null)
                {
                    marketUI.ShowMarket();
                    marketUI.ExitSelectionMode();
                }
            }

            // Always refresh resource display for both types
            if (wasProcessorAction || wasMarketAction)
            {
                WorldResourceDisplay resourceDisplay = detailDisplay.GetComponent<WorldResourceDisplay>();
                if (resourceDisplay != null)
                {
                    // Get Earth
                    WorldManager worldManager = WorldManager.Instance;
                    if (worldManager != null)
                    {
                        GameObject earth = worldManager.GetCelestialBodyByName("Earth");
                        if (earth != null && gameManager != null)
                        {
                            resourceDisplay.DisplayWorldResources(earth, gameManager.CurrentPlayerIndex);
                        }
                    }
                }
            }
            else
            {
                if (detailDisplay != null)
                {
                    detailDisplay.Hide();
                }
            }
        }
    }

    public void Redo()
    {
        if (redoStack.Count == 0)
            return;

        // Check if the detail panel is currently active
        bool detailPanelWasActive = false;
        CardDetailDisplay detailDisplay = CardDetailDisplay.Instance;
        GameObject detailPanel = null;
        bool wasProcessorAction = false;
        bool wasMarketAction = false;

        if (detailDisplay != null)
        {
            detailPanel = detailDisplay.GetDetailPanel();
            if (detailPanel != null)
            {
                detailPanelWasActive = detailPanel.activeSelf;

                // Check if the next action is a process resource action or market transaction
                if (redoStack.Peek() is ProcessResourceAction)
                {
                    wasProcessorAction = true;
                }
                else if (redoStack.Peek() is MarketTransactionAction)
                {
                    wasMarketAction = true;
                }
            }
        }

        // Get the most recently undone action
        UndoableAction action = redoStack.Pop();

        // Execute the redo
        action.Redo();

        // Add back to undo stack
        undoStack.Push(action);

        // Increment action counter
        actionCounter++;

        // Directly enable the undo button
        if (undoButton != null)
        {
            undoButton.interactable = true;
        }

        // Also update the GameManager state if needed
        if (gameManager != null && gameUI != null)
        {
            gameUI.RefreshUI();
        }

        // Restore detail panel if it was active
        if (detailPanelWasActive && detailPanel != null)
        {
            detailPanel.SetActive(true);

            // If it was a processor action, show processor options again
            if (wasProcessorAction)
            {
                // Get the CardActionButtonManager and refresh the processor options
                if (detailDisplay != null)
                {
                    System.Reflection.FieldInfo buttonManagerField = typeof(CardDetailDisplay).GetField("buttonManager",
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                    if (buttonManagerField != null)
                    {
                        var buttonManager = buttonManagerField.GetValue(detailDisplay);
                        if (buttonManager != null)
                        {
                            // Use reflection to call ShowProcessorOptions
                            System.Reflection.MethodInfo showProcessorMethod = buttonManager.GetType().GetMethod("ShowProcessorOptions",
                                System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                            if (showProcessorMethod != null)
                            {
                                showProcessorMethod.Invoke(buttonManager, null);
                            }
                        }
                    }
                }
            }
            // If it was a market action, show market again and exit selection mode
            else if (wasMarketAction)
            {
                EarthMarketUI marketUI = detailDisplay.earthMarketUI;
                if (marketUI != null)
                {
                    marketUI.ShowMarket();
                    marketUI.ExitSelectionMode();
                }
            }

            // Always refresh resource display for both types
            if (wasProcessorAction || wasMarketAction)
            {
                WorldResourceDisplay resourceDisplay = detailDisplay.GetComponent<WorldResourceDisplay>();
                if (resourceDisplay != null)
                {
                    // Get Earth
                    WorldManager worldManager = WorldManager.Instance;
                    if (worldManager != null)
                    {
                        GameObject earth = worldManager.GetCelestialBodyByName("Earth");
                        if (earth != null && gameManager != null)
                        {
                            resourceDisplay.DisplayWorldResources(earth, gameManager.CurrentPlayerIndex);
                        }
                    }
                }
            }
            else
            {
                if (detailDisplay != null)
                {
                    detailDisplay.Hide();
                }
            }
        }
    }


    public bool CanUndo()
    {
        return actionCounter > 0;
    }

    public bool CanRedo()
    {
        return redoStack.Count > 0;
    }

    public void ClearStacks()
    {
        undoStack.Clear();
        redoStack.Clear();
        actionCounter = 0;

        // Directly disable the undo button
        if (undoButton != null)
        {
            undoButton.interactable = false;
        }

        GameLogManager logManager = GameLogManager.Instance;
        if (logManager != null)
        {
            logManager.ClearAllLogs();
        }
    }
}