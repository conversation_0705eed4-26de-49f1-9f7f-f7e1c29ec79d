using System.Collections.Generic;
using UnityEngine;

public class StealResourceEffect : ICardEffect
{
    private string name;
    
    public StealResourceEffect(string name)
    {
        this.name = name;
    }
    
    public bool CanExecute(Player player, GameObject location, Dictionary<string, object> parameters)
    {
        return true;
    }
    
    public bool Execute(Player player, GameObject location, Dictionary<string, object> parameters)
    {
        // This would need game-specific logic to handle stealing
        // For now, just log that it was used
        Debug.Log($"Player {player.PlayerId} used {name}");
        return true;
    }
    
    public string GetDescription()
    {
        return "Initiative: Choose another player. Take up to 2 resources of your choice from that player.";
    }
}