using System.Collections.Generic;

[System.Serializable]
public class CardData
{
    public string Name;
    public string Type;
    public string SubType;
    public int Tier;
    public int ScienceCost;
    public List<Resource> BuildCost;
    public string Effect;
    public Dictionary<string, object> EffectParams;
    
    // Add count property
    public int Count;
    
    // Special flags
    public bool IsWonder;
    public bool IsInitiative;
    public bool AutoActivate;
    public bool RequiresAdvancedAssembler;
    public int PowerOutput;
    public int PowerRequired;

    // Store parsed resources from effect text
    public List<Resource> InputResources = new List<Resource>(); // Resources consumed (negative)
    public List<Resource> OutputResources = new List<Resource>(); // Resources produced (positive)

    public string SolEncyclopedia;
    public string RealQuote;
    public string SciFiQuote;
}