using UnityEngine;

public class ClickableTechnologyCard3D : MonoBehaviour
{
    private CardData cardData;
    private int cardIndex;
    
    [SerializeField] private float hoverHeight = 0.1f;
    private Vector3 originalPosition;
    
    private void Start()
    {
        originalPosition = transform.localPosition;
    }
    
    public void Initialize(CardData data, int index)
    {
        cardData = data;
        cardIndex = index;
    }
    
    private void OnMouseEnter()
    {
        transform.localPosition = originalPosition + Vector3.up * hoverHeight;
    }
    
    private void OnMouseExit()
    {
        transform.localPosition = originalPosition;
    }
    
    private void OnMouseDown()
    {
    }
}