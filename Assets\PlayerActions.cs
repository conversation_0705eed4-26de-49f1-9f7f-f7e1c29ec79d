using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Handles player actions like mining, processing, building, and managing cards
/// </summary>
public class PlayerActions : MonoBehaviour
{
    // Singleton pattern
    public static PlayerActions Instance { get; private set; }
    
    private GameManager gameManager;
    private WorldManager worldManager;
    private ResourceManager resourceManager;
    private PlayAreaManager playAreaManager;
    private TechnologyDeckManager deckManager;
    private TechnologyEffectsManager effectsManager;

    private void Awake()
    {
        // Singleton setup
        if (Instance != null && Instance != this)
        {
            Destroy(gameObject);
            return;
        }
        
        Instance = this;
    }
    
    private void Start()
    {
        // Get references to other managers
        gameManager = GameManager.Instance;
        worldManager = WorldManager.Instance;
        resourceManager = ResourceManager.Instance;
        playAreaManager = PlayAreaManager.Instance;
        deckManager = TechnologyDeckManager.Instance;
        effectsManager = TechnologyEffectsManager.Instance;

        if (gameManager == null) Debug.LogError("GameManager not found!");
        if (worldManager == null) Debug.LogError("WorldManager not found!");
        if (resourceManager == null) Debug.LogError("ResourceManager not found!");
        if (playAreaManager == null) Debug.LogError("PlayAreaManager not found!");
        if (deckManager == null) Debug.LogError("TechnologyDeckManager not found!");
        if (effectsManager == null) Debug.LogError("TechnologyEffectsManager not found!");
    }
    
    /// <summary>
    /// Extract resources from a location
    /// </summary>
    public bool ExtractLocation(int playerId, GameObject location)
    {
        if (gameManager == null)
            return false;
        
        Player player = GetPlayer(playerId);
        if (player == null)
            return false;
            
        // Check if the player has any extractors at this location
        List<Module> extractors = new List<Module>();
        foreach (Module module in player.GetModulesOnPlanet(location))
        {
            if (module.Type == ModuleType.Extractor && !module.IsActive)
            {
                extractors.Add(module);
            }
        }
        
        if (extractors.Count == 0)
        {
            Debug.LogWarning($"Player {playerId} has no inactive extractors at {location.name}");
            return false;
        }
        
        // Check power availability
        int availablePower = player.GetAvailablePower(location);
        int usedPower = player.GetUsedPower(location);
        int remainingPower = availablePower - usedPower;
        
        if (remainingPower <= 0)
        {
            Debug.LogWarning($"No available power at {location.name} to activate extractors");
            return false;
        }
        
        // Use an action
        if (!gameManager.UseAction())
        {
            Debug.LogWarning("No actions remaining!");
            return false;
        }
        
        // Activate as many extractors as we have power for
        int activated = 0;
        foreach (Module extractor in extractors)
        {
            if (remainingPower >= extractor.PowerRequired)
            {
                extractor.IsActive = true;
                remainingPower -= extractor.PowerRequired;
                activated++;
                
                // Perform extraction
                PerformExtraction(player, location);
            }
        }
        
        Debug.Log($"Player {playerId} activated {activated} extractors at {location.name}");
        
        // Update the play area
        if (playAreaManager != null)
        {
            playAreaManager.RefreshPlayerPlayArea(playerId);
        }
        
        return true;
    }
    
    /// <summary>
    /// Perform resource extraction at a location
    /// </summary>
    private void PerformExtraction(Player player, GameObject location)
    {
        PlanetBody planetBody = location.GetComponent<PlanetBody>();
        if (planetBody == null)
        {
            Debug.LogWarning($"{location.name} does not have a PlanetBody component!");
            return;
        }
        
        // Mine the resources
        planetBody.Mine(player);
        
        // Update player's play area
        if (playAreaManager != null)
        {
            foreach (ResourceDeposit deposit in planetBody.GetDeposits())
            {
                int amount = player.GetResourceAmount(location, deposit.ResourceType);
                playAreaManager.OnPlayerResourcesChanged(player.PlayerId, location, deposit.ResourceType, amount);
            }
        }
    }
    
    /// <summary>
    /// Process resources using a processor module
    /// </summary>
    public bool ProcessResources(int playerId, GameObject location, int processorIndex)
    {
        if (gameManager == null || resourceManager == null)
            return false;
            
        Player player = GetPlayer(playerId);
        if (player == null)
            return false;
            
        // Get processor modules at this location
        List<Module> processors = new List<Module>();
        foreach (Module module in player.GetModulesOnPlanet(location))
        {
            if (module.Type == ModuleType.Processor)
                processors.Add(module);
        }
        
        if (processorIndex < 0 || processorIndex >= processors.Count)
        {
            Debug.LogWarning($"Invalid processor index {processorIndex}");
            return false;
        }
        
        Module processor = processors[processorIndex];
        
        // Check if processor can be activated
        if (!player.CanActivateModule(location, processor))
        {
            Debug.LogWarning($"Cannot activate processor {processor.Name} at {location.name}");
            return false;
        }
        
        // Use an action
        if (!gameManager.UseAction())
        {
            Debug.LogWarning("No actions remaining!");
            return false;
        }
        
        // Activate processor and process resources
        processor.IsActive = true;
        bool success = resourceManager.ProcessResources(player, location, processor);
        processor.IsActive = false; // Deactivate after use
        
        if (!success)
        {
            gameManager.RefundAction();
            return false;
        }
        
        Debug.Log($"Player {playerId} processed resources using {processor.Name} at {location.name}");
        
        // Update play area
        if (playAreaManager != null)
        {
            playAreaManager.RefreshPlayerPlayArea(playerId);
        }
        
        return true;
    }
    
    /// <summary>
    /// Build a module or facility at a location
    /// </summary>
    public bool BuildStructure(int playerId, GameObject location, TechnologyCardData technologyData)
    {
        if (gameManager == null || playAreaManager == null)
            return false;
            
        Player player = GetPlayer(playerId);
        if (player == null)
            return false;
            
        BlueprintTechnologyCardData blueprint = technologyData as BlueprintTechnologyCardData;
        if (blueprint == null)
        {
            Debug.LogWarning($"{technologyData.cardName} is not a blueprint");
            return false;
        }
        
        // Check if it's a buildable structure type
        bool isModule = technologyData.cardDescription.Contains("module", System.StringComparison.OrdinalIgnoreCase);
        bool isFacility = technologyData.cardDescription.Contains("facility", System.StringComparison.OrdinalIgnoreCase);
        
        if (!isModule && !isFacility)
        {
            Debug.LogWarning($"{technologyData.cardName} is not a buildable structure");
            return false;
        }
        
        // Check if player has required assembler
        bool hasAdvancedAssembler = HasAdvancedAssembler(player, location);
        
        if (isFacility && !hasAdvancedAssembler)
        {
            Debug.LogWarning($"Facilities require an Advanced Assembler at {location.name}");
            return false;
        }
        
        // Check if player has any assembler for modules
        if (isModule && !hasAdvancedAssembler && !HasBasicAssembler(player, location))
        {
            Debug.LogWarning($"No assembler available at {location.name} to build modules");
            return false;
        }
        
        // Check resources
        if (!HasRequiredResources(player, location, blueprint.buildCost))
        {
            Debug.LogWarning($"Insufficient resources to build {technologyData.cardName} at {location.name}");
            return false;
        }
        
        // Use an action
        if (!gameManager.UseAction())
        {
            Debug.LogWarning("No actions remaining!");
            return false;
        }
        
        // Deduct resources
        DeductResources(player, location, blueprint.buildCost);
        
        // Create the module/facility
        Module newStructure = CreateModuleFromBlueprint(blueprint);
        player.AddModule(location, newStructure);
        
        // Update play area
        if (playAreaManager != null)
        {
            playAreaManager.OnPlayerAcquiredModule(playerId, newStructure, location);
        }
        
        Debug.Log($"Player {playerId} built {technologyData.cardName} at {location.name}");
        
        return true;
    }
    
    /// <summary>
    /// Build a ship at a location
    /// </summary>
    public bool BuildShip(int playerId, GameObject location, TechnologyCardData technologyData)
    {
        if (gameManager == null || playAreaManager == null)
            return false;
            
        Player player = GetPlayer(playerId);
        if (player == null)
            return false;
            
        BlueprintTechnologyCardData blueprint = technologyData as BlueprintTechnologyCardData;
        if (blueprint == null || blueprint.blueprintType != BlueprintType.Ship)
        {
            Debug.LogWarning($"{technologyData.cardName} is not a ship blueprint");
            return false;
        }
        
        // Check for Advanced Assembler
        if (!HasAdvancedAssembler(player, location))
        {
            Debug.LogWarning($"Ships require an Advanced Assembler at {location.name}");
            return false;
        }
        
        // Check ship limit
        if (player.ShipsInPlay >= player.MaxShips)
        {
            Debug.LogWarning($"Player {playerId} has reached maximum ships ({player.MaxShips})");
            return false;
        }
        
        // Check resources
        if (!HasRequiredResources(player, location, blueprint.buildCost))
        {
            Debug.LogWarning($"Insufficient resources to build {technologyData.cardName} at {location.name}");
            return false;
        }
        
        // Use an action
        if (!gameManager.UseAction())
        {
            Debug.LogWarning("No actions remaining!");
            return false;
        }
        
        // Deduct resources
        DeductResources(player, location, blueprint.buildCost);
        
        // Create the ship
        Ship newShip = CreateShipFromBlueprint(blueprint);
        newShip.CurrentLocation = location;
        player.AddShip(newShip);
        player.ShipsInPlay++;
        
        // Update play area
        if (playAreaManager != null)
        {
            playAreaManager.OnPlayerBuiltShip(playerId, newShip, location);
        }
        
        // Register with planet
        PlanetBody planetBody = location.GetComponent<PlanetBody>();
        if (planetBody != null)
        {
            planetBody.RegisterShipArrival(newShip, player);
        }
        
        Debug.Log($"Player {playerId} built {technologyData.cardName} at {location.name}");
        
        return true;
    }
    
    /// <summary>
    /// Take a card from the card row
    /// </summary>
    public bool TakeCardFromRow(int playerId, int cardIndex)
    {
        if (gameManager == null || deckManager == null)
            return false;
            
        Player player = GetPlayer(playerId);
        if (player == null)
            return false;
        
        // Get action cost
        int actionCost = deckManager.GetCardCost(cardIndex);
        
        // Check actions
        if (gameManager.ActionsRemaining < actionCost)
        {
            Debug.LogWarning($"Insufficient actions. Need {actionCost}, have {gameManager.ActionsRemaining}");
            return false;
        }
        
        // Use actions
        for (int i = 0; i < actionCost; i++)
        {
            gameManager.UseAction();
        }
        
        // Take the card
        CardData cardData = deckManager.TakeCardFromRowNoRedraw(cardIndex);
        if (cardData == null)
        {
            // Refund actions if card couldn't be taken
            for (int i = 0; i < actionCost; i++)
            {
                gameManager.RefundAction();
            }
            return false;
        }
        
        // Handle card based on type
        HandleTakenCard(player, cardData);
        
        return true;
    }
    
    /// <summary>
    /// Handle a card that was taken from the row
    /// </summary>
    private void HandleTakenCard(Player player, CardData cardData)
    {
        // Initiative cards are already resolved by TechnologyDeckManager
        if (cardData.IsInitiative)
        {
            Debug.Log($"Initiative card {cardData.Name} already resolved");
            return;
        }
        
        // Convert to appropriate data type and add to player
        if (cardData.IsWonder)
        {
            // Wonders go to player's hand to be built later
            // Could implement a hand system here
            Debug.Log($"Wonder {cardData.Name} added to Player {player.PlayerId}'s hand");
        }
        else
        {
            // Technology cards
            TechnologyCardData techData;
            
            if (cardData.SubType.ToLower().Contains("module") || 
                cardData.SubType.ToLower().Contains("facility") || 
                cardData.SubType.ToLower().Contains("ship"))
            {
                techData = new BlueprintTechnologyCardData(cardData);
            }
            else
            {
                techData = new TechnologyCardData(cardData);
            }
            
            // Technology goes to hand to be developed later
            Debug.Log($"Technology {cardData.Name} added to Player {player.PlayerId}'s hand");
        }
        
        // Update play area
        if (playAreaManager != null)
        {
            playAreaManager.RefreshPlayerPlayArea(player.PlayerId);
        }
    }
    
    /// <summary>
    /// Develop a technology from hand
    /// </summary>
    public bool DevelopTechnology(int playerId, TechnologyCardData technology, GameObject location)
    {
        if (gameManager == null)
            return false;
            
        Player player = GetPlayer(playerId);
        if (player == null)
            return false;
        
        // Check science cost
        if (player.ScienceValue < technology.scienceValue)
        {
            Debug.LogWarning($"Insufficient science. Need {technology.scienceValue}, have {player.ScienceValue}");
            return false;
        }
        
        // Check resource costs
        if (!HasRequiredResources(player, location, technology.resourceCost))
        {
            Debug.LogWarning($"Insufficient resources to develop {technology.cardName} at {location.name}");
            return false;
        }
        
        // Use an action
        if (!gameManager.UseAction())
        {
            Debug.LogWarning("No actions remaining!");
            return false;
        }
        
        // Deduct costs
        player.ScienceValue -= technology.scienceValue;
        DeductResources(player, location, technology.resourceCost);
        
        // Add to player's technologies
        player.AddTechnology(technology);
        
        // Apply technology effects
        if (effectsManager != null)
        {
            effectsManager.OnTechnologyAcquired(technology);
        }
        
        // Update play area
        if (playAreaManager != null)
        {
            playAreaManager.OnPlayerAcquiredTechnology(playerId, null); // TODO: We need access to actual TechnologyCard
        }
        
        Debug.Log($"Player {playerId} developed {technology.cardName}");
        
        return true;
    }
    
    /// <summary>
    /// Move a ship from one location to another
    /// </summary>
    public bool MoveShip(int playerId, Ship ship, GameObject destination)
    {
        if (gameManager == null || playAreaManager == null)
            return false;
            
        Player player = GetPlayer(playerId);
        if (player == null)
            return false;
            
        // Verify ownership
        if (!player.GetShips().Contains(ship))
        {
            Debug.LogWarning($"Player {playerId} doesn't own ship {ship.Name}");
            return false;
        }
        
        GameObject currentLocation = ship.CurrentLocation;
        if (currentLocation == null || currentLocation == destination)
        {
            Debug.LogWarning($"Invalid movement from {currentLocation?.name} to {destination.name}");
            return false;
        }
        
        
        // Calculate delta-V
        
        // Check fuel

        // Special movement rules for specific ships
        if (!CheckSpecialMovementRules(ship, currentLocation, destination))
        {
            return false;
        }
        
        // Use action (most ships)
        bool needsAction = ship.Name != "Ferry"; // Ferry doesn't use actions
        
        if (needsAction && !gameManager.UseAction())
        {
            Debug.LogWarning("No actions remaining!");
            return false;
        }
        
        // Consume fuel
        
        // Move the ship
        ship.CurrentLocation = destination;
        
        // Update planet registries
        UpdatePlanetRegistries(ship, player, currentLocation, destination);
        
        // Update play area
        if (playAreaManager != null)
        {
            playAreaManager.OnPlayerMovedShip(playerId, ship, currentLocation, destination);
        }
        
        // Handle survey probes
        if (ship.IsConsumedOnSurvey)
        {
            HandleSurveyProbe(player, ship, destination);
        }
        
        Debug.Log($"Player {playerId} moved {ship.Name} from {currentLocation.name} to {destination.name}");
        
        return true;
    }
    
    // Helper methods
    
    private bool HasAdvancedAssembler(Player player, GameObject location)
    {
        // Earth has an Advanced Assembler
        if (location.name == "Earth")
            return true;
            
        // Check player's modules
        foreach (Module module in player.GetModulesOnPlanet(location))
        {
            if (module.Name == "Advanced Assembler")
                return true;
        }
        
        return false;
    }
    
    private bool HasBasicAssembler(Player player, GameObject location)
    {
        foreach (Module module in player.GetModulesOnPlanet(location))
        {
            if (module.Name == "Assembler Module")
                return true;
        }
        
        // Universal Builder can also act as assembler
        foreach (Ship ship in player.GetShipsAtLocation(location))
        {
            if (ship.Name == "Universal Builder")
                return true;
        }
        
        return false;
    }
    
    private bool HasRequiredResources(Player player, GameObject location, List<Resource> costs)
    {
        foreach (Resource resource in costs)
        {
            if (player.GetResourceAmount(location, resource.Type) < resource.Amount)
                return false;
        }
        return true;
    }
    
    private void DeductResources(Player player, GameObject location, List<Resource> costs)
    {
        foreach (Resource resource in costs)
        {
            player.UseResource(location, resource.Type, resource.Amount);
        }
    }
    
    private Module CreateModuleFromBlueprint(BlueprintTechnologyCardData blueprint)
    {
        Module module = new Module
        {
            Name = blueprint.cardName,
            Type = ConvertBlueprintTypeToModuleType(blueprint.blueprintType),
            PowerOutput = blueprint.powerOutput,
            PowerRequired = GetPowerRequirement(blueprint),
            IsWonder = blueprint.cardDescription.Contains("wonder", System.StringComparison.OrdinalIgnoreCase),
            VictoryPointValue = GetVictoryPointValue(blueprint),
            ProcessorDescription = blueprint.cardDescription
        };
        
        return module;
    }
    
    private Ship CreateShipFromBlueprint(BlueprintTechnologyCardData blueprint)
    {
        Ship ship = new Ship
        {
            Name = blueprint.cardName,
            DeltaVPerFuel = blueprint.deltaVPerFuel,
            CargoCapacity = blueprint.cargoCapacity,
            Strength = blueprint.shipStrength,
            IsConsumedOnSurvey = blueprint.isConsumedOnSurvey
        };
        
        return ship;
    }
    
    private ModuleType ConvertBlueprintTypeToModuleType(BlueprintType blueprintType)
    {
        switch (blueprintType)
        {
            case BlueprintType.Power:
                return ModuleType.Power;
            case BlueprintType.Extractor:
                return ModuleType.Extractor;
            case BlueprintType.Processor:
                return ModuleType.Processor;
            default:
                return ModuleType.Other;
        }
    }
    
    private int GetPowerRequirement(BlueprintTechnologyCardData blueprint)
    {
        // Extract power requirement from description or set default
        if (blueprint.blueprintType == BlueprintType.Power)
            return 0;
        if (blueprint.blueprintType == BlueprintType.Extractor)
            return 1;
        if (blueprint.blueprintType == BlueprintType.Processor)
            return blueprint.powerOutput > 0 ? blueprint.powerOutput : 1;
            
        return 0;
    }
    
    private int GetVictoryPointValue(BlueprintTechnologyCardData blueprint)
    {
        // TODO: Extract VP value from description
        if (blueprint.cardDescription.Contains("wonder", System.StringComparison.OrdinalIgnoreCase))
            return 1; // Default for wonders
        
        return 0;
    }
    
    private bool CheckSpecialMovementRules(Ship ship, GameObject from, GameObject to)
    {
        // Graphene Dirigible rules
        if (ship.Name == "Graphene Dirigible")
        {
            // Can only move between atmospheric bodies and their orbits
            bool fromAtmospheric = IsAtmosphericLocation(from);
            bool toAtmospheric = IsAtmosphericLocation(to);

            if (!fromAtmospheric && !toAtmospheric)
            {
                Debug.LogWarning("Graphene Dirigible can only move between atmospheric bodies and their orbits");
                return false;
            }
        }

        // Add more special rules as needed

        return true;
    }

    /// <summary>
    /// Check if a location is atmospheric (either an atmospheric body or orbit around one)
    /// </summary>
    private bool IsAtmosphericLocation(GameObject location)
    {
        // Check if it's directly an atmospheric body
        PlanetBody planetBody = location.GetComponent<PlanetBody>();
        if (planetBody != null)
        {
            return planetBody.Type == PlanetBody.BodyType.Atmospheric;
        }

        // Check if it's an orbit around an atmospheric body
        OrbitLocation orbitLocation = location.GetComponent<OrbitLocation>();
        if (orbitLocation != null && orbitLocation.ParentCelestialBody != null)
        {
            PlanetBody parentBody = orbitLocation.ParentCelestialBody.GetComponent<PlanetBody>();
            return parentBody != null && parentBody.Type == PlanetBody.BodyType.Atmospheric;
        }

        return false;
    }
    
    private void UpdatePlanetRegistries(Ship ship, Player player, GameObject from, GameObject to)
    {
        // Handle departure from celestial body
        PlanetBody fromBody = from.GetComponent<PlanetBody>();
        if (fromBody != null)
        {
            fromBody.RegisterShipDeparture(ship, player);
        }

        // Handle departure from orbit location
        OrbitLocation fromOrbit = from.GetComponent<OrbitLocation>();
        if (fromOrbit != null)
        {
            fromOrbit.RegisterShipDeparture(ship, player);
        }

        // Handle arrival at celestial body
        PlanetBody toBody = to.GetComponent<PlanetBody>();
        if (toBody != null)
        {
            toBody.RegisterShipArrival(ship, player);
        }

        // Handle arrival at orbit location
        OrbitLocation toOrbit = to.GetComponent<OrbitLocation>();
        if (toOrbit != null)
        {
            toOrbit.RegisterShipArrival(ship, player);
        }
    }
    
    private void HandleSurveyProbe(Player player, Ship ship, GameObject destination)
    {
        bool surveyed = false;

        // Handle survey of celestial body
        PlanetBody planetBody = destination.GetComponent<PlanetBody>();
        if (planetBody != null && !planetBody.HasBeenSurveyed)
        {
            // Perform survey
            planetBody.MarkAsSurveyed(player);
            surveyed = true;
        }

        // Handle survey of orbit location
        OrbitLocation orbitLocation = destination.GetComponent<OrbitLocation>();
        if (orbitLocation != null && !orbitLocation.HasBeenExplored)
        {
            // Mark orbit as explored
            orbitLocation.MarkAsExplored(player);

            // Also mark in WorldManager
            WorldManager worldManager = WorldManager.Instance;
            if (worldManager != null)
            {
                worldManager.MarkOrbitExplored(destination, player);
            }

            surveyed = true;
        }

        if (surveyed)
        {
            // Consume the ship
            player.GetShips().Remove(ship);
            player.ShipsInPlay--;

            // Remove from play area
            if (playAreaManager != null)
            {
                playAreaManager.OnShipConsumed(player.PlayerId, ship, destination);
            }

            Debug.Log($"Survey probe consumed at {destination.name}");
        }
    }
    
    /// <summary>
    /// Trade with the Earth market
    /// </summary>
    public bool TradeWithMarket(int playerId, ResourceType type, int amount, bool isBuying)
    {
        if (gameManager == null || resourceManager == null)
            return false;
            
        Player player = GetPlayer(playerId);
        if (player == null)
            return false;
            
        GameObject earth = worldManager.GetCelestialBodyByName("Earth");
        if (earth == null)
        {
            Debug.LogError("Earth not found!");
            return false;
        }
        
        // Use an action
        if (!gameManager.UseAction())
        {
            Debug.LogWarning("No actions remaining!");
            return false;
        }
        
        bool success = isBuying ? 
            resourceManager.BuyFromMarket(player, type, amount) : 
            resourceManager.SellToMarket(player, type, amount);
        
        if (!success)
        {
            gameManager.RefundAction();
            return false;
        }
        
        // Update play area
        if (playAreaManager != null)
        {
            playAreaManager.OnPlayerResourcesChanged(playerId, earth, type, 
                player.GetResourceAmount(earth, type));
        }
        
        Debug.Log($"Player {playerId} {(isBuying ? "bought" : "sold")} {amount} {type}");
        
        return true;
    }
    
    /// <summary>
    /// Get a player by ID
    /// </summary>
    private Player GetPlayer(int playerId)
    {
        if (gameManager == null)
            return null;
            
        if (playerId < 0 || playerId >= gameManager.Players.Count)
        {
            Debug.LogWarning($"Invalid player ID: {playerId}");
            return null;
        }
        
        return gameManager.Players[playerId];
    }
}