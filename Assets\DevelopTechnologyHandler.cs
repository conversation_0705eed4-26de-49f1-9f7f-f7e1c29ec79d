using UnityEngine;

public static class DevelopTechnologyHandler
{
    public static void HandleDevelopTechnology(CardData cardData, CardDetailDisplay cardDetailDisplay)
    {
        GameManager gameManager = GameManager.Instance;
        if (gameManager?.CurrentPlayer == null || cardData == null) 
        {
            cardDetailDisplay.Hide();
            return;
        }

        Player currentPlayer = gameManager.CurrentPlayer;
        
        if (currentPlayer.ScienceValue < cardData.ScienceCost || gameManager.ActionsRemaining <= 0)
        {
            cardDetailDisplay.Hide();
            return;
        }

        // Convert CardData to TechnologyCardData
        TechnologyCardData techData = cardData.SubType?.ToLower().Contains("module") == true ||
                                     cardData.SubType?.ToLower().Contains("facility") == true ||
                                     cardData.SubType?.ToLower().Contains("ship") == true
            ? new BlueprintTechnologyCardData(cardData)
            : new TechnologyCardData(cardData);

        // Create undoable action
        DevelopTechnologyAction developAction = new DevelopTechnologyAction(
            cardData, techData, cardData.ScienceCost, currentPlayer.PlayerId);

        // Execute changes
        gameManager.SetActionsRemaining(gameManager.ActionsRemaining - 1);
        currentPlayer.ScienceValue -= cardData.ScienceCost;
        currentPlayer.AddTechnology(techData);

        GameLogManager logManager = GameLogManager.Instance;
        if (logManager != null)
        {
            logManager.AddLog($"Player {currentPlayer.PlayerId + 1} developed {cardData.Name}");
        }

        // Apply effects
        TechnologyEffectsManager effectsManager = TechnologyEffectsManager.Instance;
        effectsManager?.OnTechnologyAcquired(techData);

        // Update UI
        PlayAreaManager playAreaManager = PlayAreaManager.Instance;
        playAreaManager?.RefreshPlayerPlayArea(currentPlayer.PlayerId);

        CardToHandSystem cardToHandSystem = CardToHandSystem.Instance;
        cardToHandSystem?.RemoveCardFromHand(cardData);

        UndoManager undoManager = UndoManager.Instance;
        undoManager?.RegisterAction(developAction);

        cardDetailDisplay.Hide();

        GameUI gameUI = UnityEngine.Object.FindFirstObjectByType<GameUI>();
        gameUI?.RefreshUI();
    }
}