using UnityEngine;
using System.Collections;

/// <summary>
/// Initializes the orbit system and ensures all components work together
/// </summary>
public class OrbitSystemInitializer : MonoBehaviour
{
    [Header("Initialization Settings")]
    [SerializeField] private bool initializeOnStart = true;
    [SerializeField] private bool findExistingOrbits = true;
    [SerializeField] private float initializationDelay = 2f;
    
    [Header("Status")]
    [SerializeField] private bool isInitialized = false;
    
    private void Start()
    {
        if (initializeOnStart)
        {
            StartCoroutine(InitializeOrbitSystem());
        }
    }
    
    [ContextMenu("Initialize Orbit System")]
    public void InitializeOrbitSystemManual()
    {
        StartCoroutine(InitializeOrbitSystem());
    }
    
    private IEnumerator InitializeOrbitSystem()
    {
        // Wait for other systems to initialize
        yield return new WaitForSeconds(initializationDelay);

        // Step 1: Ensure WorldManager is ready
        if (!EnsureWorldManagerReady())
        {
            Debug.LogError("WorldManager not ready! Orbit system initialization failed.");
            yield break;
        }

        // Step 2: Find and initialize existing orbit locations
        if (findExistingOrbits)
        {
            InitializeExistingOrbitLocations();
            yield return new WaitForSeconds(0.5f);
        }

        // Step 3: Ensure SolarSystemGraph is ready
        if (!EnsureSolarSystemGraphReady())
        {
            Debug.LogError("SolarSystemGraph not ready! Orbit system initialization failed.");
            yield break;
        }

        // Step 4: Initialize orbit locations in WorldManager
        InitializeWorldManagerOrbits();
        yield return new WaitForSeconds(0.5f);

        // Step 5: Orbit navigation paths already exist in delta-V map
        // No additional navigation setup needed
        yield return new WaitForSeconds(0.5f);

        // Step 6: Initialize click handlers for orbital locations
        InitializeOrbitClickHandlers();
        yield return new WaitForSeconds(0.5f);

        // Step 7: Verify system integrity
        if (VerifySystemIntegrity())
        {
            isInitialized = true;
        }
        else
        {
            Debug.LogError("✗ Orbit System initialization failed verification!");
        }
    }
    
    private bool EnsureWorldManagerReady()
    {
        WorldManager worldManager = WorldManager.Instance;
        if (worldManager == null)
        {
            Debug.LogError("WorldManager instance not found!");
            return false;
        }

        // Check if celestial bodies are loaded
        var celestialBodies = worldManager.GetAllCelestialBodies();
        if (celestialBodies.Count == 0)
        {
            return false;
        }

        return true;
    }
    
    private void InitializeExistingOrbitLocations()
    {
        // Find or create orbit initializer
        OrbitLocationInitializer initializer = FindFirstObjectByType<OrbitLocationInitializer>();
        if (initializer == null)
        {
            GameObject initializerObj = new GameObject("OrbitLocationInitializer");
            initializer = initializerObj.AddComponent<OrbitLocationInitializer>();
        }

        // Initialize existing orbits
        initializer.InitializeExistingOrbitLocations();
    }
    
    private bool EnsureSolarSystemGraphReady()
    {
        SolarSystemGraph graph = SolarSystemGraph.Instance;
        if (graph == null)
        {
            Debug.LogError("SolarSystemGraph instance not found!");
            return false;
        }

        if (!graph.IsInitialized)
        {
            return false;
        }

        return true;
    }
    
    private void InitializeWorldManagerOrbits()
    {
        WorldManager worldManager = WorldManager.Instance;
        if (worldManager != null)
        {
            worldManager.RefreshOrbitLocations();
        }
    }

    private void InitializeOrbitClickHandlers()
    {
        // Find or create orbit click initializer
        OrbitLocationClickInitializer clickInitializer = FindFirstObjectByType<OrbitLocationClickInitializer>();
        if (clickInitializer == null)
        {
            GameObject initializerObj = new GameObject("OrbitLocationClickInitializer");
            clickInitializer = initializerObj.AddComponent<OrbitLocationClickInitializer>();
        }

        // The click initializer will run its Start() method automatically
        Debug.Log("✓ Orbit click handlers initialization triggered");
    }

    // Orbit navigation is already handled by existing delta-V map
    // No additional setup needed
    
    private bool VerifySystemIntegrity()
    {
        bool allChecksPass = true;

        // Check WorldManager
        WorldManager worldManager = WorldManager.Instance;
        if (worldManager == null)
        {
            Debug.LogError("Verification failed: WorldManager not found");
            allChecksPass = false;
        }

        // Check SolarSystemGraph
        SolarSystemGraph graph = SolarSystemGraph.Instance;
        if (graph == null)
        {
            Debug.LogError("Verification failed: SolarSystemGraph not found");
            allChecksPass = false;
        }

        return allChecksPass;
    }
    

    
    [ContextMenu("Force Reinitialize")]
    public void ForceReinitialize()
    {
        isInitialized = false;
        StartCoroutine(InitializeOrbitSystem());
    }
    
    [ContextMenu("Verify System")]
    public void VerifySystemManual()
    {
        bool isValid = VerifySystemIntegrity();
        if (!isValid)
        {
            Debug.LogError("✗ System verification failed!");
        }
    }
    
    public bool IsSystemReady()
    {
        return isInitialized;
    }
}
