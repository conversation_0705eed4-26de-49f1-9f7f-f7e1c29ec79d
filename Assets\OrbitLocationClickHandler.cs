using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// <PERSON><PERSON> clicks on orbital locations in the 3D map
/// </summary>
public class OrbitLocationClickHandler : MonoBehaviour
{
    private void OnMouseUp()
    {
        // Check if the detail panel is currently open
        CardDetailDisplay detailDisplay = CardDetailDisplay.Instance;
        if (detailDisplay != null)
        {
            GameObject detailPanel = detailDisplay.GetDetailPanel();
            if (detailPanel != null && detailPanel.activeSelf)
            {
                // Panel is active, check if mouse is over it using raycasting
                Vector2 mousePosition = Input.mousePosition;
                UnityEngine.EventSystems.PointerEventData eventData = new UnityEngine.EventSystems.PointerEventData(UnityEngine.EventSystems.EventSystem.current)
                {
                    position = mousePosition
                };
                
                List<UnityEngine.EventSystems.RaycastResult> results = new List<UnityEngine.EventSystems.RaycastResult>();
                UnityEngine.EventSystems.EventSystem.current.RaycastAll(eventData, results);
                
                // Check if any of the results contain the detail panel
                foreach (var result in results)
                {
                    // Check if this is the detail panel or any of its children
                    Transform current = result.gameObject.transform;
                    while (current != null)
                    {
                        if (current.gameObject == detailPanel)
                        {
                            // Click is on the detail panel, ignore it
                            return;
                        }
                        current = current.parent;
                    }
                }
            }
        }

        // Check if this click should be suppressed for build modes
        if (AssemblerHandler.ShouldSuppressCardClick() || TechBuildHandler.ShouldSuppressCardClick())
        {
            Debug.Log("Orbit location click suppressed for build mode");
            return;
        }
        
        // Show orbital location detail
        ShowOrbitLocationDetail(gameObject, gameObject);
    }

    /// <summary>
    /// Static utility method to show an orbital location detail for any orbit location
    /// </summary>
    public static void ShowOrbitLocationDetail(GameObject orbitLocation, GameObject sourceObject)
    {
        if (orbitLocation == null || CardDetailDisplay.Instance == null)
            return;

        // Get the OrbitLocation component
        OrbitLocation orbitComponent = orbitLocation.GetComponent<OrbitLocation>();
        if (orbitComponent == null)
            return;

        // Get the orbit name
        string orbitName = orbitComponent.Name;
        if (string.IsNullOrEmpty(orbitName))
        {
            orbitName = orbitLocation.name;
        }

        // Create a basic CardData with orbit info
        CardData orbitData = new CardData
        {
            Name = orbitName,
            Type = "Orbital Location",
            SubType = "Low Orbit",
            Tier = 0,
            Effect = CreateOrbitDescription(orbitComponent),
            // Add a fake SolEncyclopedia section for flavor text
            SolEncyclopedia = $"Sol Encyclopedia: {orbitName} is an orbital location in the Solar System."
        };

        // Show card in detail display with new OrbitLocation source
        CardDetailDisplay.Instance.ShowCard(orbitData, -1, CardDetailDisplay.CardSource.OrbitLocation, sourceObject);
    }

    /// <summary>
    /// Create a description for the orbital location
    /// </summary>
    private static string CreateOrbitDescription(OrbitLocation orbitLocation)
    {
        string description = $"An orbital location";
        
        if (orbitLocation.ParentCelestialBody != null)
        {
            description += $" around {orbitLocation.ParentCelestialBody.name}";
        }
        
        description += ". Ships, modules, facilities, and wonders can be placed here.";
        
        return description;
    }
}
