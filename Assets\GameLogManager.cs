using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections;
using System.Collections.Generic;

public class GameLogManager : MonoBehaviour
{
    public static GameLogManager Instance { get; private set; }
    
    [Header("Log UI")]
    [SerializeField] private GameObject messageTextPrefab;
    [SerializeField] private Transform messageParent;
    [SerializeField] private GameObject yourScrollView;
    
    [Header("Animation")]
    [SerializeField] private RectTransform logPanelTransform;
    [SerializeField] private Button toggleButton;
    [SerializeField] private float animationDuration = 0.3f;
    [SerializeField] private AnimationCurve animationCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
    
    private AutoResizeContent contentResizer;
    private List<GameObject> logMessages = new List<GameObject>();
    private bool isExpanded = false;
    private bool isAnimating = false;
    private Vector3 hiddenPosition;
    private Vector3 shownPosition;

    private void Awake()
    {
        if (Instance != null && Instance != this)
        {
            Destroy(gameObject);
            return;
        }
        Instance = this;
        contentResizer = GameObject.Find("Content")?.GetComponent<AutoResizeContent>();
    }
    
    private void Start()
    {
        // Setup button listener
        if (toggleButton != null)
        {
            toggleButton.onClick.AddListener(ToggleLogPanel);
        }
        
        // Store initial positions
        if (logPanelTransform != null)
        {
            hiddenPosition = logPanelTransform.anchoredPosition;
            shownPosition = new Vector2(hiddenPosition.x + 410f, hiddenPosition.y);
        }
    }
    
    public void ToggleLogPanel()
    {
        if (isAnimating) return;
        
        isExpanded = !isExpanded;
        StartCoroutine(AnimatePanel());
    }
    
    private IEnumerator AnimatePanel()
    {
        isAnimating = true;
        
        Vector2 startPos = logPanelTransform.anchoredPosition;
        Vector2 targetPos = isExpanded ? shownPosition : hiddenPosition;
        
        Vector3 startRot = toggleButton.transform.eulerAngles;
        Vector3 targetRot = new Vector3(startRot.x, startRot.y, startRot.z + 180f);
        
        float elapsed = 0f;
        
        while (elapsed < animationDuration)
        {
            elapsed += Time.deltaTime;
            float t = animationCurve.Evaluate(elapsed / animationDuration);
            
            // Animate panel position
            logPanelTransform.anchoredPosition = Vector2.Lerp(startPos, targetPos, t);
            
            // Animate button rotation
            toggleButton.transform.eulerAngles = Vector3.Lerp(startRot, targetRot, t);
            
            yield return null;
        }
        
        // Ensure final values are set
        logPanelTransform.anchoredPosition = targetPos;
        toggleButton.transform.eulerAngles = targetRot;
        
        isAnimating = false;
    }

    public void AddLog(string message)
    {
        GameObject messageObj = Instantiate(messageTextPrefab, messageParent);
        TextMeshProUGUI textComponent = messageObj.GetComponentInChildren<TextMeshProUGUI>();
        if (textComponent != null)
        {
            textComponent.text = message;
        }
        logMessages.Add(messageObj);
        
        if (contentResizer != null)
        {
            contentResizer.ResizeContent();
        }
        
        if (yourScrollView != null)
        {
            StartScrolledToBottom scrollComponent = yourScrollView.GetComponent<StartScrolledToBottom>();
            if (scrollComponent != null)
            {
                scrollComponent.ScrollToBottom();
            }
        }
    }
    
    public void RemoveLastLog()
    {
        if (logMessages.Count > 0)
        {
            int lastIndex = logMessages.Count - 1;
            GameObject lastMessage = logMessages[lastIndex];
            logMessages.RemoveAt(lastIndex);
            Destroy(lastMessage);
            
            if (contentResizer != null)
            {
                contentResizer.ResizeContent();
            }
            
            if (yourScrollView != null)
            {
                StartScrolledToBottom scrollComponent = yourScrollView.GetComponent<StartScrolledToBottom>();
                if (scrollComponent != null)
                {
                    scrollComponent.ScrollToBottom();
                }
            }
        }
    }

    public void ClearAllLogs()
    {
        foreach (GameObject message in logMessages)
        {
            if (message != null)
                Destroy(message);
        }
        logMessages.Clear();
        
        if (contentResizer != null)
        {
            contentResizer.ResizeContent();
        }
        
        if (yourScrollView != null)
        {
            StartScrolledToBottom scrollComponent = yourScrollView.GetComponent<StartScrolledToBottom>();
            if (scrollComponent != null)
            {
                scrollComponent.ScrollToBottom();
            }
        }
    }
}