using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Base abstract class for undoable actions
/// </summary>
public abstract class UndoableAction
{
    public abstract void Undo();
    public abstract void Redo();
}

/// <summary>
/// Undoable action for taking a card
/// </summary>
public class TakeCardAction : UndoableAction
{
    private CardData cardData;
    private int cardIndex;
    private GameObject sourceObject;
    private int actionCost;

    public TakeCardAction(CardData cardData, int cardIndex, GameObject sourceObject, int actionCost = 1)
    {
        this.cardData = cardData;
        this.cardIndex = cardIndex;
        this.sourceObject = sourceObject;
        this.actionCost = actionCost;
    }

    public override void Undo()
    {
        // Restore the card in the deck
        TechnologyDeckManager deckManager = TechnologyDeckManager.Instance;
        if (deckManager != null)
        {
            deckManager.RestoreCard(cardIndex);
        }

        // Remove the card from the player's hand
        CardToHandSystem cardToHandSystem = CardToHandSystem.Instance;
        if (cardToHandSystem != null)
        {
            cardToHandSystem.RemoveCardFromHand(cardData);
        }

        // Restore the card visibility in the UI
        CardRowUI cardRowUI = UnityEngine.Object.FindAnyObjectByType<CardRowUI>();
        if (cardRowUI != null)
        {
            cardRowUI.RestoreCard(cardIndex);
        }

        // Restore the source object visibility if available
        if (sourceObject != null)
        {
            CardToHandSystem.Instance.RestoreCardVisibility(sourceObject);
        }

        // Refund the actions used
        GameManager gameManager = GameManager.Instance;
        if (gameManager != null)
        {
            // Refund all consumed actions
            for (int i = 0; i < actionCost; i++)
            {
                gameManager.RefundAction();
            }
        }

        // After the existing undo logic, before Update UI:
        GameLogManager logManager = GameLogManager.Instance;
        if (logManager != null)
        {
            logManager.RemoveLastLog();
        }

        // Update UI
        GameUI gameUI = UnityEngine.Object.FindAnyObjectByType<GameUI>();
        if (gameUI != null)
        {
            gameUI.RefreshUI();
        }
    }

    public override void Redo()
    {
        // Use the actions first
        GameManager gameManager = GameManager.Instance;
        if (gameManager != null)
        {
            // Use the required number of actions
            for (int i = 0; i < actionCost; i++)
            {
                gameManager.UseAction();
            }
        }

        // Retake the card
        TechnologyDeckManager deckManager = TechnologyDeckManager.Instance;
        if (deckManager != null)
        {
            deckManager.MarkCardAsTaken(cardIndex, cardData);
        }

        // Add back to player's hand
        CardToHandSystem cardToHandSystem = CardToHandSystem.Instance;
        if (cardToHandSystem != null)
        {
            cardToHandSystem.AddCardToHand(cardData);
        }

        // Mark the card as taken in the UI
        CardRowUI cardRowUI = UnityEngine.Object.FindAnyObjectByType<CardRowUI>();
        if (cardRowUI != null)
        {
            cardRowUI.MarkCardAsTaken(cardIndex);
        }

        // After the existing redo logic, before Update UI:
        GameLogManager logManager = GameLogManager.Instance;
        if (logManager != null)
        {
            logManager.AddLog($"Player {gameManager.CurrentPlayerIndex + 1} took {cardData.Name}");
        }

        // Update UI
        GameUI gameUI = UnityEngine.Object.FindAnyObjectByType<GameUI>();
        if (gameUI != null)
        {
            gameUI.RefreshUI();
        }
    }
}

/// <summary>
/// Undoable action for processing resources
/// </summary>
public class ProcessResourceAction : UndoableAction
{
    // Store inputs and outputs directly instead of the recipe reference
    private List<ResourceType> inputs;
    private List<ResourceType> outputs;
    private int playerId;
    private GameObject location;

    public ProcessResourceAction(List<ResourceType> inputs, List<ResourceType> outputs, int playerId, GameObject location)
    {
        this.inputs = new List<ResourceType>(inputs);  // Make a copy of the lists
        this.outputs = new List<ResourceType>(outputs);
        this.playerId = playerId;
        this.location = location;
    }

    public override void Undo()
    {
        GameManager gameManager = GameManager.Instance;
        if (gameManager == null || playerId >= gameManager.Players.Count)
            return;

        Player player = gameManager.Players[playerId];
        if (player == null)
            return;

        // Add back input resources
        Dictionary<ResourceType, int> inputResources = new Dictionary<ResourceType, int>();
        foreach (ResourceType type in inputs)
        {
            if (!inputResources.ContainsKey(type))
                inputResources[type] = 0;

            inputResources[type]++;
        }

        foreach (var kvp in inputResources)
        {
            player.AddResource(location, kvp.Key, kvp.Value);
        }

        // Remove output resources
        Dictionary<ResourceType, int> outputResources = new Dictionary<ResourceType, int>();
        foreach (ResourceType type in outputs)
        {
            if (!outputResources.ContainsKey(type))
                outputResources[type] = 0;

            outputResources[type]++;
        }

        foreach (var kvp in outputResources)
        {
            player.UseResource(location, kvp.Key, kvp.Value);
        }

        // Refund the action
        if (gameManager.CurrentPlayerIndex == playerId)
        {
            gameManager.RefundAction();
        }

        // After the existing undo logic:
        GameLogManager logManager = GameLogManager.Instance;
        if (logManager != null)
        {
            logManager.RemoveLastLog();
        }

        // Update UI
        PlayAreaManager playAreaManager = PlayAreaManager.Instance;
        if (playAreaManager != null)
        {
            playAreaManager.RefreshPlayerPlayArea(playerId);
        }
    }

    public override void Redo()
    {
        GameManager gameManager = GameManager.Instance;
        if (gameManager == null || playerId >= gameManager.Players.Count)
            return;

        Player player = gameManager.Players[playerId];
        if (player == null)
            return;

        // Consume input resources
        Dictionary<ResourceType, int> inputResources = new Dictionary<ResourceType, int>();
        foreach (ResourceType type in inputs)
        {
            if (!inputResources.ContainsKey(type))
                inputResources[type] = 0;

            inputResources[type]++;
        }

        foreach (var kvp in inputResources)
        {
            player.UseResource(location, kvp.Key, kvp.Value);
        }

        // Add output resources
        Dictionary<ResourceType, int> outputResources = new Dictionary<ResourceType, int>();
        foreach (ResourceType type in outputs)
        {
            if (!outputResources.ContainsKey(type))
                outputResources[type] = 0;

            outputResources[type]++;
        }

        foreach (var kvp in outputResources)
        {
            player.AddResource(location, kvp.Key, kvp.Value);
        }

        // Use an action
        if (gameManager.CurrentPlayerIndex == playerId)
        {
            gameManager.UseAction();
        }

        GameLogManager logManager = GameLogManager.Instance;
        if (logManager != null)
        {
            string inputStr = string.Join(", ", inputs);
            string outputStr = string.Join(", ", outputs);
            logManager.AddLog($"Player {playerId + 1} processed {inputStr} → {outputStr}");
        }

        // Update UI
        PlayAreaManager playAreaManager = PlayAreaManager.Instance;
        if (playAreaManager != null)
        {
            playAreaManager.RefreshPlayerPlayArea(playerId);
        }
    }
}

/// <summary>
/// Undoable action for market transactions
/// </summary>
public class MarketTransactionAction : UndoableAction
{
    private ResourceType resourceType;
    private int amount;
    private int totalCost;
    private bool wasBuyTransaction;
    private int playerId;
    private int[] priceChanges; // Track price change for each unit

    public MarketTransactionAction(ResourceType resourceType, int amount, int totalCost, bool wasBuyTransaction, int playerId, int[] priceChanges)
    {
        this.resourceType = resourceType;
        this.amount = amount;
        this.totalCost = totalCost;
        this.wasBuyTransaction = wasBuyTransaction;
        this.playerId = playerId;
        this.priceChanges = new int[priceChanges.Length];
        System.Array.Copy(priceChanges, this.priceChanges, priceChanges.Length);
    }

    public override void Undo()
    {
        GameManager gameManager = GameManager.Instance;
        ResourceManager resourceManager = ResourceManager.Instance;
        
        if (gameManager == null || resourceManager == null || playerId >= gameManager.Players.Count)
            return;

        Player player = gameManager.Players[playerId];
        WorldManager worldManager = WorldManager.Instance;
        GameObject earth = worldManager?.GetCelestialBodyByName("Earth");
        
        if (player == null || earth == null) return;

        if (wasBuyTransaction)
        {
            // Undo buy: remove resources, refund money, decrease prices
            player.UseResource(earth, resourceType, amount);
            player.Money += totalCost;
            
            // Decrease market price by the amount that was bought
            var marketPricesField = typeof(ResourceManager).GetField("marketPrices", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (marketPricesField != null)
            {
                var marketPrices = marketPricesField.GetValue(resourceManager) as System.Collections.Generic.Dictionary<ResourceType, int>;
                if (marketPrices != null && marketPrices.ContainsKey(resourceType))
                {
                    marketPrices[resourceType] = UnityEngine.Mathf.Max(1, marketPrices[resourceType] - amount);
                }
            }
        }
        else
        {
            // Undo sell: add resources back, remove money, increase prices
            player.AddResource(earth, resourceType, amount);
            player.Money -= totalCost;
            
            // Increase market price by the amount that was sold
            var marketPricesField = typeof(ResourceManager).GetField("marketPrices", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (marketPricesField != null)
            {
                var marketPrices = marketPricesField.GetValue(resourceManager) as System.Collections.Generic.Dictionary<ResourceType, int>;
                if (marketPrices != null && marketPrices.ContainsKey(resourceType))
                {
                    marketPrices[resourceType] += amount;
                }
            }
        }

        // Refund the action
        gameManager.RefundAction();

        // After the existing undo logic:
        GameLogManager logManager = GameLogManager.Instance;
        if (logManager != null)
        {
            logManager.RemoveLastLog();
        }


        // Update UI
        RefreshGameUI();
    }

    public override void Redo()
    {
        GameManager gameManager = GameManager.Instance;
        ResourceManager resourceManager = ResourceManager.Instance;
        
        if (gameManager == null || resourceManager == null || playerId >= gameManager.Players.Count)
            return;

        Player player = gameManager.Players[playerId];
        
        if (player == null) return;

        // Use an action
        gameManager.SetActionsRemaining(gameManager.ActionsRemaining - 1);

        // Execute the original transaction with suppressed logging
        if (wasBuyTransaction)
        {
            for (int i = 0; i < amount; i++)
            {
                resourceManager.BuyFromMarket(player, resourceType, 1, true);
            }
        }
        else
        {
            for (int i = 0; i < amount; i++)
            {
                resourceManager.SellToMarket(player, resourceType, 1, true);
            }
        }

        // Add single combined log message
        GameLogManager logManager = GameLogManager.Instance;
        if (logManager != null)
        {
            string action = wasBuyTransaction ? "bought" : "sold";
            logManager.AddLog($"Player {playerId + 1} {action} {amount} {resourceType} for ${totalCost}");
        }

        // Update UI
        RefreshGameUI();
    }

    private void RefreshGameUI()
    {
        PlayAreaManager playAreaManager = PlayAreaManager.Instance;
        GameManager gameManager = GameManager.Instance;

        if (playAreaManager != null && gameManager != null)
        {
            playAreaManager.RefreshPlayerPlayArea(playerId);
        }

        GameUI gameUI = UnityEngine.Object.FindFirstObjectByType<GameUI>();
        if (gameUI != null)
        {
            gameUI.RefreshUI();
        }

        // Update world resource display
        WorldResourceDisplay resourceDisplay = UnityEngine.Object.FindFirstObjectByType<WorldResourceDisplay>();
        if (resourceDisplay != null)
        {
            WorldManager worldManager = WorldManager.Instance;
            GameObject earth = worldManager?.GetCelestialBodyByName("Earth");
            if (earth != null && gameManager != null)
            {
                resourceDisplay.DisplayWorldResources(earth, gameManager.CurrentPlayer.PlayerId);
            }
        }

        // Update market UI
        EarthMarketUI marketUI = UnityEngine.Object.FindFirstObjectByType<EarthMarketUI>();
        if (marketUI != null)
        {
            marketUI.UpdatePricesAfterTransaction();
        }
    }
}