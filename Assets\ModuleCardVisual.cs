using UnityEngine;
using UnityEngine.UI;
using TMPro;

/// <summary>
/// Handles the visual representation of a module card in a player's play area
/// </summary>
public class ModuleCardVisual : MonoBehaviour
{
    [Header("Card References")]
    [SerializeField] private TextMeshP<PERSON>UGUI nameText;
    [Serialize<PERSON>ield] private TextMeshProUGUI typeText;
    [SerializeField] private TextMeshProUGUI descriptionText;
    [SerializeField] private Image moduleImage;
    [SerializeField] private GameObject powerIndicator;
    [SerializeField] private TextMeshProUGUI powerOutputText;
    [SerializeField] private TextM<PERSON>P<PERSON>UGUI powerRequiredText;
    
    // Reference to the module this card represents
    private Module module;
    private bool isActivated = false;
    
    /// <summary>
    /// Set up this card to represent a module
    /// </summary>
    public void SetupCard(Module module)
    {
        this.module = module;
        
        // Set the name and type
        if (nameText != null)
            nameText.text = module.Name;
            
        if (typeText != null)
            typeText.text = module.Type.ToString();
            
        // Set the description (you might need to add a description to the Module class)
        if (descriptionText != null)
        {
            // Could set a type-specific description
            switch (module.Type)
            {
                case ModuleType.Power:
                    descriptionText.text = $"+{module.PowerOutput} Power";
                    break;
                case ModuleType.Extractor:
                    descriptionText.text = "Activate: -1 Power, +1 each resource per deposit";
                    break;
                case ModuleType.Processor:
                    descriptionText.text = module.ProcessorDescription ?? "Processor Module";
                    break;
                case ModuleType.Wonder:
                    descriptionText.text = $"+{module.VictoryPointValue} VP" + (module.IsWonder ? " per turn" : "");
                    break;
                default:
                    descriptionText.text = "";
                    break;
            }
        }
        
        // Set image based on module type
        if (moduleImage != null)
        {
            // Could use a dictionary or resource manager to get the appropriate sprite
            string imagePath = $"ModuleIcons/{module.Type}";
            Sprite sprite = Resources.Load<Sprite>(imagePath);
            if (sprite != null)
                moduleImage.sprite = sprite;
        }
        
        // Set power indicators
        if (powerIndicator != null)
            powerIndicator.SetActive(module.Type == ModuleType.Power);
            
        if (powerOutputText != null)
        {
            powerOutputText.text = module.PowerOutput > 0 ? $"+{module.PowerOutput}" : "";
            powerOutputText.gameObject.SetActive(module.PowerOutput > 0);
        }
            
        if (powerRequiredText != null)
        {
            powerRequiredText.text = module.PowerRequired > 0 ? $"-{module.PowerRequired}" : "";
            powerRequiredText.gameObject.SetActive(module.PowerRequired > 0);
        }
    }
    
    /// <summary>
    /// Toggle the activated state of this module (for those that need to be activated)
    /// </summary>
    public void ToggleActivation(bool activated)
    {
        isActivated = activated;
        
        // Visual feedback for activation state
        if (module.Type == ModuleType.Extractor || module.Type == ModuleType.Processor)
        {
            // Could change colors or add an activation overlay
            Color moduleColor = activated ? Color.yellow : Color.white;
            
            if (moduleImage != null)
                moduleImage.color = moduleColor;
                
            if (nameText != null)
                nameText.color = activated ? Color.black : Color.white;
        }
    }
    
    /// <summary>
    /// Get the module this card represents
    /// </summary>
    public Module GetModule()
    {
        return module;
    }
    
    /// <summary>
    /// Check if this module is currently activated
    /// </summary>
    public bool IsActivated()
    {
        return isActivated;
    }
}