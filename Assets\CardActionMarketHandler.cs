using UnityEngine;

public class CardActionMarketHandler
{
    private CardDetailDisplay cardDetailDisplay;
    private EarthMarketUI earthMarketUI;

    public CardActionMarketHandler(CardDetailDisplay cardDetailDisplay, EarthMarketUI earthMarketUI)
    {
        this.cardDetailDisplay = cardDetailDisplay;
        this.earthMarketUI = earthMarketUI;
    }

    public void OnMarketButtonClicked()
    {
        // Clear all buttons and indicators first
        cardDetailDisplay.buttonManager.buttonUI.HideAllButtons();
        cardDetailDisplay.buttonManager.indicatorManager.ClearAllIndicators();

        // Hide flavor text
        if (cardDetailDisplay.flavorText != null)
            cardDetailDisplay.flavorText.gameObject.SetActive(false);

        // Move resource displays down
        MoveResourceDisplaysDown();

        // Clear reminder text
        if (cardDetailDisplay.resourceUIManager != null)
        {
            cardDetailDisplay.resourceUIManager.ClearResourceItems();
        }

        if (earthMarketUI != null)
        {
            earthMarketUI.ShowMarket();
        }
    }


    private void MoveResourceDisplaysDown()
    {
        WorldResourceDisplay resourceDisplay = cardDetailDisplay.GetComponent<WorldResourceDisplay>();
        if (resourceDisplay == null) return;

        // Move resource container down
        if (resourceDisplay.resourceContainer != null)
        {
            RectTransform resourceRect = resourceDisplay.resourceContainer as RectTransform;
            if (resourceRect != null)
            {
                Vector2 position = resourceRect.anchoredPosition;
                position.y = -100f;
                resourceRect.anchoredPosition = position;
            }
        }

        // Move resources header down
        if (resourceDisplay.resourcesHeaderText != null)
        {
            RectTransform headerRect = resourceDisplay.resourcesHeaderText.transform as RectTransform;
            if (headerRect != null)
            {
                Vector2 position = headerRect.anchoredPosition;
                position.y = -120f;
                headerRect.anchoredPosition = position;
            }
        }
    }

    public void ResetDetailPanelUI()
    {
        // Reset flavor text visibility
        if (cardDetailDisplay.flavorText != null)
            cardDetailDisplay.flavorText.gameObject.SetActive(true);

        // Reset resource container position
        WorldResourceDisplay resourceDisplay = cardDetailDisplay.GetComponent<WorldResourceDisplay>();
        if (resourceDisplay == null) return;

        if (resourceDisplay.resourceContainer != null)
        {
            RectTransform resourceRect = resourceDisplay.resourceContainer as RectTransform;
            if (resourceRect != null)
            {
                Vector2 position = resourceRect.anchoredPosition;
                position.y = 0f;
                resourceRect.anchoredPosition = position;
            }
        }

        if (resourceDisplay.resourcesHeaderText != null)
        {
            RectTransform headerRect = resourceDisplay.resourcesHeaderText.transform as RectTransform;
            if (headerRect != null)
            {
                Vector2 position = headerRect.anchoredPosition;
                position.y = -20f;
                headerRect.anchoredPosition = position;
            }
        }
    }
}