// CardEffects/ResourceProcessingEffect.cs
using System.Collections.Generic;
using System.Linq;
using UnityEngine; // Added for GameObject

public class ResourceProcessingEffect : ICardEffect
{
    private string processorName;
    private int powerRequired;
    private List<Resource> inputResources;
    private List<Resource> outputResources;
    
    public ResourceProcessingEffect(string processorName, int powerRequired, 
        List<Resource> inputs, List<Resource> outputs)
    {
        this.processorName = processorName;
        this.powerRequired = powerRequired;
        this.inputResources = inputs;
        this.outputResources = outputs;
    }
    
    public bool CanExecute(Player player, GameObject location, Dictionary<string, object> parameters)
    {
        // Check power
        if (player.GetAvailablePower(location) < powerRequired)
            return false;
            
        // Check input resources
        foreach (var resource in inputResources)
        {
            if (player.GetResourceAmount(location, resource.Type) < resource.Amount)
                return false;
        }
        
        return true;
    }
    
    public bool Execute(Player player, GameObject location, Dictionary<string, object> parameters)
    {
        if (!CanExecute(player, location, parameters))
            return false;
            
        // Consume inputs
        foreach (var resource in inputResources)
        {
            player.UseResource(location, resource.Type, resource.Amount);
        }
        
        // Produce outputs
        foreach (var resource in outputResources)
        {
            player.AddResource(location, resource.Type, resource.Amount);
        }
        
        Debug.Log($"Player {player.PlayerId} used {processorName} at {location.name}");
        return true;
    }
    
    public string GetDescription()
    {
        string inputs = string.Join(", ", inputResources.Select(r => $"{r.Amount} {r.Type}"));
        string outputs = string.Join(", ", outputResources.Select(r => $"{r.Amount} {r.Type}"));
        return $"Consumes {inputs} to produce {outputs}";
    }
}