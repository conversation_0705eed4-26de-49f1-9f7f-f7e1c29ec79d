using UnityEngine;
using System.Collections.Generic;
using System;

public class GameManager : MonoBehaviour
{
    // Static reference - initialized early in Awake with high execution order
    public static GameManager Instance { get; private set; }

    [Header("Game Settings")]
    [SerializeField] private int numberOfPlayers = 4;
    //[SerializeField] private int actionsPerTurn = 4;
    [SerializeField] private Color[] playerColors = new Color[] {
        new Color(1, 0.3f, 0.3f), // Red
        new Color(0.3f, 1, 0.3f), // Green
        new Color(0.3f, 0.3f, 1), // Blue
        new Color(1, 1, 0.3f)     // Yellow
    };

    private int currentPlayerIndex = 0;
    private int actionsRemaining;

    // Game history for undo/redo
    private Stack<GameState> gameHistory = new Stack<GameState>();
    private Stack<GameState> redoHistory = new Stack<GameState>();
    
    // References
    private GameUI gameUI;
    
    // Properties
    public List<Player> Players { get; private set; } = new List<Player>();
    public Player CurrentPlayer => Players.Count > currentPlayerIndex ? Players[currentPlayerIndex] : null;
    public int CurrentPlayerIndex => currentPlayerIndex;
    public int ActionsRemaining => actionsRemaining;
    // Update the MaxActionsPerTurn property to use the current player's value
    public int MaxActionsPerTurn => CurrentPlayer != null ? CurrentPlayer.MaxActions : 4;
    public int PlayerCount => numberOfPlayers;
    public Color[] PlayerColors => playerColors;

    // This will execute before other scripts' Awake methods
    [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.BeforeSceneLoad)]
    private static void OnBeforeSceneLoadRuntimeMethod()
    {
        // Unity doesn't actually allow script execution order to be set at runtime,
        // but this ensures GameManager is created early
    }

    private void Awake()
    {
        // Setup singleton pattern
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Debug.LogWarning("Multiple GameManager instances detected!");
            Destroy(gameObject);
            return;
        }

        // Initialize game state immediately
        InitializeGame();
    }

    private void Start()
    {
        // Find UI after all objects are initialized
        gameUI = FindFirstObjectByType<GameUI>();
        
        if (gameUI == null)
        {
            Debug.LogWarning("GameUI not found in the scene. UI won't update automatically.");
        }
        else
        {
            // Update UI based on the initialized game state
            gameUI.RefreshUI();
        }
    }

    // Update InitializeGame method
private void InitializeGame()
{
    
    // Create players
    Players.Clear();
        for (int i = 0; i < numberOfPlayers; i++)
        {
            Player player = new Player(i);
            player.MaxActions = 4; // Starting value - same for all players initially
            Players.Add(player);
    }

    // Set initial actions for the first player
    currentPlayerIndex = 0;
    actionsRemaining = Players[0].MaxActions;

    // Clear history
    gameHistory.Clear();
    redoHistory.Clear();

    // Record initial game state
    SaveGameState();
    
}

    public bool UseAction()
    {
        if (actionsRemaining <= 0)
            return false;

        // Clear redo history when a new action is taken
        redoHistory.Clear();

        // Save state before action
        SaveGameState();

        actionsRemaining--;

        // Update UI
        if (gameUI != null)
        {
            gameUI.OnActionUsed();
        }

        return true;
    }

    // Update EndTurn method to use the next player's MaxActions
    public void EndTurn()
    {
        // Clear redo history when turn ends
        redoHistory.Clear();

        // Save state before ending turn
        SaveGameState();

        // Move to next player
        currentPlayerIndex = (currentPlayerIndex + 1) % Players.Count;
        actionsRemaining = Players[currentPlayerIndex].MaxActions;

        // Update UI
        if (gameUI != null)
        {
            gameUI.UpdateUI();
        }
    }

    public bool CanUndo()
    {
        return gameHistory.Count > 1; // Need at least 2 states (current + previous)
    }

    public bool CanRedo()
    {
        return redoHistory.Count > 0;
    }

    public void Undo()
    {
        if (!CanUndo())
            return;

        // Save current state to redo history
        redoHistory.Push(gameHistory.Pop());

        // Restore previous state
        GameState previousState = gameHistory.Peek();
        RestoreGameState(previousState);

        // Update UI
        if (gameUI != null)
        {
            gameUI.UpdateUI();
        }
    }

    public void Redo()
    {
        if (!CanRedo())
            return;

        // Save current state to undo history
        SaveGameState();

        // Get state from redo history
        GameState redoState = redoHistory.Pop();
        RestoreGameState(redoState);

        // Update UI
        if (gameUI != null)
        {
            gameUI.UpdateUI();
        }
    }

    // Update SaveGameState method to save MaxActions
    private void SaveGameState()
    {
        GameState state = new GameState
        {
            CurrentPlayerIndex = currentPlayerIndex,
            ActionsRemaining = actionsRemaining,
            PlayerStates = new List<PlayerState>()
        };

        // Save player states
        foreach (Player player in Players)
        {
            state.PlayerStates.Add(new PlayerState
            {
                PlayerId = player.PlayerId,
                VictoryPoints = player.VictoryPoints,
                ScienceValue = player.ScienceValue,
                MaxActions = player.MaxActions // Save MaxActions
                // Add more player state data as needed
            });
        }

        gameHistory.Push(state);
    }

    // Update RestoreGameState method to restore MaxActions
    private void RestoreGameState(GameState state)
    {
        currentPlayerIndex = state.CurrentPlayerIndex;
        actionsRemaining = state.ActionsRemaining;

        // Restore player states
        foreach (PlayerState playerState in state.PlayerStates)
        {
            Player player = Players.Find(p => p.PlayerId == playerState.PlayerId);
            if (player != null)
            {
                player.VictoryPoints = playerState.VictoryPoints;
                player.ScienceValue = playerState.ScienceValue;
                player.MaxActions = playerState.MaxActions; // Restore MaxActions
                // Restore more player state data as needed
            }
        }
    }

    /// <summary>
    /// Sets the current player by index
    /// </summary>
    public void SetCurrentPlayer(int playerIndex)
    {
        if (playerIndex >= 0 && playerIndex < Players.Count)
        {
            currentPlayerIndex = playerIndex;
        }
        else
        {
            Debug.LogWarning($"Invalid player index: {playerIndex}");
        }
    }
    
    /// <summary>
    /// Sets the remaining actions for the current turn
    /// </summary>
    public void SetActionsRemaining(int actions)
    {
        actionsRemaining = actions;
    }
    
    /// <summary>
    /// Refunds an action to the current player
    /// </summary>
    public void RefundAction()
    {
        actionsRemaining++;
        
        // Update UI
        if (gameUI != null)
        {
            gameUI.OnActionUsed();
        }
    }
}

// Classes for game state
[Serializable]
public class GameState
{
    public int CurrentPlayerIndex;
    public int ActionsRemaining;
    public List<PlayerState> PlayerStates;
}

// Update GameState class to include MaxActions in player state
[Serializable]
public class PlayerState
{
    public int PlayerId;
    public int VictoryPoints;
    public int ScienceValue;
    public int MaxActions; 
    public int ActionsRemaining;
    public int MaxShips;
    public int ShipsInPlay;
    // Add more player state data as needed
}