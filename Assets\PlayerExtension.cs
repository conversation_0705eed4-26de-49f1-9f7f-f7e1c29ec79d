using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Extension methods for the Player class to support additional game functionality
/// </summary>
public partial class Player
{
    // Additional properties
    public int Money { get; set; } = 10; // Starting money (space credits/dollars)
    
    /// <summary>
    /// Get the available power at a location
    /// </summary>
    public int GetAvailablePower(GameObject location)
    {
        if (!planetaryModules.ContainsKey(location))
            return 0;
            
        int totalPower = 0;
        
        // Calculate total power from all power modules
        foreach (Module module in planetaryModules[location])
        {
            if (module.Type == ModuleType.Power)
            {
                totalPower += module.PowerOutput;
            }
        }
        
        // Apply technology effects if available
        TechnologyEffectsManager effectsManager = TechnologyEffectsManager.Instance;
        if (effectsManager != null)
        {
            totalPower = effectsManager.GetAdditionalPower(totalPower);
        }
        
        // If location has a Dyson Sphere wonder, all actions cost 0 power
        // This would require checking if player has a Dyson Sphere at the sun
        
        return totalPower;
    }
    
    /// <summary>
    /// Get used power at a location (power consumed by active modules)
    /// </summary>
    public int GetUsedPower(GameObject location)
    {
        if (!planetaryModules.ContainsKey(location))
            return 0;
            
        int usedPower = 0;
        
        // Calculate power used by active modules
        foreach (Module module in planetaryModules[location])
        {
            if (module.IsActive)
            {
                usedPower += module.PowerRequired;
            }
        }
        
        return usedPower;
    }
    
    /// <summary>
    /// Check if player can activate a module at a location
    /// </summary>
    public bool CanActivateModule(GameObject location, Module module)
    {
        // Get available power
        int availablePower = GetAvailablePower(location);
        int usedPower = GetUsedPower(location);
        int remainingPower = availablePower - usedPower;
        
        // Check if enough power to activate
        if (remainingPower < module.PowerRequired)
        {
            Debug.LogWarning($"Not enough power to activate {module.Name}. Needs {module.PowerRequired}, has {remainingPower}");
            return false;
        }
        
        return true;
    }
    
    /// <summary>
    /// Activate a module at a location
    /// </summary>
    public bool ActivateModule(GameObject location, Module module)
    {
        if (!CanActivateModule(location, module))
            return false;
            
        // Set module as active
        module.IsActive = true;
        
        Debug.Log($"Activated {module.Name} at {location.name}");
        return true;
    }
    
    /// <summary>
    /// Deactivate a module at a location
    /// </summary>
    public void DeactivateModule(GameObject location, Module module)
    {
        module.IsActive = false;
        
        Debug.Log($"Deactivated {module.Name} at {location.name}");
    }
    
    /// <summary>
    /// Check if a resource is available at a specific location
    /// </summary>
    public bool HasResource(GameObject location, ResourceType type, int amount)
    {
        return GetResourceAmount(location, type) >= amount;
    }
    
    /// <summary>
    /// Get total ship strength at a specific location
    /// </summary>
    public int GetTotalShipStrengthAtLocation(GameObject location)
    {
        int totalStrength = 0;
        
        foreach (Ship ship in GetShipsAtLocation(location))
        {
            totalStrength += ship.Strength;
        }
        
        return totalStrength;
    }
    
    /// <summary>
    /// Transfer cargo between a ship and a location
    /// </summary>
    public bool TransferCargo(Ship ship, GameObject location, ResourceType type, int amount, bool toShip)
    {
        // Transferring from ship to location
        if (!toShip)
        {
            // Check if ship has enough of the resource
            if (ship.GetCargoAmount(type) < amount)
            {
                Debug.LogWarning($"Ship {ship.Name} doesn't have enough {type} to transfer. Has {ship.GetCargoAmount(type)}, trying to transfer {amount}");
                return false;
            }
            
            // Remove from ship
            if (!ship.RemoveCargo(type, amount))
            {
                Debug.LogWarning($"Failed to remove cargo from ship {ship.Name}");
                return false;
            }
            
            // Add to location
            AddResource(location, type, amount);
            
            Debug.Log($"Transferred {amount} {type} from {ship.Name} to {location.name}");
            return true;
        }
        // Transferring from location to ship
        else
        {
            // Check if location has enough of the resource
            if (GetResourceAmount(location, type) < amount)
            {
                Debug.LogWarning($"Location {location.name} doesn't have enough {type} to transfer. Has {GetResourceAmount(location, type)}, trying to transfer {amount}");
                return false;
            }
            
            // Check if ship has enough cargo space
            int currentCargo = ship.GetTotalCargoAmount();
            if (currentCargo + amount > ship.CargoCapacity)
            {
                Debug.LogWarning($"Ship {ship.Name} doesn't have enough cargo space. Current: {currentCargo}, Capacity: {ship.CargoCapacity}, Trying to add: {amount}");
                return false;
            }
            
            // Remove from location
            if (!UseResource(location, type, amount))
            {
                Debug.LogWarning($"Failed to remove resource from location {location.name}");
                return false;
            }
            
            // Add to ship
            if (!ship.AddCargo(type, amount))
            {
                // If failed to add to ship, add back to location
                AddResource(location, type, amount);
                Debug.LogWarning($"Failed to add cargo to ship {ship.Name}");
                return false;
            }
            
            Debug.Log($"Transferred {amount} {type} from {location.name} to {ship.Name}");
            return true;
        }
    }
    
    /// <summary>
    /// Count how many worlds the player has surveyed
    /// </summary>
    public int CountSurveyedWorlds()
    {
        int count = 0;
        
        // This would need a way to track which worlds have been surveyed by this player
        // Could be implemented using a list of surveyed world IDs or names
        
        return count;
    }
    
    /// <summary>
    /// Count how many worlds the player has military occupation of
    /// </summary>
    public int CountOccupiedWorlds()
    {
        int count = 0;
        
        // This would need a way to track which worlds this player has military occupation of
        // Could be implemented by checking all PlanetBody components in the scene
        
        return count;
    }
    
    /// <summary>
    /// Calculate total victory points from all sources
    /// </summary>
    public int CalculateVictoryPoints()
    {
        int totalVP = VictoryPoints;
        
        // Add points from wonders, technology cards, etc.
        // This would be a more complex calculation based on the game rules
        
        return totalVP;
    }
    
    /// <summary>
    /// Add victory points
    /// </summary>
    public void AddVictoryPoints(int amount)
    {
        VictoryPoints += amount;
        Debug.Log($"Player {PlayerId} gained {amount} Victory Points. Total: {VictoryPoints}");
    }
}