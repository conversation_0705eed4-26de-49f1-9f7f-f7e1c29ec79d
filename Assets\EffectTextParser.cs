using System.Text.RegularExpressions;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

public class EffectTextParser
{
    private ResourceTypeConverter resourceConverter;
    
    public EffectTextParser(ResourceTypeConverter resourceConverter)
    {
        this.resourceConverter = resourceConverter;
    }
    
    public void ParseEffectResources(CardData card)
    {
        // Initialize empty lists
        card.InputResources = new List<Resource>();
        card.OutputResources = new List<Resource>();
        
        string effectText = card.Effect ?? "";
        if (string.IsNullOrEmpty(effectText))
            return;
        
        // First check for Victory Points
        if (effectText.Contains("VP") || effectText.Contains("Victory Point"))
        {
            Match vpMatch = Regex.Match(effectText, @"(\d+)\s*(?:VP|Victory\s*Points?)", RegexOptions.IgnoreCase);
            if (vpMatch.Success)
            {
                int amount = int.Parse(vpMatch.Groups[1].Value);
                card.OutputResources.Add(new Resource(ResourceType.VP, amount));
            }
        }
        
        // Special case for Ship Strength - this is the key addition for Strength
        if (card.Type.ToLower() == "ship" || card.SubType.ToLower() == "ship")
        {
            // Check if this is a Ship card with Strength
            Match strengthMatch = Regex.Match(card.Effect, @"Strength\s+(\d+)", RegexOptions.IgnoreCase);
            if (strengthMatch.Success)
            {
                int amount = int.Parse(strengthMatch.Groups[1].Value);
                card.OutputResources.Add(new Resource(ResourceType.Strength, amount));
            }
        }

        // Check for passive gains (e.g. "+1 Science per turn")
        if (effectText.Contains("per turn") || effectText.Contains("Passive"))
        {
            ParsePassiveGains(card);
        }
        
        // Handle different card patterns with more flexible detection
        if (Regex.IsMatch(effectText, @"\([^)]+\)"))
        {
            // Any format using parentheses for inputs, including:
            // - "Activate (1 Power, 1 Ore): +1 Alloy"
            // - "Activate in orbit (1 Power, 1 Carbon): +1 Graphene" 
            // - "Auto activate at Neptune (1 Power): +2 Helium-3"
            ParseActivateParenthesesFormat(card, effectText);
        }
        else if (effectText.Contains("Activate:") || effectText.Contains("Auto activate:"))
        {
            // Standard activate format: "Activate: -1 Power, -1 Carbon → +1 Fuel"
            ParseActivateColonFormat(card, effectText);
        }
        else if (card.PowerOutput > 0)
        {
            // Power generators: "+1 Power"
            card.OutputResources.Add(new Resource(ResourceType.Power, card.PowerOutput));
        }
        else
        {
            // Other effects - look for patterns like "+1 Resource", "-1 Resource" 
            ParseGenericEffectResources(card, effectText);
        }
        
        // Special case for power
        if (card.PowerRequired > 0 && !card.InputResources.Any(r => r.Type == ResourceType.Power))
        {
            card.InputResources.Add(new Resource(ResourceType.Power, card.PowerRequired));
        }
        
        // Check if the effect mentions Fuel but we didn't add it to outputs
        if (effectText.Contains("Fuel") && 
            !card.OutputResources.Any(r => r.Type == ResourceType.Fuel) &&
            !card.InputResources.Any(r => r.Type == ResourceType.Fuel))
        {
            // Look for patterns like ": +1 Fuel" or " +1 Fuel"
            Match fuelMatch = Regex.Match(effectText, @"(?::|→|,|\s)\s*\+(\d+)\s+Fuel", RegexOptions.IgnoreCase);
            if (fuelMatch.Success)
            {
                int amount = int.Parse(fuelMatch.Groups[1].Value);
                card.OutputResources.Add(new Resource(ResourceType.Fuel, amount));
            }
        }
        
        // Final check for any direct resource mentions with "+" signs
        foreach (ResourceType resourceType in System.Enum.GetValues(typeof(ResourceType)))
        {
            string resourceName = resourceType.ToString();
            if (effectText.Contains("+" + resourceName) && 
                !card.OutputResources.Any(r => r.Type == resourceType))
            {
                Match resourceMatch = Regex.Match(effectText, 
                    $@"\+(\d+)\s*{resourceName}", RegexOptions.IgnoreCase);
                    
                if (resourceMatch.Success)
                {
                    int amount = int.Parse(resourceMatch.Groups[1].Value);
                    card.OutputResources.Add(new Resource(resourceType, amount));
                }
            }
        }
    }
    
    private void ParsePassiveGains(CardData card)
    {
        string effectText = card.Effect?.ToLower() ?? "";
        
        // Check for passive/habitation wording
        bool isPassive = effectText.Contains("passive") || 
                        effectText.Contains("per turn") || 
                        effectText.Contains("per round") ||
                        effectText.Contains("habitation") ||
                        effectText.Contains("end of the game") ||
                        card.IsWonder;
                        
        if (!isPassive)
            return;
            
        // Parse VP gains - more flexible regex to catch "5 VP" as well as "5 Victory Points"
        Match vpMatch = Regex.Match(effectText, @"(\d+)\s*(?:vp|victory\s*points?)", RegexOptions.IgnoreCase);
        if (vpMatch.Success)
        {
            int amount = int.Parse(vpMatch.Groups[1].Value);
            card.OutputResources.Add(new Resource(ResourceType.VP, amount));
        }
        
        // Parse Science gains - allow for "+2 Science" format
        Match scienceMatch = Regex.Match(effectText, @"\+?(\d+)\s*science", RegexOptions.IgnoreCase);
        if (scienceMatch.Success)
        {
            int amount = int.Parse(scienceMatch.Groups[1].Value);
            card.OutputResources.Add(new Resource(ResourceType.Science, amount));
        }
        
        // Parse Money gains - handle multiple formats: "$3", "3$", "3 Dollars"
        Match moneyMatch = Regex.Match(effectText, @"(\$(\d+)|\+?(\d+)\s*\$|\+?(\d+)\s*dollars)", RegexOptions.IgnoreCase);
        if (moneyMatch.Success)
        {
            // Determine which group contains the value
            string valueStr = null;
            if (moneyMatch.Groups[2].Success) // $3 format
                valueStr = moneyMatch.Groups[2].Value;
            else if (moneyMatch.Groups[3].Success) // 3$ format
                valueStr = moneyMatch.Groups[3].Value;
            else if (moneyMatch.Groups[4].Success) // 3 Dollars format
                valueStr = moneyMatch.Groups[4].Value;
                
            if (valueStr != null)
            {
                int amount = int.Parse(valueStr);
                card.OutputResources.Add(new Resource(ResourceType.Dollars, amount));
            }
        }
        
        // Look for any resources that are specified with + sign
        foreach (ResourceType resourceType in System.Enum.GetValues(typeof(ResourceType)))
        {
            if (resourceType == ResourceType.VP || 
                resourceType == ResourceType.Science || 
                resourceType == ResourceType.Dollars)
                continue; // Already handled above
                
            string resourceName = resourceType.ToString();
            Match resourceMatch = Regex.Match(effectText, 
                $@"\+(\d+)\s*{resourceName}", RegexOptions.IgnoreCase);
                
            if (resourceMatch.Success)
            {
                int amount = int.Parse(resourceMatch.Groups[1].Value);
                card.OutputResources.Add(new Resource(resourceType, amount));
            }
        }
    }

    private void ParseActivateParenthesesFormat(CardData card, string effectText)
    {
        // Extract content inside parentheses for inputs
        Match inputsMatch = Regex.Match(effectText, @"\(([^)]+)\)");
        
        if (inputsMatch.Success)
        {
            string inputsText = inputsMatch.Groups[1].Value;
            string[] inputs = inputsText.Split(',');
            
            foreach (string input in inputs)
            {
                string cleanInput = input.Trim();
                Match resourceMatch = Regex.Match(cleanInput, @"(\d+)\s+(.+)");
                
                if (resourceMatch.Success)
                {
                    int amount = int.Parse(resourceMatch.Groups[1].Value);
                    string resourceName = resourceMatch.Groups[2].Value;
                    ResourceType resourceType = resourceConverter.ParseResourceType(resourceName);
                    
                    card.InputResources.Add(new Resource(resourceType, amount));
                }
            }
        }
        
        // Extract outputs after colon - modified regex to better catch Fuel
        Match outputsMatch = Regex.Match(effectText, @":\s*(.+)");
        if (outputsMatch.Success)
        {
            string outputsText = outputsMatch.Groups[1].Value;
            
            // Find all resource patterns like "+X Resource"
            var resourceMatches = Regex.Matches(outputsText, @"\+(\d+)\s+([a-zA-Z\-]+)");
            foreach (Match match in resourceMatches)
            {
                if (match.Groups.Count >= 3)
                {
                    int amount = int.Parse(match.Groups[1].Value);
                    string resourceName = match.Groups[2].Value.Trim();
                    ResourceType resourceType = resourceConverter.ParseResourceType(resourceName);
                    
                    card.OutputResources.Add(new Resource(resourceType, amount));
                }
            }
        }
        
        // Additional check specifically for Fuel
        if (effectText.Contains("Fuel") && !card.OutputResources.Any(r => r.Type == ResourceType.Fuel))
        {
            Match fuelMatch = Regex.Match(effectText, @"\+(\d+)\s+Fuel");
            if (fuelMatch.Success)
            {
                int amount = int.Parse(fuelMatch.Groups[1].Value);
                card.OutputResources.Add(new Resource(ResourceType.Fuel, amount));
            }
        }
    }
    
    private void ParseActivateColonFormat(CardData card, string effectText)
    {
        // Extract all text after "Activate:" - now supports "Auto activate:"
        Match activateMatch = Regex.Match(effectText, @"(?:Auto\s+)?Activate\s*:\s*(.+)", RegexOptions.IgnoreCase);
        if (!activateMatch.Success)
            return;
            
        string activateText = activateMatch.Groups[1].Value;
        
        // Look for inputs (-X Resource) and outputs (+Y Resource)
        ParseInputsAndOutputs(card, activateText);
    }

    private void ParseGenericEffectResources(CardData card, string effectText)
    {
        // First, check for Wonder/VP patterns
        if (IsWonderCard(card))
        {
            ParsePassiveGains(card);
            return;
        }
        
        // Look for passive gain patterns
        if (effectText.Contains("per turn") || 
            effectText.Contains("Passive") || 
            effectText.Contains("gain each turn"))
        {
            ParsePassiveGains(card);
            return;
        }
        
        // Add explicit check for Ship Strength
        if ((card.Type.ToLower() == "ship" || card.SubType.ToLower() == "ship") && 
            effectText.Contains("Strength"))
        {
            Match strengthMatch = Regex.Match(effectText, @"Strength\s+(\d+)", RegexOptions.IgnoreCase);
            if (strengthMatch.Success)
            {
                int amount = int.Parse(strengthMatch.Groups[1].Value);
                // Only add if not already in resources
                if (!card.OutputResources.Any(r => r.Type == ResourceType.Strength))
                {
                    card.OutputResources.Add(new Resource(ResourceType.Strength, amount));
                }
            }
        }

        // Look for patterns like "+1 Resource" or "-1 Resource"
        ParseInputsAndOutputs(card, effectText);
        
        // If no resources found, try checking for resource keywords
        if (card.InputResources.Count == 0 && card.OutputResources.Count == 0)
        {
            foreach (ResourceType resourceType in System.Enum.GetValues(typeof(ResourceType)))
            {
                string resourceName = resourceType.ToString();
                
                // Skip checking for common words like "Ore" that might be part of other words
                if (resourceType == ResourceType.Ore && 
                !effectText.Contains(" Ore ") && 
                !effectText.Contains(" ore ") &&
                !effectText.EndsWith(" Ore") && 
                !effectText.EndsWith(" ore"))
                {
                    continue;
                }
                
                // Check if the resource is mentioned in the effect
                if (effectText.IndexOf(resourceName, System.StringComparison.OrdinalIgnoreCase) >= 0)
                {
                    // Try to determine if it's an input or output and how many
                    bool isOutput = 
                        effectText.Contains($"produce {resourceName}", System.StringComparison.OrdinalIgnoreCase) || 
                        effectText.Contains($"gain {resourceName}", System.StringComparison.OrdinalIgnoreCase) ||
                        effectText.Contains($"additional {resourceName}", System.StringComparison.OrdinalIgnoreCase) ||
                        effectText.Contains($"+{resourceName}", System.StringComparison.OrdinalIgnoreCase);
                        
                    bool isInput = 
                        effectText.Contains($"use {resourceName}", System.StringComparison.OrdinalIgnoreCase) || 
                        effectText.Contains($"consume {resourceName}", System.StringComparison.OrdinalIgnoreCase) ||
                        effectText.Contains($"-{resourceName}", System.StringComparison.OrdinalIgnoreCase);
                    
                    // Try to extract amount
                    int amount = 1; // Default
                    
                    Match amountMatch = Regex.Match(effectText, 
                        $@"(\d+)\s+{resourceName}|{resourceName}\s+\((\d+)\)", 
                        RegexOptions.IgnoreCase);
                        
                    if (amountMatch.Success)
                    {
                        if (amountMatch.Groups[1].Success)
                            amount = int.Parse(amountMatch.Groups[1].Value);
                        else if (amountMatch.Groups[2].Success)
                            amount = int.Parse(amountMatch.Groups[2].Value);
                    }
                    
                    // Add to appropriate list
                    if (isOutput)
                        card.OutputResources.Add(new Resource(resourceType, amount));
                    else if (isInput)
                        card.InputResources.Add(new Resource(resourceType, amount));
                }
            }
        }
    }

    private void ParseInputsAndOutputs(CardData card, string text, bool outputsOnly = false)
    {
        // Normalize arrow notation to make parsing easier
        text = text.Replace("→", ",");
        
        // Find all input resources (-X Resource)
        if (!outputsOnly)
        {
            var inputMatches = Regex.Matches(text, @"-(\d+)\s+([a-zA-Z\s\-]+?)(?:,|$|\s|\.)");
            foreach (Match match in inputMatches)
            {
                if (match.Groups.Count >= 3)
                {
                    int amount = int.Parse(match.Groups[1].Value);
                    string resourceName = match.Groups[2].Value.Trim();
                    ResourceType resourceType = resourceConverter.ParseResourceType(resourceName);
                    
                    card.InputResources.Add(new Resource(resourceType, amount));
                }
            }
        }
        
        // Find all output resources (+X Resource)
        var outputMatches = Regex.Matches(text, @"\+(\d+)\s+([a-zA-Z\s\-]+?)(?:,|$|\s|\.)");
        foreach (Match match in outputMatches)
        {
            if (match.Groups.Count >= 3)
            {
                int amount = int.Parse(match.Groups[1].Value);
                string resourceName = match.Groups[2].Value.Trim();
                ResourceType resourceType = resourceConverter.ParseResourceType(resourceName);
                
                card.OutputResources.Add(new Resource(resourceType, amount));
            }
        }
        
        // Also look for resources without a + sign that are likely outputs
        if (!text.Contains("+") && text.Contains(":"))
        {
            // After a colon with no + sign, assume outputs
            string outputPart = text.Split(':')[1].Trim();
            var simpleMatches = Regex.Matches(outputPart, @"(\d+)\s+([a-zA-Z\s\-]+?)(?:,|$|\s|\.)");
            
            foreach (Match match in simpleMatches)
            {
                if (match.Groups.Count >= 3)
                {
                    int amount = int.Parse(match.Groups[1].Value);
                    string resourceName = match.Groups[2].Value.Trim();
                    ResourceType resourceType = resourceConverter.ParseResourceType(resourceName);
                    
                    // Only add if it's not already in inputs (avoid duplication)
                    if (!card.InputResources.Any(r => r.Type == resourceType) && 
                        !card.OutputResources.Any(r => r.Type == resourceType))
                    {
                        card.OutputResources.Add(new Resource(resourceType, amount));
                    }
                }
            }
        }
    }

    private bool IsWonderCard(CardData card)
    {
        string effectText = card.Effect?.ToLower() ?? "";
        string nameText = card.Name?.ToLower() ?? "";
        string typeText = card.Type?.ToLower() ?? "";
        
        // Check for wonder-like names or effects
        bool isWonder = typeText.Contains("wonder") ||
                        nameText.Contains("wonder") || 
                        nameText.Contains("monument") ||
                        effectText.Contains("wonder") ||
                        effectText.Contains("victory point") ||
                        effectText.Contains(" vp") ||
                        card.IsWonder;
                    
        if (isWonder)
        {
            card.IsWonder = true;
        }
        
        return isWonder;
    }

    public int ParsePowerOutput(string effectText)
    {
        // Look for patterns like "+1 Power" or "1 Power"
        Match match = Regex.Match(effectText, @"\+?(\d+)\s*Power", RegexOptions.IgnoreCase);
        if (match.Success)
        {
            return int.Parse(match.Groups[1].Value);
        }
        return 0;
    }
    
    public int ParsePowerRequired(string effectText)
    {
        // Look for patterns like "-1 Power" or "Activate: -1 Power"
        Match match = Regex.Match(effectText, @"-(\d+)\s*Power", RegexOptions.IgnoreCase);
        if (match.Success)
        {
            return int.Parse(match.Groups[1].Value);
        }
        
        // For patterns like "Activate (1 Power):" where the power requirement is in parentheses
        match = Regex.Match(effectText, @"Activate\s*\(?(\d+)\s*Power", RegexOptions.IgnoreCase);
        if (match.Success)
        {
            return int.Parse(match.Groups[1].Value);
        }
        
        // If effect contains "Activate:" but no power specified, assume 1
        if (effectText.Contains("Activate:") && !effectText.Contains("Power"))
        {
            return 1;
        }
        
        return 0;
    }
}