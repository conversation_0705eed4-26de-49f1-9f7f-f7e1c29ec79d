using System;
using System.Collections.Generic;

[System.Serializable]

public class TechnologyCardData
{
    public string cardName;
    public int tier;
    public int scienceValue;
    public string cardDescription;
    public List<Resource> resourceCost;
    
    public TechnologyCardData()
    {
        cardName = string.Empty;
        tier = 0;
        scienceValue = 0;
        cardDescription = string.Empty;
        resourceCost = new List<Resource>();
    }

    public TechnologyCardData(CardData cardData)
    {
        cardName = cardData.Name;
        tier = cardData.Tier;
        scienceValue = cardData.ScienceCost;
        cardDescription = cardData.Effect;
        resourceCost = new List<Resource>(cardData.BuildCost);
    }
}

[System.Serializable]
public class BlueprintTechnologyCardData : TechnologyCardData
{
    
    public BlueprintType blueprintType;
    public List<Resource> buildCost;
    public int powerOutput;
    public int cargoCapacity;
    public float deltaVPerFuel;
    public int shipStrength;
    public bool isConsumedOnSurvey;
    
   public BlueprintTechnologyCardData() : base()
    {
        buildCost = new List<Resource>();
    }

    public BlueprintTechnologyCardData(CardData cardData) : base(cardData)
    {
        buildCost = new List<Resource>(cardData.BuildCost);
        powerOutput = cardData.PowerOutput;
        
        // Set blueprint type based on subtype
        if (cardData.SubType.ToLower().Contains("ship"))
            blueprintType = BlueprintType.Ship;
        else if (cardData.SubType.ToLower().Contains("power"))
            blueprintType = BlueprintType.Power;
        else if (cardData.SubType.ToLower().Contains("extractor"))
            blueprintType = BlueprintType.Extractor;
        else if (cardData.SubType.ToLower().Contains("processor"))
            blueprintType = BlueprintType.Processor;
        else
            blueprintType = BlueprintType.Other;
        
        // Extract properties from effect parameters
        if (cardData.EffectParams.ContainsKey("cargoCapacity"))
            cargoCapacity = (int)cardData.EffectParams["cargoCapacity"];
        if (cardData.EffectParams.ContainsKey("deltaVPerFuel"))
            deltaVPerFuel = Convert.ToSingle(cardData.EffectParams["deltaVPerFuel"]);
        if (cardData.EffectParams.ContainsKey("shipStrength"))
            shipStrength = (int)cardData.EffectParams["shipStrength"];
        if (cardData.EffectParams.ContainsKey("consumedOnSurvey"))
            isConsumedOnSurvey = (bool)cardData.EffectParams["consumedOnSurvey"];
    }
}