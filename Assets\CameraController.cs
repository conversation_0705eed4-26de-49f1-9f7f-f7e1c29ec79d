using UnityEngine;

public class CameraController : Mono<PERSON><PERSON><PERSON><PERSON>
{
    [Header("Movement Settings")]
    [SerializeField] private float panSpeed = 20f;
    [SerializeField] private float keyboardMoveSpeed = 40f;
    [SerializeField] private float zoomSpeed = 1f;

    [Header("Zoom Bounds")]
    [SerializeField] private float minZoomSize = 2f;
    [SerializeField] private float maxZoomSize = 15f;

    [Header("Pan Bounds")]
    [SerializeField] private bool useBounds = true;
    [SerializeField] private float minX = 0f;
    [SerializeField] private float maxX = 128f;
    [SerializeField] private float minZ = 145f;
    [SerializeField] private float maxZ = 220f;

    [Header("Smoothing Settings")]
    [SerializeField] private bool enableSmoothing = true;
    [SerializeField] private float movementSmoothTime = 0.1f;
    [SerializeField] private float zoomSmoothTime = 0.05f;

    // Velocity tracking for smoothing
    private Vector3 panVelocity;
    private float zoomVelocity;
    private Vector3 targetPosition;
    private float targetZoom;

    private Camera cam;
    private Vector3 lastMousePosition;
    private bool isPanning;

    private void Start()
    {
        cam = GetComponent<Camera>();
        if (cam == null)
        {
            cam = Camera.main;
        }

        // Ensure the camera is orthographic
        cam.orthographic = true;

        // Initialize target values
        targetPosition = transform.position;
        targetZoom = cam.orthographicSize;
    }

    private void Update()
    {
        HandlePanning();
        HandleKeyboardPanning();
        HandleZooming();

        if (enableSmoothing)
        {
            ApplySmoothing();
        }
    }

    private Vector3 ClampPosition(Vector3 position)
    {
        if (useBounds)
        {
            position.x = Mathf.Clamp(position.x, minX, maxX);
            position.z = Mathf.Clamp(position.z, minZ, maxZ);
        }
        return position;
    }

    private void HandlePanning()
    {
        if (Input.GetMouseButtonDown(0))
        {
            isPanning = true;
            lastMousePosition = Input.mousePosition;
        }
        else if (Input.GetMouseButtonUp(0))
        {
            isPanning = false;
        }

        if (isPanning)
        {
            Vector3 delta = Input.mousePosition - lastMousePosition;

            // Convert screen movement to world space movement
            Vector3 movement = panSpeed * Time.deltaTime * new Vector3(-delta.x, 0, -delta.y);

            // Move relative to camera's orientation but keep y position constant
            Vector3 translation = transform.right * movement.x + transform.forward * movement.z;
            translation.y = 0;

            Vector3 newTargetPosition = targetPosition + translation;
            targetPosition = ClampPosition(newTargetPosition);

            if (!enableSmoothing)
            {
                transform.position = targetPosition;
            }

            lastMousePosition = Input.mousePosition;
        }
    }

    private void HandleKeyboardPanning()
    {
        Vector3 movement = Vector3.zero;

        // Up movement
        if (Input.GetKey(KeyCode.W) || Input.GetKey(KeyCode.UpArrow))
        {
            movement.z += 1;
        }
        // Down movement
        if (Input.GetKey(KeyCode.S) || Input.GetKey(KeyCode.DownArrow))
        {
            movement.z -= 1;
        }
        // Right movement
        if (Input.GetKey(KeyCode.D) || Input.GetKey(KeyCode.RightArrow))
        {
            movement.x += 1;
        }
        // Left movement
        if (Input.GetKey(KeyCode.A) || Input.GetKey(KeyCode.LeftArrow))
        {
            movement.x -= 1;
        }

        if (movement != Vector3.zero)
        {
            float angle = -225f * Mathf.Deg2Rad;
            float cos = Mathf.Cos(angle);
            float sin = Mathf.Sin(angle);

            Vector3 rotatedMovement = new Vector3(
                movement.x * cos - movement.z * sin,
                0,
                movement.x * sin + movement.z * cos
            );

            Vector3 newTargetPosition = targetPosition + rotatedMovement * keyboardMoveSpeed * Time.deltaTime;
            targetPosition = ClampPosition(newTargetPosition);

            if (!enableSmoothing)
            {
                transform.position = targetPosition;
            }
        }
    }

    private void HandleZooming()
    {
        float scrollDelta = Input.mouseScrollDelta.y;

        if (scrollDelta != 0)
        {
            // Calculate new orthographic size
            float newZoom = targetZoom - scrollDelta * zoomSpeed;
            targetZoom = Mathf.Clamp(newZoom, minZoomSize, maxZoomSize);

            if (!enableSmoothing)
            {
                cam.orthographicSize = targetZoom;
            }
        }
    }

    private void ApplySmoothing()
    {
        // Smooth position movement
        transform.position = Vector3.SmoothDamp(transform.position, targetPosition, ref panVelocity, movementSmoothTime);

        // Smooth zoom
        cam.orthographicSize = Mathf.SmoothDamp(cam.orthographicSize, targetZoom, ref zoomVelocity, zoomSmoothTime);
    }
}