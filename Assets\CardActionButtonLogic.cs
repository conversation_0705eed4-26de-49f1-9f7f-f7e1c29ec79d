using UnityEngine;

public class CardActionButtonLogic
{
    private CardDetailDisplay cardDetailDisplay;
    private CardActionButtonUI buttonUI;
    private CardActionIndicatorManager indicatorManager;
    private CardActionProcessorHandler processorHandler;
    private CardActionMarketHandler marketHandler;

    private CardData currentCardData;
    private int currentCardIndex;
    private CardDetailDisplay.CardSource currentCardSource;
    private GameObject currentSourceObject;

    public CardActionButtonLogic(
        CardDetailDisplay cardDetailDisplay,
        CardActionButtonUI buttonUI,
        CardActionIndicatorManager indicatorManager,
        CardActionProcessorHandler processorHandler,
        CardActionMarketHandler marketHandler)
    {
        this.cardDetailDisplay = cardDetailDisplay;
        this.buttonUI = buttonUI;
        this.indicatorManager = indicatorManager;
        this.processorHandler = processorHandler;
        this.marketHandler = marketHandler;
    }

    public void SetCardData(CardData cardData, int cardIndex, CardDetailDisplay.CardSource cardSource, GameObject sourceObject)
    {
        currentCardData = cardData;
        currentCardIndex = cardIndex;
        currentCardSource = cardSource;
        currentSourceObject = sourceObject;
    }

    public void ConfigureButtons(CardDetailDisplay.CardSource cardSource)
    {
        if (currentCardData == null) return;

        switch (cardSource)
        {
            case CardDetailDisplay.CardSource.CardRow:
                ConfigureCardRowButtons();
                break;
            case CardDetailDisplay.CardSource.PlayerHand:
                ConfigurePlayerHandButtons();
                break;
            case CardDetailDisplay.CardSource.PlayerPlayArea:
                ConfigurePlayerPlayAreaButtons();
                break;
            case CardDetailDisplay.CardSource.WorldCard:
                ConfigureWorldCardButtons();
                break;
            case CardDetailDisplay.CardSource.OrbitLocation:
                ConfigureOrbitLocationButtons();
                break;
        }
    }

    private void ConfigureCardRowButtons()
    {
        if (currentCardIndex < 0) return;

        TechnologyDeckManager deckManager = TechnologyDeckManager.Instance;
        int actionCost = deckManager?.GetCardCost(currentCardIndex) ?? 1;

        GameManager gameManager = GameManager.Instance;
        bool hasEnoughActions = gameManager != null && gameManager.ActionsRemaining >= actionCost;

        string buttonText = hasEnoughActions ? "Take Card" : "Not enough\nactions";
        int fontSize = hasEnoughActions ? 17 : 15;

        buttonUI.ConfigureButton(0, buttonText, OnTakeCardClick, CanTakeCard(), 250f);
        buttonUI.SetButtonText(0, buttonText, fontSize);
        indicatorManager.CreateActionIndicators(0, actionCost);
    }

    private void ConfigurePlayerHandButtons()
    {
        buttonUI.ConfigureButton(0, "Develop", OnDevelopCardClick, CanDevelopCard(), 250f);
        if (CanDevelopCard())
        {
            indicatorManager.CreateActionIndicators(0, 1);
        }
    }

    private void ConfigurePlayerPlayAreaButtons()
    {
        Debug.Log($"[CardActionButtonLogic] ConfigurePlayerPlayAreaButtons for card: {currentCardData?.Name}");

        // Check if this is a constructed ship
        bool isConstructedShip = IsConstructedShip();
        Debug.Log($"[CardActionButtonLogic] Is constructed ship: {isConstructedShip}");

        if (isConstructedShip)
        {
            GameManager gameManager = GameManager.Instance;
            bool hasActions = gameManager && gameManager.ActionsRemaining > 0;
            Debug.Log($"[CardActionButtonLogic] Has actions: {hasActions}, Actions remaining: {gameManager?.ActionsRemaining}");

            buttonUI.ConfigureButton(0, "Move", OnMoveShipClick, hasActions);
            Debug.Log($"[CardActionButtonLogic] Configured Move button for constructed ship {currentCardData?.Name}");

            if (hasActions)
            {
                indicatorManager.CreateActionIndicators(0, 1);
            }
        }
        else
        {
            // Check if this is a buildable tech card
            bool isBuildable = IsBuildableTech();
            Debug.Log($"[CardActionButtonLogic] Is buildable tech: {isBuildable}");

            if (isBuildable)
            {
                GameManager gameManager = GameManager.Instance;
                bool hasActions = gameManager && gameManager.ActionsRemaining > 0;
                Debug.Log($"[CardActionButtonLogic] Has actions: {hasActions}, Actions remaining: {gameManager?.ActionsRemaining}");

                buttonUI.ConfigureButton(0, "Build", OnBuildCardClick, hasActions);
                Debug.Log($"[CardActionButtonLogic] Configured Build button for {currentCardData?.Name}");

                if (hasActions)
                {
                    indicatorManager.CreateActionIndicators(0, 1);
                }
            }
            else
            {
                Debug.Log($"[CardActionButtonLogic] Configuring Activate button for {currentCardData?.Name}");
                buttonUI.ConfigureButton(0, "Activate", OnActivateCardClick, false);
            }
        }
    }

    private void ConfigureWorldCardButtons()
    {
        bool isEarth = IsEarth();
        GameManager gameManager = GameManager.Instance;
        bool hasActions = gameManager && gameManager.ActionsRemaining > 0;

        if (isEarth)
        {
            buttonUI.ConfigureButton(0, "Market", () => marketHandler.OnMarketButtonClicked(), hasActions);
            buttonUI.ConfigureButton(1, "Processor", () => processorHandler.OnEarthProcessorButtonClicked(), hasActions);
            buttonUI.ConfigureButton(2, "Assembler", OnEarthAssemblerButtonClicked, hasActions);

            for (int i = 0; i < 3; i++)
            {
                indicatorManager.CreateActionIndicators(i, 1);
            }
        }
    }

    private void ConfigureOrbitLocationButtons()
    {
        // Orbital locations have similar functionality to worlds but without Earth-specific features
        GameManager gameManager = GameManager.Instance;
        bool hasActions = gameManager && gameManager.ActionsRemaining > 0;

        // For now, orbital locations don't have specific buttons
        // They can contain ships, modules, facilities, and wonders but don't have special actions
        // This method is here for future expansion if orbital locations get specific functionality
    }

    private bool IsEarth()
    {
        if (currentSourceObject != null)
        {
            WorldCardVisual worldCard = currentSourceObject.GetComponent<WorldCardVisual>();
            if (worldCard?.GetCelestialBody()?.GetComponent<PlanetBody>()?.Name == "Earth")
                return true;

            PlanetBody sourcePlanetBody = currentSourceObject.GetComponent<PlanetBody>();
            if (sourcePlanetBody?.Name == "Earth")
                return true;
        }

        return currentCardData?.Name == "Earth";
    }

    private bool CanTakeCard()
    {
        if (currentCardIndex < 0) return false;

        GameManager gameManager = GameManager.Instance;
        TechnologyDeckManager deckManager = TechnologyDeckManager.Instance;

        if (gameManager == null || deckManager == null) return false;

        int actionCost = deckManager.GetCardCost(currentCardIndex);
        return gameManager.ActionsRemaining >= actionCost;
    }

    private bool CanDevelopCard()
    {
        if (currentCardData == null) return false;

        GameManager gameManager = GameManager.Instance;
        if (gameManager?.CurrentPlayer == null) return false;

        Player currentPlayer = gameManager.CurrentPlayer;
        bool hasActions = gameManager.ActionsRemaining > 0;
        bool hasEnoughScience = currentPlayer.ScienceValue >= currentCardData.ScienceCost;

        // Update UI based on what's missing
        if (!hasActions)
        {
            buttonUI.SetButtonText(0, "No Actions\nRemaining", 15);
            return false;
        }
        else if (!hasEnoughScience && currentCardData.ScienceCost > 0)
        {
            buttonUI.SetButtonText(0, $"Need {currentCardData.ScienceCost}\nScience", 15);
            return false;
        }
        else
        {
            buttonUI.SetButtonText(0, "Develop", 17);
        }

        return hasActions && hasEnoughScience;
    }

    private bool IsConstructedShip()
    {
        if (currentSourceObject == null)
        {
            Debug.Log("[CardActionButtonLogic] IsConstructedShip: currentSourceObject is null");
            return false;
        }

        // Check if this object has a ShipCardVisual component, which indicates it's a constructed ship
        ShipCardVisual shipCardVisual = currentSourceObject.GetComponent<ShipCardVisual>();
        if (shipCardVisual != null)
        {
            Debug.Log($"[CardActionButtonLogic] IsConstructedShip for {currentCardData?.Name}: True (has ShipCardVisual)");
            return true;
        }

        // Also check if it's using UniversalCardVisual3D with ship data (like starting ships)
        if (currentCardData != null)
        {
            bool isConstructedShipType = currentCardData.Type == "ConstructedShip" ||
                                       currentCardData.SubType == "ConstructedShip" ||
                                       (currentCardData.SubType == "Ship" && currentSourceObject.name.StartsWith("Ship_"));

            Debug.Log($"[CardActionButtonLogic] IsConstructedShip for {currentCardData?.Name}: {isConstructedShipType} (CardData check - Type: {currentCardData.Type}, SubType: {currentCardData.SubType}, ObjectName: {currentSourceObject.name})");
            return isConstructedShipType;
        }

        Debug.Log($"[CardActionButtonLogic] IsConstructedShip for {currentCardData?.Name}: False");
        return false;
    }

    private bool IsBuildableTech()
    {
        if (currentCardData == null)
        {
            Debug.Log("[CardActionButtonLogic] IsBuildableTech: currentCardData is null");
            return false;
        }

        string type = currentCardData.Type?.ToLower() ?? "";
        string subType = currentCardData.SubType?.ToLower() ?? "";
        string effect = currentCardData.Effect?.ToLower() ?? "";

        Debug.Log($"[CardActionButtonLogic] IsBuildableTech for {currentCardData.Name}: Type='{type}', SubType='{subType}', Effect='{effect}'");

        // Exclude constructed objects - these have different types/subtypes and should not be buildable
        if (type.Contains("constructed") || subType.Contains("constructed"))
        {
            Debug.Log($"[CardActionButtonLogic] {currentCardData.Name} is a constructed object, not buildable");
            return false;
        }

        // Only allow building of technology cards (blueprints)
        bool isBuildable = subType.Contains("module") ||
                          subType.Contains("facility") ||
                          subType.Contains("ship") ||
                          type.Contains("wonder") ||
                          effect.Contains("wonder");

        Debug.Log($"[CardActionButtonLogic] IsBuildableTech result: {isBuildable}");
        return isBuildable;
    }



    // Button click handlers
    private void OnTakeCardClick()
    {
        CardToHandSystem cardToHandSystem = UnityEngine.Object.FindAnyObjectByType<CardToHandSystem>();
        if (cardToHandSystem != null)
        {
            cardToHandSystem.OnTakeCardButtonClicked();
        }
        else
        {
            cardDetailDisplay.Hide();
        }
    }

    private void OnDevelopCardClick()
    {
        DevelopTechnologyHandler.HandleDevelopTechnology(currentCardData, cardDetailDisplay);
    }

    private void OnActivateCardClick()
    {
        Debug.Log("Activate action not implemented yet");
        cardDetailDisplay.Hide();
    }

    private void OnMoveShipClick()
    {
        Debug.Log("Move ship action not implemented yet");
        cardDetailDisplay.Hide();
    }

    private void OnBuildCardClick()
    {
        Debug.Log($"[CardActionButtonLogic] OnBuildCardClick called for {currentCardData?.Name}");

        TechBuildHandler buildHandler = TechBuildHandler.Instance;
        Debug.Log($"[CardActionButtonLogic] TechBuildHandler.Instance: {buildHandler != null}");

        if (buildHandler != null)
        {
            Debug.Log($"[CardActionButtonLogic] Calling buildHandler.OnBuildButtonClicked");
            buildHandler.OnBuildButtonClicked(currentCardData);
        }
        else
        {
            Debug.LogError("[CardActionButtonLogic] TechBuildHandler.Instance is null! Make sure TechBuildHandler is initialized in the scene.");
        }

        cardDetailDisplay.Hide();
    }

    private void OnEarthAssemblerButtonClicked()
    {
        AssemblerHandler assemblerHandler = AssemblerHandler.Instance;
        if (assemblerHandler != null)
        {
            GameObject actualWorld = GetActualWorldFromSource();
            assemblerHandler.OnAssemblerButtonClicked(actualWorld);
        }
    }

    private GameObject GetActualWorldFromSource()
    {
        if (currentSourceObject == null) return null;

        // If clicked on a world card, get the actual celestial body
        WorldCardVisual worldCard = currentSourceObject.GetComponent<WorldCardVisual>();
        if (worldCard != null)
        {
            return worldCard.GetCelestialBody();
        }

        // If clicked directly on planet, return as-is
        PlanetBody planetBody = currentSourceObject.GetComponent<PlanetBody>();
        if (planetBody != null)
        {
            return currentSourceObject;
        }

        return currentSourceObject;
    }

}