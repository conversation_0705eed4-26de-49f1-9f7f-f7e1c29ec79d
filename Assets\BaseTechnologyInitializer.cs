using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// Base class for technology initializers with common functionality
/// </summary>
public abstract class BaseTechnologyInitializer : MonoBehaviour
{
    [Header("Card Positioning")]
    [SerializeField] protected Transform cardsParent; // Parent object for organization
    [SerializeField] protected Vector3 startPosition = new Vector3(-600f, 0f, 0f); // Starting position of the first card
    [SerializeField] protected Vector3 cardOffset = new Vector3(220f, 0f, 0f); // Offset between cards
    [SerializeField] protected int cardsPerRow = 6; // Number of cards per row
    [SerializeField] protected Vector3 rowOffset = new Vector3(0f, -300f, 0f); // Offset between rows
    
    [Header("Created Cards")]
    [SerializeField] protected List<GameObject> createdCards = new List<GameObject>();
    
    /// <summary>
    /// Clear all previously created cards
    /// </summary>
    protected void ClearCards()
    {
        foreach (GameObject card in createdCards)
        {
            if (card != null)
                Destroy(card);
        }
        createdCards.Clear();
    }
    
    /// <summary>
    /// Calculate the position for a card based on its index, taking into account rows
    /// </summary>
    protected Vector3 GetCardPosition(int index)
    {
        int row = index / cardsPerRow;
        int col = index % cardsPerRow;
        
        return startPosition + (cardOffset * col) + (rowOffset * row);
    }
    
    /// <summary>
    /// Set the position of a card
    /// </summary>
    protected void SetCardPosition(GameObject card, Vector3 position)
    {
        if (card == null) return;
        
        RectTransform rectTransform = card.GetComponent<RectTransform>();
        if (rectTransform != null)
        {
            rectTransform.anchoredPosition = position;
        }
        else
        {
            card.transform.localPosition = position;
        }
    }
    
    /// <summary>
    /// Helper method to set private serialized fields via reflection
    /// </summary>
    protected void SetPrivateField<T>(Component component, string fieldName, T value)
    {
        System.Reflection.FieldInfo field = component.GetType().GetField(fieldName, 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
        if (field != null)
        {
            field.SetValue(component, value);
        }
        else
        {
            // Try to find it in the parent class
            field = component.GetType().BaseType.GetField(fieldName, 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                
            if (field != null)
            {
                field.SetValue(component, value);
            }
            else
            {
                Debug.LogError($"Field {fieldName} not found in {component.GetType().Name} or its parent class");
            }
        }
    }
}