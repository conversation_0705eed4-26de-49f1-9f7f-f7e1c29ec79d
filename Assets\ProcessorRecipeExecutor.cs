using UnityEngine;
using System.Collections.Generic;

public static class ProcessorRecipeExecutor
{
    public static void Execute(ProcessorRecipe recipe)
    {
        GameManager gameManager = GameManager.Instance;
        WorldManager worldManager = WorldManager.Instance;

        if (gameManager?.CurrentPlayer == null || worldManager == null) return;

        Player currentPlayer = gameManager.CurrentPlayer;
        GameObject earth = worldManager.GetCelestialBodyByName("Earth");
        if (earth == null) return;

        // Check resources
        if (!HasRequiredResources(currentPlayer, earth, recipe)) return;
        if (gameManager.ActionsRemaining <= 0) return;

        // Create undoable action
        ProcessResourceAction processAction = new ProcessResourceAction(
            recipe.Inputs, recipe.Outputs, currentPlayer.PlayerId, earth);

        // Execute transaction
        gameManager.SetActionsRemaining(gameManager.ActionsRemaining - 1);
        ConsumeInputs(currentPlayer, earth, recipe);
        ProduceOutputs(currentPlayer, earth, recipe);

        GameLogManager logManager = GameLogManager.Instance;
        if (logManager != null)
        {
            string inputStr = string.Join(", ", recipe.Inputs);
            string outputStr = string.Join(", ", recipe.Outputs);
            logManager.AddLog($"Player {currentPlayer.PlayerId + 1} processed {inputStr} → {outputStr}");
        }

        // Register undo and update UI
        UndoManager.Instance?.RegisterAction(processAction);
        UpdateUI(currentPlayer, earth);
    }

    private static bool HasRequiredResources(Player player, GameObject earth, ProcessorRecipe recipe)
    {
        Dictionary<ResourceType, int> required = new Dictionary<ResourceType, int>();

        foreach (ResourceType type in recipe.Inputs)
        {
            required[type] = required.GetValueOrDefault(type, 0) + 1;
        }

        foreach (var kvp in required)
        {
            if (player.GetResourceAmount(earth, kvp.Key) < kvp.Value)
                return false;
        }

        return true;
    }

    private static void ConsumeInputs(Player player, GameObject earth, ProcessorRecipe recipe)
    {
        Dictionary<ResourceType, int> toConsume = new Dictionary<ResourceType, int>();

        foreach (ResourceType type in recipe.Inputs)
        {
            toConsume[type] = toConsume.GetValueOrDefault(type, 0) + 1;
        }

        foreach (var kvp in toConsume)
        {
            player.UseResource(earth, kvp.Key, kvp.Value);
        }
    }

    private static void ProduceOutputs(Player player, GameObject earth, ProcessorRecipe recipe)
    {
        Dictionary<ResourceType, int> toProduce = new Dictionary<ResourceType, int>();

        foreach (ResourceType type in recipe.Outputs)
        {
            toProduce[type] = toProduce.GetValueOrDefault(type, 0) + 1;
        }

        foreach (var kvp in toProduce)
        {
            player.AddResource(earth, kvp.Key, kvp.Value);
        }
    }

    private static void UpdateUI(Player player, GameObject earth)
    {
        // Update play area
        PlayAreaManager playAreaManager = PlayAreaManager.Instance;
        playAreaManager?.RefreshPlayerPlayArea(player.PlayerId);

        // Update game UI
        GameUI gameUI = UnityEngine.Object.FindFirstObjectByType<GameUI>();
        gameUI?.RefreshUI();

        // Update resource display in detail panel
        CardDetailDisplay cardDetailDisplay = CardDetailDisplay.Instance;
        if (cardDetailDisplay != null)
        {
            WorldResourceDisplay resourceDisplay = cardDetailDisplay.GetComponent<WorldResourceDisplay>();
            resourceDisplay?.DisplayWorldResources(earth, player.PlayerId);
        }
    }
}