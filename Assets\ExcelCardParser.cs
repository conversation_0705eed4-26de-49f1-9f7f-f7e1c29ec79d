using System.Collections.Generic;
using UnityEngine;

public class ExcelCardParser : MonoBehaviour
{
    private List<CardData> cachedCards = null;
    private CardCSVReader csvReader;
    private CardDataFactory cardDataFactory;
    private EffectTextParser effectParser;
    private ResourceTypeConverter resourceConverter;
    
    private void Awake()
    {
        // Initialize dependencies
        csvReader = new CardCSVReader();
        resourceConverter = new ResourceTypeConverter();
        effectParser = new EffectTextParser(resourceConverter);
        cardDataFactory = new CardDataFactory(resourceConverter, effectParser);
    }
    
    public List<CardData> ParseAllCards(TextAsset excelFile = null)
    {
        // Return cached cards if already parsed
        if (cachedCards != null)
        {
            return cachedCards;
        }
        
        cachedCards = new List<CardData>();
        
        // Parse each sheet - paths should not include .csv extension
        cachedCards.AddRange(ParseSheet("Cards/starting_technologies_tier_0"));
        cachedCards.AddRange(ParseSheet("Cards/tier_1_technologies"));
        cachedCards.AddRange(ParseSheet("Cards/tier_2_technologies")); 
        cachedCards.AddRange(ParseSheet("Cards/tier_3_technologies"));
        cachedCards.AddRange(ParseSheet("Cards/special_technologies"));
        
        return cachedCards;
    }

    // Add a method to clear cache if needed
    public void ClearCache()
    {
        cachedCards = null;
    }
    
    private List<CardData> ParseSheet(string csvPath)
    {
        List<CardData> cards = new List<CardData>();

        // Load the CSV file - Unity requires not including the extension for Resources.Load
        string pathWithoutExtension = csvPath;
        if (pathWithoutExtension.EndsWith(".csv"))
        {
            pathWithoutExtension = pathWithoutExtension.Substring(0, pathWithoutExtension.Length - 4);
        }
        
        TextAsset csvFile = Resources.Load<TextAsset>(pathWithoutExtension);
        if (csvFile == null)
        {
            Debug.LogError($"Could not load CSV file: {pathWithoutExtension}");
            return cards;
        }
        
        // Parse CSV content
        List<List<string>> parsedLines = csvReader.ParseCSV(csvFile.text);
        
        // Skip header row and process each line
        for (int i = 1; i < parsedLines.Count; i++)
        {
            List<string> fields = parsedLines[i];
            if (fields.Count < 7)
            {
                Debug.LogWarning($"Line {i} has insufficient fields: {fields.Count}");
                continue;
            }
            
            // Determine tier from the filename
            int tier = 4;
            if (csvPath.Contains("tier_1"))
                tier = 1;
            else if (csvPath.Contains("tier_2"))
                tier = 2;
            else if (csvPath.Contains("tier_3"))
                tier = 3;
            else if (csvPath.Contains("tier_0") || csvPath.Contains("starting_technologies"))
                tier = 0;
            
            // Create card data from fields
            CardData card = cardDataFactory.CreateCardData(fields, tier);
            cards.Add(card);
        }
        
        return cards;
    }
}