using System.Collections.Generic;
using System.Linq;
using UnityEngine; 
// Initiative card effects
public class GainScienceEffect : ICardEffect
{
    private string name;
    private int amount;
    
    public GainScienceEffect(string name, int amount)
    {
        this.name = name;
        this.amount = amount;
    }
    
    public bool CanExecute(Player player, GameObject location, Dictionary<string, object> parameters)
    {
        return true;
    }
    
    public bool Execute(Player player, GameObject location, Dictionary<string, object> parameters)
    {
        player.ScienceValue += amount;
        Debug.Log($"Player {player.PlayerId} gained {amount} Science from {name}");
        return true;
    }
    
    public string GetDescription()
    {
        return $"Initiative: Gain {amount} Science.";
    }
}