using System.Collections.Generic;
using UnityEngine;
using System.Linq;

/// <summary>
/// Manages Earth cards and the effects they have on the game
/// </summary>
public class EarthCardManager : MonoBehaviour
{
    // Singleton pattern
    public static EarthCardManager Instance { get; private set; }
    
    [Header("Card Decks")]
    [SerializeField] private List<EarthCard> tier1Deck = new List<EarthCard>();
    [SerializeField] private List<EarthCard> tier2Deck = new List<EarthCard>();
    [SerializeField] private List<EarthCard> tier3Deck = new List<EarthCard>();
    
    [Header("Card Prefabs")]
    [SerializeField] private GameObject earthCardPrefab;
    
    [Header("Earth Card Display")]
    [SerializeField] private Transform cardDisplayParent;
    [SerializeField] private float displayDuration = 5.0f;
    
    // Current state
    private int currentTier = 1;
    private GameObject activeCardDisplay = null;
    private float displayTimer = 0f;
    
    // Card history
    private List<EarthCard> drawnCards = new List<EarthCard>();
    
    private void Awake()
    {
        // Singleton setup
        if (Instance != null && Instance != this)
        {
            Destroy(gameObject);
            return;
        }
        
        Instance = this;
    }
    
    private void Start()
    {
        // Initialize card decks
        InitializeCardDecks();
        
        // Shuffle the decks
        ShuffleDecks();
    }
    
    private void Update()
    {
        // Handle card display timing
        if (activeCardDisplay != null)
        {
            displayTimer -= Time.deltaTime;
            
            if (displayTimer <= 0f)
            {
                // Hide the card display
                activeCardDisplay.SetActive(false);
                activeCardDisplay = null;
            }
        }
    }
    
    /// <summary>
    /// Initialize the Earth card decks
    /// </summary>
    private void InitializeCardDecks()
    {
        // This would normally load cards from a resource or parsing a text file
        // For now, we'll create them directly
    }
    
    /// <summary>
    /// Shuffle all decks
    /// </summary>
    private void ShuffleDecks()
    {
        ShuffleDeck(tier1Deck);
        ShuffleDeck(tier2Deck);
        ShuffleDeck(tier3Deck);
    }
    
    /// <summary>
    /// Shuffle a single deck
    /// </summary>
    private void ShuffleDeck<T>(List<T> deck)
    {
        for (int i = 0; i < deck.Count; i++)
        {
            int randomIndex = Random.Range(i, deck.Count);
            T temp = deck[i];
            deck[i] = deck[randomIndex];
            deck[randomIndex] = temp;
        }
    }
    
    /// <summary>
    /// Draw an Earth card at the start of a player's turn
    /// </summary>
    public EarthCard DrawCard()
    {
        // Determine which deck to draw from
        List<EarthCard> currentDeck = GetCurrentDeck();
        
        if (currentDeck.Count == 0)
        {
            // Move to the next tier if the current deck is empty
            if (currentTier < 3)
            {
                currentTier++;
                Debug.Log($"Moving to Tier {currentTier} Earth cards");
                currentDeck = GetCurrentDeck();
            }
            else
            {
                // No more Earth cards
                Debug.Log("No more Earth cards to draw. Game is in final rounds.");
                return null;
            }
        }
        
        if (currentDeck.Count > 0)
        {
            // Draw the top card
            EarthCard card = currentDeck[0];
            currentDeck.RemoveAt(0);
            
            // Add to drawn cards
            drawnCards.Add(card);
            
            // Display the card
            DisplayCard(card);
            
            // Resolve the card effect
            ResolveCardEffect(card);
            
            Debug.Log($"Drew Earth card: {card.Name}");
            return card;
        }
        
        return null;
    }
    
    /// <summary>
    /// Get the current deck based on tier
    /// </summary>
    private List<EarthCard> GetCurrentDeck()
    {
        switch (currentTier)
        {
            case 1: return tier1Deck;
            case 2: return tier2Deck;
            case 3: return tier3Deck;
            default: return tier1Deck;
        }
    }
    
    /// <summary>
    /// Display the drawn card on screen
    /// </summary>
    private void DisplayCard(EarthCard card)
    {
        // Hide any existing card display
        if (activeCardDisplay != null)
        {
            activeCardDisplay.SetActive(false);
        }
        
        // Create a new card display
        GameObject cardObj = Instantiate(earthCardPrefab, cardDisplayParent);
        
        // Set up the card visual
        EarthCardVisual cardVisual = cardObj.GetComponent<EarthCardVisual>();
        if (cardVisual != null)
        {
            cardVisual.SetupCard(card);
        }
        
        // Set as active display
        activeCardDisplay = cardObj;
        displayTimer = displayDuration;
        
        // Show the card
        activeCardDisplay.SetActive(true);
    }
    
    /// <summary>
    /// Resolve the effect of an Earth card
    /// </summary>
    private void ResolveCardEffect(EarthCard card)
    {
        GameManager gameManager = GameManager.Instance;
        if (gameManager == null)
            return;
            
        // Get all players
        List<Player> players = gameManager.Players;
        
        // Get current player
        Player currentPlayer = gameManager.CurrentPlayer;
        int currentPlayerId = gameManager.CurrentPlayerIndex;
        
        // Get Earth location
        GameObject earth = WorldManager.Instance.GetCelestialBodyByName("Earth");
        if (earth == null)
        {
            Debug.LogError("Earth not found in WorldManager!");
            return;
        }
        
        // Handle the card effect based on type
        switch (card.EffectType)
        {
            case EarthCardEffectType.AllPlayersGainBasicResources:
                // All players gain 1 of each basic resource
                foreach (Player player in players)
                {
                    player.AddResource(earth, ResourceType.Ore, 1);
                    player.AddResource(earth, ResourceType.Ice, 1);
                    player.AddResource(earth, ResourceType.Carbon, 1);
                    player.AddResource(earth, ResourceType.Silicon, 1);
                    player.AddResource(earth, ResourceType.RareEarths, 1);
                    
                    // Update UI
                    PlayAreaManager playAreaManager = PlayAreaManager.Instance;
                    if (playAreaManager != null)
                    {
                        playAreaManager.OnPlayerResourcesChanged(player.PlayerId, earth, ResourceType.Ore, player.GetResourceAmount(earth, ResourceType.Ore));
                        playAreaManager.OnPlayerResourcesChanged(player.PlayerId, earth, ResourceType.Ice, player.GetResourceAmount(earth, ResourceType.Ice));
                        playAreaManager.OnPlayerResourcesChanged(player.PlayerId, earth, ResourceType.Carbon, player.GetResourceAmount(earth, ResourceType.Carbon));
                        playAreaManager.OnPlayerResourcesChanged(player.PlayerId, earth, ResourceType.Silicon, player.GetResourceAmount(earth, ResourceType.Silicon));
                        playAreaManager.OnPlayerResourcesChanged(player.PlayerId, earth, ResourceType.RareEarths, player.GetResourceAmount(earth, ResourceType.RareEarths));
                    }
                }
                
                // Active player gets extra money
                if (currentPlayer != null)
                {
                    currentPlayer.Money += 2;
                }
                break;
                
            case EarthCardEffectType.AllPlayersGainMoney:
                // All players gain money
                foreach (Player player in players)
                {
                    player.Money += 4;
                }
                
                // Active player gets extra money
                if (currentPlayer != null)
                {
                    currentPlayer.Money += 1;
                }
                break;
                
            case EarthCardEffectType.AllPlayersGainScience:
                // All players gain science
                foreach (Player player in players)
                {
                    player.ScienceValue += 1;
                }
                
                // Active player gets extra science
                if (currentPlayer != null)
                {
                    currentPlayer.ScienceValue += 1;
                }
                break;
                
            case EarthCardEffectType.ShipStrengthVictoryPoints:
                // All players gain VP based on ship strength
                foreach (Player player in players)
                {
                    int totalStrength = 0;
                    foreach (Ship ship in player.GetShips())
                    {
                        totalStrength += ship.Strength;
                    }
                    
                    int vp = totalStrength * 2;
                    player.AddVictoryPoints(vp);
                }
                break;
                
            case EarthCardEffectType.MilitaryConflict:
                // Players with highest strength gain resources, lowest lose resources
                int highestStrength = 0;
                List<Player> highestStrengthPlayers = new List<Player>();
                int lowestStrength = int.MaxValue;
                List<Player> lowestStrengthPlayers = new List<Player>();
                
                // Calculate strength for each player
                foreach (Player player in players)
                {
                    int totalStrength = 0;
                    foreach (Ship ship in player.GetShips())
                    {
                        totalStrength += ship.Strength;
                    }
                    
                    if (totalStrength > highestStrength)
                    {
                        highestStrength = totalStrength;
                        highestStrengthPlayers.Clear();
                        highestStrengthPlayers.Add(player);
                    }
                    else if (totalStrength == highestStrength)
                    {
                        highestStrengthPlayers.Add(player);
                    }
                    
                    if (totalStrength < lowestStrength && totalStrength > 0)
                    {
                        lowestStrength = totalStrength;
                        lowestStrengthPlayers.Clear();
                        lowestStrengthPlayers.Add(player);
                    }
                    else if (totalStrength == lowestStrength)
                    {
                        lowestStrengthPlayers.Add(player);
                    }
                }
                
                // Highest strength players gain resources
                foreach (Player player in highestStrengthPlayers)
                {
                    player.AddResource(earth, ResourceType.Ore, 1);
                    player.AddResource(earth, ResourceType.Ice, 1);
                    player.AddResource(earth, ResourceType.Carbon, 1);
                    player.AddResource(earth, ResourceType.Silicon, 1);
                    player.AddResource(earth, ResourceType.RareEarths, 1);
                    
                    // Update UI
                    PlayAreaManager playAreaManager = PlayAreaManager.Instance;
                    if (playAreaManager != null)
                    {
                        playAreaManager.OnPlayerResourcesChanged(player.PlayerId, earth, ResourceType.Ore, player.GetResourceAmount(earth, ResourceType.Ore));
                        playAreaManager.OnPlayerResourcesChanged(player.PlayerId, earth, ResourceType.Ice, player.GetResourceAmount(earth, ResourceType.Ice));
                        playAreaManager.OnPlayerResourcesChanged(player.PlayerId, earth, ResourceType.Carbon, player.GetResourceAmount(earth, ResourceType.Carbon));
                        playAreaManager.OnPlayerResourcesChanged(player.PlayerId, earth, ResourceType.Silicon, player.GetResourceAmount(earth, ResourceType.Silicon));
                        playAreaManager.OnPlayerResourcesChanged(player.PlayerId, earth, ResourceType.RareEarths, player.GetResourceAmount(earth, ResourceType.RareEarths));
                    }
                }
                
                // Lowest strength players lose resources
                foreach (Player player in lowestStrengthPlayers)
                {
                    player.UseResource(earth, ResourceType.Ore, 1);
                    player.UseResource(earth, ResourceType.Ice, 1);
                    player.UseResource(earth, ResourceType.Carbon, 1);
                    player.UseResource(earth, ResourceType.Silicon, 1);
                    player.UseResource(earth, ResourceType.RareEarths, 1);
                    
                    // Update UI
                    PlayAreaManager playAreaManager = PlayAreaManager.Instance;
                    if (playAreaManager != null)
                    {
                        playAreaManager.OnPlayerResourcesChanged(player.PlayerId, earth, ResourceType.Ore, player.GetResourceAmount(earth, ResourceType.Ore));
                        playAreaManager.OnPlayerResourcesChanged(player.PlayerId, earth, ResourceType.Ice, player.GetResourceAmount(earth, ResourceType.Ice));
                        playAreaManager.OnPlayerResourcesChanged(player.PlayerId, earth, ResourceType.Carbon, player.GetResourceAmount(earth, ResourceType.Carbon));
                        playAreaManager.OnPlayerResourcesChanged(player.PlayerId, earth, ResourceType.Silicon, player.GetResourceAmount(earth, ResourceType.Silicon));
                        playAreaManager.OnPlayerResourcesChanged(player.PlayerId, earth, ResourceType.RareEarths, player.GetResourceAmount(earth, ResourceType.RareEarths));
                    }
                }
                break;
                
            case EarthCardEffectType.SurveyScience:
                // Players gain science per world they've surveyed
                foreach (Player player in players)
                {
                    int surveyedWorlds = player.CountSurveyedWorlds();
                    int scienceGained = surveyedWorlds * 2;
                    player.ScienceValue += scienceGained;
                    
                    Debug.Log($"Player {player.PlayerId} gained {scienceGained} science from {surveyedWorlds} surveyed worlds");
                }
                break;
                
            case EarthCardEffectType.FinancialCrisis:
                // Players lose half their money
                foreach (Player player in players)
                {
                    int moneyLost = player.Money / 2;
                    player.Money -= moneyLost;
                    
                    Debug.Log($"Player {player.PlayerId} lost {moneyLost} money due to financial crisis");
                }
                break;
                
            case EarthCardEffectType.ExplorationVictoryPoints:
                // Players gain VP per surveyed world
                foreach (Player player in players)
                {
                    int surveyedWorlds = player.CountSurveyedWorlds();
                    int vpGained = surveyedWorlds * 3;
                    player.AddVictoryPoints(vpGained);
                    
                    Debug.Log($"Player {player.PlayerId} gained {vpGained} VP from {surveyedWorlds} surveyed worlds");
                }
                break;
                
            case EarthCardEffectType.DominationVictoryPoints:
                // Players gain VP per occupied world
                foreach (Player player in players)
                {
                    int occupiedWorlds = player.CountOccupiedWorlds();
                    int vpGained = occupiedWorlds * 5;
                    player.AddVictoryPoints(vpGained);
                    
                    Debug.Log($"Player {player.PlayerId} gained {vpGained} VP from {occupiedWorlds} occupied worlds");
                }
                break;
                
            // Add more cases for other card effects
            
            default:
                Debug.LogWarning($"Unhandled Earth card effect type: {card.EffectType}");
                break;
        }
    }
    
    #region Card Creation Methods

    
    #endregion
    
    /// <summary>
    /// Get the current tier of Earth cards
    /// </summary>
    public int GetCurrentTier()
    {
        return currentTier;
    }
    
    /// <summary>
    /// Get the number of cards remaining in the current deck
    /// </summary>
    public int GetRemainingCardsInCurrentDeck()
    {
        return GetCurrentDeck().Count;
    }
    
    /// <summary>
    /// Get the list of drawn cards
    /// </summary>
    public List<EarthCard> GetDrawnCards()
    {
        return new List<EarthCard>(drawnCards);
    }
}