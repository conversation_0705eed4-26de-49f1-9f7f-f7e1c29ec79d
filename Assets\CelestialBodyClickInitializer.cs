using UnityEngine;

/// <summary>
/// Initializes click handlers for all celestial bodies in the scene
/// </summary>
public class CelestialBodyClickInitializer : MonoBehaviour
{
    private void Start()
    {
        // Get the WorldManager
        WorldManager worldManager = WorldManager.Instance;
        if (worldManager == null)
        {
            Debug.LogError("WorldManager instance not found!");
            return;
        }

        // Get all celestial bodies
        var celestialBodies = worldManager.GetAllCelestialBodies();
        
        // Add click handlers to all bodies
        foreach (var body in celestialBodies)
        {
            if (body != null && !body.GetComponent<CelestialBodyClickHandler>())
            {
                body.AddComponent<CelestialBodyClickHandler>();
            }
        }
    }
}