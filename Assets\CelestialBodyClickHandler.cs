using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// <PERSON><PERSON> clicks on celestial bodies in the 3D map
/// </summary>
public class CelestialBodyClickHandler : MonoBehaviour
{
    private void OnMouseUp()
    {
        // Check if the detail panel is currently open
        CardDetailDisplay detailDisplay = CardDetailDisplay.Instance;
        if (detailDisplay != null)
        {
            GameObject detailPanel = detailDisplay.GetDetailPanel();
            if (detailPanel != null && detailPanel.activeSelf)
            {
                // Panel is active, check if mouse is over it using raycasting
                Vector2 mousePosition = Input.mousePosition;
                
                // Use UnityEngine.EventSystems to check if pointer is over UI
                UnityEngine.EventSystems.PointerEventData eventData = 
                    new UnityEngine.EventSystems.PointerEventData(UnityEngine.EventSystems.EventSystem.current);
                eventData.position = mousePosition;
                
                List<UnityEngine.EventSystems.RaycastResult> results = new List<UnityEngine.EventSystems.RaycastResult>();
                UnityEngine.EventSystems.EventSystem.current.RaycastAll(eventData, results);
                
                // Check if any of the results contain the detail panel
                foreach (var result in results)
                {
                    // Check if this is the detail panel or any of its children
                    Transform current = result.gameObject.transform;
                    while (current != null)
                    {
                        if (current.gameObject == detailPanel)
                        {
                            // Click is on the detail panel, ignore it
                            return;
                        }
                        current = current.parent;
                    }
                }
            }
        }
        
        // Reuse the same logic from WorldCardVisual
        WorldCardVisual.ShowWorldCardDetail(gameObject, gameObject);
    }
}