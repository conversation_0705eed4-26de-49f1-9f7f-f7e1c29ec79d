using System.Collections.Generic;
using System.Linq;
using UnityEngine; 

public class ExtractorEffect : ICardEffect
{
    private string extractorName;
    private int powerRequired;
    private int bonusPerDeposit;
    
    public ExtractorEffect(string extractorName, int powerRequired, int bonusPerDeposit = 1)
    {
        this.extractorName = extractorName;
        this.powerRequired = powerRequired;
        this.bonusPerDeposit = bonusPerDeposit;
    }
    
    public bool CanExecute(Player player, GameObject location, Dictionary<string, object> parameters)
    {
        // Check power
        if (player.GetAvailablePower(location) < powerRequired)
            return false;
            
        // Check if location has deposits
        PlanetBody planetBody = location.GetComponent<PlanetBody>();
        if (planetBody == null || planetBody.GetDeposits().Count == 0)
            return false;
            
        return true;
    }
    
    public bool Execute(Player player, GameObject location, Dictionary<string, object> parameters)
    {
        if (!CanExecute(player, location, parameters))
            return false;
            
        PlanetBody planetBody = location.GetComponent<PlanetBody>();
        if (planetBody != null)
        {
            foreach (var deposit in planetBody.GetDeposits())
            {
                player.AddResource(location, deposit.ResourceType, bonusPerDeposit);
            }
        }
        
        return true;
    }
    
    public string GetDescription()
    {
        return $"Extracts {bonusPerDeposit} of each resource per deposit";
    }
}