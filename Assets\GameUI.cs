using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;
using TMPro;

public class GameUI : MonoBehaviour
{
    [Header("Action UI")]
    [SerializeField] private GameObject actionIndicatorPrefab; // White square prefab
    [SerializeField] private GameObject actionContainerPrefab; // Grey container prefab
    [SerializeField] private Transform actionContainersParent; // Parent for action containers
    private List<GameObject> actionIndicators = new List<GameObject>();
    private List<GameObject> actionContainers = new List<GameObject>();

    [Header("Card Row UI")]
    [SerializeField] private CardRowUI cardRowUI;

    [Header("Activation Instructions")]
    [SerializeField] private TextMeshProUGUI activationInstructionsText;

    [Header("Player Score UI")]
    // Player score buttons
    [SerializeField] private TextMeshProUGUI playerScoreButtonRed;
    [SerializeField] private TextMeshProUGUI playerScoreButtonGreen;
    [SerializeField] private TextMeshProUGUI playerScoreButtonBlue;
    [SerializeField] private TextMeshProUGUI playerScoreButtonYellow;

    // List to easily iterate through player score buttons
    private List<TextMeshProUGUI> playerScoreButtons = new List<TextMeshProUGUI>();

    [Header("Player Rockets")]
    [SerializeField] private GameObject redRocket;
    [SerializeField] private GameObject greenRocket;
    [SerializeField] private GameObject blueRocket;
    [SerializeField] private GameObject yellowRocket;
    // List to easily iterate through all rockets
    private List<GameObject> playerRockets = new List<GameObject>();

    [Header("Buttons")]
    [SerializeField] private Button endTurnButton;
    [SerializeField] private Button redoButton;
    [SerializeField] private Button undoButton;

    [Header("Camera Control")]
    [SerializeField] private PlayerCameraController cameraController;

    // Reference to game manager
    private GameManager gameManager;

    private void Awake()
    {

        // Initialize lists
        playerScoreButtons = new List<TextMeshProUGUI> {
            playerScoreButtonRed,
            playerScoreButtonGreen,
            playerScoreButtonBlue,
            playerScoreButtonYellow
        };

        // Initialize rocket list
        playerRockets = new List<GameObject> {
            redRocket,
            greenRocket,
            blueRocket,
            yellowRocket
        };
    }

    private void Start()
    {

        // Wait until GameManager is initialized
        gameManager = GameManager.Instance;

        if (gameManager == null)
        {
            Debug.LogError("GameManager instance is null! Will try to find it again in Update.");
            return;
        }

        // Initialize UI components
        InitializeUI();
    }

    private void Update()
    {
        // Check if we need to initialize the UI (if GameManager wasn't available in Start)
        if (gameManager == null)
        {
            gameManager = GameManager.Instance;
            if (gameManager != null)
            {
                InitializeUI();
            }
        }
    }

    private void InitializeUI()
    {
        // Initialize action indicators
        InitializeActionIndicators();

        // Initialize player scores and rocket visibility
        InitializePlayerUI();

        // Set up button listeners
        SetupButtons();

        // Update UI based on initial game state
        UpdateUI();

        if (cardRowUI != null)
        {
            cardRowUI.InitializeCardRow();
        }

        // Initialize activation instructions as inactive
        HideActivationInstructions();
    }

    private void InitializeActionIndicators()
    {
        // Clear any existing indicators and containers
        foreach (var indicator in actionIndicators)
        {
            if (indicator != null)
                Destroy(indicator);
        }
        actionIndicators.Clear();

        foreach (var container in actionContainers)
        {
            if (container != null)
                Destroy(container);
        }
        actionContainers.Clear();

        if (actionContainerPrefab == null || actionIndicatorPrefab == null)
        {
            Debug.LogError("Action container or indicator prefab is null!");
            return;
        }

        if (actionContainersParent == null)
        {
            Debug.LogError("Action containers parent not set!");
            return;
        }

        // Get current player's max actions
        int maxPlayerActions = gameManager && gameManager.CurrentPlayer != null
            ? gameManager.CurrentPlayer.MaxActions
            : 4;

        // Create action containers and indicators based on max actions
        for (int i = 0; i < maxPlayerActions; i++)
        {
            // Create container
            GameObject container = Instantiate(actionContainerPrefab);
            container.transform.SetParent(actionContainersParent, false);
            actionContainers.Add(container);

            // Create indicator inside container
            GameObject indicator = Instantiate(actionIndicatorPrefab);
            indicator.transform.SetParent(container.transform, false);

            // Ensure proper positioning and scaling
            RectTransform indicatorRect = indicator.GetComponent<RectTransform>();
            if (indicatorRect != null)
            {
                indicatorRect.anchorMin = new Vector2(0, 0);
                indicatorRect.anchorMax = new Vector2(1, 1);
                indicatorRect.anchoredPosition = Vector2.zero;
                indicatorRect.sizeDelta = Vector2.zero;
                indicatorRect.localScale = Vector3.one;
            }

            actionIndicators.Add(indicator);
        }
    }

    private void InitializePlayerUI()
    {

        // Validate score button references
        for (int i = 0; i < playerScoreButtons.Count; i++)
        {
            if (playerScoreButtons[i] == null)
            {
                Debug.LogError($"Player score button {i} is null!");
            }
        }

        // Validate rocket references
        for (int i = 0; i < playerRockets.Count; i++)
        {
            if (playerRockets[i] == null)
            {
                Debug.LogError($"Player rocket {i} is null!");
            }
        }

        // Get player count from GameManager
        int playerCount = gameManager ? gameManager.PlayerCount : 2;

        // Show/hide buttons and rockets based on player count
        for (int i = 0; i < playerScoreButtons.Count; i++)
        {
            bool isActive = (i < playerCount);

            // Set score button visibility
            if (playerScoreButtons[i] != null)
            {
                // Get the Button component that wraps the TextMeshProUGUI
                Button buttonComponent = playerScoreButtons[i].GetComponentInParent<Button>();
                if (buttonComponent != null)
                {
                    buttonComponent.gameObject.SetActive(isActive);
                }
                else
                {
                    // If not wrapped in a button, hide/show the text component's game object
                    playerScoreButtons[i].gameObject.SetActive(isActive);
                }
            }

            // Set rocket visibility
            if (i < playerRockets.Count && playerRockets[i] != null)
            {
                playerRockets[i].SetActive(isActive);
            }
        }

        // Initialize score texts
        for (int i = 0; i < playerScoreButtons.Count && i < playerCount; i++)
        {
            if (playerScoreButtons[i] != null)
            {
                playerScoreButtons[i].text = $"Player {i + 1}: 0";
            }
        }
    }

    private void SetupButtons()
    {

        if (endTurnButton != null)
        {
            endTurnButton.onClick.AddListener(OnEndTurnClicked);
        }
        else
        {
            Debug.LogWarning("End Turn button not assigned!");
        }

        if (redoButton != null)
        {
            redoButton.onClick.RemoveAllListeners();
            redoButton.onClick.AddListener(OnRedoClicked);
        }
        else
        {
            Debug.LogWarning("Redo button not assigned!");
        }

        if (undoButton != null)
        {
            undoButton.onClick.AddListener(OnUndoClicked);
            undoButton.interactable = false;
        }
        else
        {
            Debug.LogWarning("Undo button not assigned!");
        }

        // Setup player score button click listeners
        for (int i = 0; i < playerScoreButtons.Count; i++)
        {
            int playerIndex = i; // Create a local copy for the closure
            if (playerScoreButtons[i] != null)
            {
                Button buttonComponent = playerScoreButtons[i].GetComponentInParent<Button>();
                if (buttonComponent != null)
                {
                    buttonComponent.onClick.AddListener(() => OnPlayerScoreButtonClicked(playerIndex));
                }
            }
        }
    }

    public void UpdateUI()
    {
        gameManager = GameManager.Instance;

        if (gameManager == null)
        {
            // Try to find GameManager again
            gameManager = GameManager.Instance;

            // If still null, log warning and return
            if (gameManager == null)
            {
                Debug.LogWarning("Cannot update UI: GameManager is null");
                return;
            }
        }

        // Update action indicators
        UpdateActionIndicators();

        // Update player scores
        UpdatePlayerScores();

        // Update button states
        UpdateButtonStates();
    }

    private void UpdateActionIndicators()
    {
        int availableActions = gameManager.ActionsRemaining;

        // Update visibility of action indicators
        for (int i = 0; i < actionIndicators.Count; i++)
        {
            if (actionIndicators[i] != null)
            {
                // Make indicators visible based on available actions
                actionIndicators[i].SetActive(i < availableActions);
            }
        }
    }

    public Button GetUndoButton()
    {
        return undoButton;
    }

    private void UpdatePlayerScores()
    {
        // Update score display for each player
        List<Player> players = gameManager.Players;
        int playerCount = gameManager.PlayerCount;
        int currentPlayerIndex = gameManager.CurrentPlayerIndex;

        for (int i = 0; i < playerScoreButtons.Count && i < playerCount; i++)
        {
            TextMeshProUGUI scoreButton = playerScoreButtons[i];

            if (scoreButton != null && i < players.Count)
            {
                Player player = players[i];
                scoreButton.text = $"{player.VictoryPoints}";

                // Bold text for current player
                scoreButton.fontStyle = (i == currentPlayerIndex) ?
                    FontStyles.Bold : FontStyles.Normal;

                // Optionally highlight the button of the current player
                Button buttonComponent = scoreButton.GetComponentInParent<Button>();
                if (buttonComponent != null)
                {
                    // You could change the button's color or visual state here
                    // For example with a color tint
                    ColorBlock colors = buttonComponent.colors;
                    if (i == currentPlayerIndex)
                    {
                        // Highlight current player button
                        colors.normalColor = new Color(1.0f, 0.8f, 0.8f, 1.0f); // Light highlight
                    }
                    else
                    {
                        // Regular color for other player buttons
                        colors.normalColor = Color.white;
                    }
                    buttonComponent.colors = colors;
                }
            }
        }
    }

    private void UpdateButtonStates()
    {
        // Update undo/redo button states
        if (undoButton != null)
        {
            // Check both UndoManager and GameManager
            bool canUndo = false;

            // Check UndoManager first
            if (UndoManager.Instance != null)
            {
                canUndo |= UndoManager.Instance.CanUndo();
            }

            // Also check GameManager for backwards compatibility
            if (gameManager != null)
            {
                canUndo |= gameManager.CanUndo();
            }

            undoButton.interactable = canUndo;
        }

        if (redoButton != null)
        {
            UndoManager undoManager = UndoManager.Instance;
            redoButton.interactable = undoManager != null && undoManager.CanRedo();
        }
    }

    // Method to update player count dynamically during game
    public void UpdatePlayerCount(int newPlayerCount)
    {
        // Make sure player count is valid (1-4)
        newPlayerCount = Mathf.Clamp(newPlayerCount, 1, 4);

        // Show/hide buttons and rockets based on new player count
        for (int i = 0; i < playerScoreButtons.Count; i++)
        {
            bool isActive = (i < newPlayerCount);

            // Set score button visibility
            if (playerScoreButtons[i] != null)
            {
                Button buttonComponent = playerScoreButtons[i].GetComponentInParent<Button>();
                if (buttonComponent != null)
                {
                    buttonComponent.gameObject.SetActive(isActive);
                }
                else
                {
                    playerScoreButtons[i].gameObject.SetActive(isActive);
                }
            }

            // Set rocket visibility
            if (i < playerRockets.Count && playerRockets[i] != null)
            {
                playerRockets[i].SetActive(isActive);
            }
        }

        // Update the GameManager's player count if needed
        // This would require adding a method to GameManager to handle this
    }

    // Handler for player score button clicks
    // Then add this to the OnPlayerScoreButtonClicked method in GameUI.cs
    private void OnPlayerScoreButtonClicked(int playerIndex)
    {

        // Toggle camera to this player's area
        if (cameraController != null)
        {
            cameraController.TogglePlayerCamera(playerIndex);
        }

        // Notify player selection manager or other systems about the selected player
        PlayerSelectionManager selectionManager = FindFirstObjectByType<PlayerSelectionManager>();
        if (selectionManager != null && gameManager != null && playerIndex < gameManager.Players.Count)
        {
            selectionManager.OnPlayerSelected(playerIndex, gameManager.Players[playerIndex]);
        }
    }

    // Button click handlers
    private void OnEndTurnClicked()
    {
        if (gameManager != null)
        {
            gameManager.EndTurn();
            UpdateUI();
        }
    }

    private void OnUndoClicked()
    {
        bool undoHandled = false;

        // Try UndoManager first
        if (UndoManager.Instance != null && UndoManager.Instance.CanUndo())
        {
            UndoManager.Instance.Undo();
            undoHandled = true;
        }

        // Fall back to GameManager if needed
        if (!undoHandled && gameManager != null && gameManager.CanUndo())
        {
            gameManager.Undo();
        }

        // Ensure UI is updated
        UpdateUI();
    }

    private void OnRedoClicked()
    {
        UndoManager undoManager = UndoManager.Instance;
        if (undoManager != null && undoManager.CanRedo())
        {
            undoManager.Redo();
            UpdateUI();
        }
    }

    // Public methods that can be called from other scripts

    // Call this when an action is used
    public void OnActionUsed()
    {
        UpdateUI();
    }

    // Call this to refresh the UI when game state changes
    public void RefreshUI()
    {
        UpdateUI();
    }

    // Reinitialize action indicators when player changes or max actions change
    public void ReinitializeActionIndicators()
    {
        InitializeActionIndicators();
        UpdateActionIndicators();
    }

    // Activation Instructions methods
    public void ShowActivationInstructions(string message)
    {
        if (activationInstructionsText != null)
        {
            activationInstructionsText.text = message;
            activationInstructionsText.gameObject.SetActive(true);
        }
        else
        {
            Debug.LogWarning("Activation instructions text field is not assigned in GameUI!");
        }
    }

    public void HideActivationInstructions()
    {
        if (activationInstructionsText != null)
        {
            activationInstructionsText.gameObject.SetActive(false);
        }
    }
}