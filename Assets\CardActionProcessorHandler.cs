using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;

public class CardActionProcessorHandler
{
    private CardActionButtonUI buttonUI;
    private CardActionIndicatorManager indicatorManager;

    public CardActionProcessorHandler(CardActionButtonUI buttonUI, CardActionIndicatorManager indicatorManager)
    {
        this.buttonUI = buttonUI;
        this.indicatorManager = indicatorManager;
    }

    public void OnEarthProcessorButtonClicked()
    {
        // Clear all buttons and indicators first
        buttonUI.HideAllButtons();
        indicatorManager.ClearAllIndicators();

        ShowProcessorOptions();
    }

    private void ShowProcessorOptions()
    {
        // Clean up any existing processor icons first
        buttonUI.CleanupProcessorIcons();

        List<ProcessorRecipe> recipes = GetProcessorRecipes();

        for (int i = 0; i < recipes.Count && i < 8; i++)
        {
            SetupProcessorButton(i, recipes[i]);
        }
    }

    private List<ProcessorRecipe> GetProcessorRecipes()
    {
        return new List<ProcessorRecipe>
        {
            new ProcessorRecipe(
                new List<ResourceType> { ResourceType.Carbon },
                new List<ResourceType> { ResourceType.Fuel }),
            new ProcessorRecipe(
                new List<ResourceType> { ResourceType.Ice },
                new List<ResourceType> { ResourceType.Fuel }),
            new ProcessorRecipe(
                new List<ResourceType> { ResourceType.Ore, ResourceType.Silicon },
                new List<ResourceType> { ResourceType.Ceramics }),
            new ProcessorRecipe(
                new List<ResourceType> { ResourceType.Ore, ResourceType.Ore, ResourceType.Carbon },
                new List<ResourceType> { ResourceType.Alloys, ResourceType.Alloys }),
            new ProcessorRecipe(
                new List<ResourceType> { ResourceType.Carbon, ResourceType.Carbon },
                new List<ResourceType> { ResourceType.Graphene }),
            new ProcessorRecipe(
                new List<ResourceType> { ResourceType.Silicon, ResourceType.RareEarths },
                new List<ResourceType> { ResourceType.Microchips })
        };
    }

    private void SetupProcessorButton(int buttonIndex, ProcessorRecipe recipe)
    {
        Button button = buttonUI.GetButton(buttonIndex);
        if (button == null) return;

        // Create icon layout for processor button
        CreateProcessorIconLayout(button, recipe);

        button.onClick.RemoveAllListeners();
        button.onClick.AddListener(() => OnProcessRecipeClicked(recipe));
        button.gameObject.SetActive(true);
    }

    private void CreateProcessorIconLayout(Button button, ProcessorRecipe recipe)
    {
        // Hide original text
        TextMeshProUGUI buttonText = button.GetComponentInChildren<TextMeshProUGUI>();
        if (buttonText != null)
        {
            buttonText.text = "";
        }

        // Create a horizontal layout for icons
        GameObject layout = new GameObject("IconLayout");
        RectTransform layoutRect = layout.AddComponent<RectTransform>();
        HorizontalLayoutGroup group = layout.AddComponent<HorizontalLayoutGroup>();

        // Configure layout
        layout.transform.SetParent(button.transform, false);
        group.childAlignment = TextAnchor.MiddleCenter;
        group.spacing = -5;
        group.padding = new RectOffset(-237, 0, 0, 0);
        group.childControlWidth = false;
        group.childControlHeight = true;
        group.childForceExpandWidth = false;
        group.childForceExpandHeight = false;

        // Make layout fill the button
        layoutRect.anchorMin = new Vector2(0, 0);
        layoutRect.anchorMax = new Vector2(1, 1);
        layoutRect.sizeDelta = Vector2.zero;

        ResourceManager resourceManager = ResourceManager.Instance;
        if (resourceManager != null)
        {
            // Add input icons
            foreach (ResourceType type in recipe.Inputs)
            {
                AddResourceIcon(type, layout.transform, resourceManager);
            }

            // Add arrow
            GameObject arrowObj = new GameObject("Arrow");
            RectTransform arrowRect = arrowObj.AddComponent<RectTransform>();
            TextMeshProUGUI arrowText = arrowObj.AddComponent<TextMeshProUGUI>();

            arrowObj.transform.SetParent(layout.transform, false);
            arrowText.text = ">";
            arrowText.fontSize = 24;
            arrowText.color = Color.white;
            arrowText.alignment = TextAlignmentOptions.Center;
            arrowRect.sizeDelta = new Vector2(20, 30);

            // Add output icons
            foreach (ResourceType type in recipe.Outputs)
            {
                AddResourceIcon(type, layout.transform, resourceManager);
            }
        }
    }

    private void AddResourceIcon(ResourceType type, Transform parent, ResourceManager resourceManager)
    {
        GameObject iconObj = new GameObject("Icon_" + type.ToString());
        RectTransform rect = iconObj.AddComponent<RectTransform>();
        Image image = iconObj.AddComponent<Image>();

        iconObj.transform.SetParent(parent, false);

        Sprite icon = resourceManager.GetResourceIcon(type);
        if (icon != null)
        {
            image.sprite = icon;
            image.preserveAspect = true;
        }

        rect.sizeDelta = new Vector2(35, 35);
    }

    private void OnProcessRecipeClicked(ProcessorRecipe recipe)
    {
        ProcessorRecipeExecutor.Execute(recipe);

        // Clean up before refreshing to prevent duplicates
        buttonUI.CleanupProcessorIcons();
        ShowProcessorOptions(); // Refresh the options
    }

    public void ClearProcessorUI()
    {
        buttonUI.CleanupProcessorIcons();
        buttonUI.HideAllButtons();
        indicatorManager.ClearAllIndicators();
    }

}