using UnityEngine;

/// <summary>
/// Initializes the ShipMapTracker in the scene and assigns ship prefabs
/// </summary>
public class ShipMapTrackerInitializer : MonoBehaviour
{
    [Header("Ship Prefab References")]
    [SerializeField] private GameObject redShipPrefab;
    [SerializeField] private GameObject greenShipPrefab;
    [SerializeField] private GameObject blueShipPrefab;
    [SerializeField] private GameObject yellowShipPrefab;
    
    private void Start()
    {
        InitializeShipMapTracker();
    }
    
    private void InitializeShipMapTracker()
    {
        // Check if ShipMapTracker already exists
        if (ShipMapTracker.Instance != null)
        {
            Debug.Log("ShipMapTracker already exists in scene");
            // Still initialize prefabs if they're not set
            ShipMapTracker.Instance.InitializeShipPrefabs(redShipPrefab, greenShipPrefab, blueShipPrefab, yellowShipPrefab);
            return;
        }

        // Create ShipMapTracker GameObject
        GameObject trackerObject = new GameObject("ShipMapTracker");
        ShipMapTracker tracker = trackerObject.AddComponent<ShipMapTracker>();

        // Initialize prefabs
        tracker.InitializeShipPrefabs(redShipPrefab, greenShipPrefab, blueShipPrefab, yellowShipPrefab);
    }
}
