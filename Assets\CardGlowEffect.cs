using UnityEngine;
using UnityEngine.EventSystems;

public class CardGlowEffect : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerEnterHandler, IPointerExitHandler
{
    [Header("Glow Settings")]
    [SerializeField] private Color glowColor = new Color(1f, 1f, 1f, 1f); // Yellow glow
    [SerializeField] private float glowIntensity = 1.9f;
    [SerializeField] private float transitionSpeed = 5f;
    
    private Material materialInstance;
    private Renderer cardRenderer;
    private Color originalColor;
    private Color targetColor;
    private bool isHovering = false;
    private bool canGlow = true;
    
    private void Awake()
    {

        cardRenderer = GetComponent<Renderer>();
        if (cardRenderer == null)
        {
            cardRenderer = GetComponent<MeshRenderer>();
        }

        if (cardRenderer != null && cardRenderer.material != null)
        {
            // Create a material instance to avoid modifying shared materials
            materialInstance = new Material(cardRenderer.material);
            cardRenderer.material = materialInstance;
            originalColor = materialInstance.color;
            targetColor = originalColor;
        }
    }
    
    public void SetCanGlow(bool value)
    {
        canGlow = value;
        if (!canGlow && isHovering)
        {
            ResetGlow();
        }
    }

    /// <summary>
    /// Refresh the material instance - used when BuildModeGlowEffect has finished using the renderer
    /// </summary>
    public void RefreshMaterial()
    {
        if (cardRenderer != null && cardRenderer.sharedMaterial != null)
        {
            // Destroy old material instance if it exists
            if (materialInstance != null)
            {
                Destroy(materialInstance);
            }

            // Create a new material instance from the current shared material
            materialInstance = new Material(cardRenderer.sharedMaterial);
            cardRenderer.material = materialInstance;
            originalColor = materialInstance.color;
            targetColor = originalColor;
        }
    }
    
    public void OnPointerEnter(PointerEventData eventData)
    {
        if (canGlow)
        {
            isHovering = true;
            targetColor = originalColor * glowColor * glowIntensity;
        }
    }
    
    public void OnPointerExit(PointerEventData eventData)
    {
        ResetGlow();
    }
    
    private void ResetGlow()
    {
        isHovering = false;
        targetColor = originalColor;
    }
    
    private void Update()
    {
        if (materialInstance != null)
        {
            // Always lerp towards the target color, whether hovering or not
            materialInstance.color = Color.Lerp(materialInstance.color, targetColor, Time.deltaTime * transitionSpeed);
        }
    }
    
    private void OnDestroy()
    {
        if (materialInstance != null)
        {
            Destroy(materialInstance);
        }
    }
}