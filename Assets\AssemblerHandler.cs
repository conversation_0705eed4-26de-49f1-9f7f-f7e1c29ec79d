using UnityEngine;
using System.Collections.Generic;

public class AssemblerHandler : MonoBehaviour
{
    public static AssemblerHandler Instance { get; private set; }
    public static bool IsBuildModeActive => Instance != null && Instance.currentState == BuildSelectionState.Active;
    private static bool suppressNextCardClick = false;

    private enum BuildSelectionState
    {
        Inactive,
        Active
    }
    
    private BuildSelectionState currentState = BuildSelectionState.Inactive;
    private GameObject buildLocation;
    private List<GameObject> glowingTechs = new List<GameObject>();
    
    private void Awake()
    {
        if (Instance != null && Instance != this)
        {
            Destroy(gameObject);
            return;
        }
        Instance = this;
    }
    
    private void Update()
    {
        if (currentState == BuildSelectionState.Active && Input.GetMouseButtonDown(0))
        {
            HandleBuildModeClick();
        }
    }

    public static void SuppressNextCardClick()
    {
        suppressNextCardClick = true;
    }


    public static bool ShouldSuppressCardClick()
    {
        if (suppressNextCardClick)
        {
            suppressNextCardClick = false;
            return true;
        }
        return false;
    }

    public void OnAssemblerButtonClicked(GameObject location)
    {
        buildLocation = location;
        EnterBuildSelectionMode();
    }

    private void EnterBuildSelectionMode()
    {
        currentState = BuildSelectionState.Active;

        // Hide detail panel
        CardDetailDisplay detailDisplay = CardDetailDisplay.Instance;
        if (detailDisplay != null)
        {
            detailDisplay.Hide();
        }

        // Show activation instructions
        GameUI gameUI = FindFirstObjectByType<GameUI>();
        if (gameUI != null)
        {
            gameUI.ShowActivationInstructions("Pick technology to build");
        }

        // Move camera to player area only if not already there
        GameManager gameManager = GameManager.Instance;
        PlayerCameraController cameraController = FindFirstObjectByType<PlayerCameraController>();
        if (gameManager != null && cameraController != null)
        {
            // Only toggle if we're not already at this player's camera
            Camera activeCamera = cameraController.GetActiveCamera();
            Camera targetPlayerCamera = cameraController.playerCameras != null &&
                                       gameManager.CurrentPlayerIndex < cameraController.playerCameras.Length ?
                                       cameraController.playerCameras[gameManager.CurrentPlayerIndex] : null;

            if (activeCamera != targetPlayerCamera)
            {
                cameraController.TogglePlayerCamera(gameManager.CurrentPlayerIndex);
            }
        }

        // Make buildable techs glow
        HighlightBuildableTechs();
    }


    private void ExitBuildSelectionMode()
    {
        currentState = BuildSelectionState.Inactive;

        // Stop all glowing
        foreach (GameObject tech in glowingTechs)
        {
            if (tech != null)
            {
                BuildModeGlowEffect glowEffect = tech.GetComponent<BuildModeGlowEffect>();
                if (glowEffect != null)
                {
                    glowEffect.SetBuildModeGlow(false);
                }
            }
        }
        glowingTechs.Clear();
        buildLocation = null;

        // Hide activation instructions
        GameUI gameUI = FindFirstObjectByType<GameUI>();
        if (gameUI != null)
        {
            gameUI.HideActivationInstructions();
        }
    }
    private void HighlightBuildableTechs()
    {
        GameManager gameManager = GameManager.Instance;
        PlayAreaManager playAreaManager = PlayAreaManager.Instance;

        if (gameManager == null || playAreaManager == null)
            return;

        Player currentPlayer = gameManager.CurrentPlayer;
        if (currentPlayer == null)
            return;

        PlayerPlayArea playerArea = playAreaManager.GetPlayerPlayArea(gameManager.CurrentPlayerIndex);
        if (playerArea == null)
            return;

        List<GameObject> techCards = playerArea.GetTechnologyCards();

        foreach (GameObject techCard in techCards)
        {
            if (IsTechBuildable(techCard, currentPlayer))
            {
                BuildModeGlowEffect glowEffect = techCard.GetComponent<BuildModeGlowEffect>();
                if (glowEffect == null)
                {
                    glowEffect = techCard.AddComponent<BuildModeGlowEffect>();
                }

                glowEffect.SetBuildModeGlow(true);
                glowingTechs.Add(techCard);
            }
        }
    }

    private bool IsTechBuildable(GameObject techCard, Player player)
    {
        // Get card data from the tech card
        Card3DClickHandler clickHandler = techCard.GetComponent<Card3DClickHandler>();
        if (clickHandler == null)
        {
            Debug.Log("[AssemblerHandler] No click handler found");
            return false;
        }

        CardData cardData = GetCardDataFromClickHandler(clickHandler);
        if (cardData == null)
        {
            Debug.Log("[AssemblerHandler] No card data found");
            return false;
        }

        // Check if it's a buildable type
        bool isBuildable = IsCardBuildableType(cardData);
        if (!isBuildable)
            return false;

        // Check if player has required resources
        bool hasResources = HasRequiredBuildResources(player, cardData);
        return hasResources;
    }
    
    private CardData GetCardDataFromClickHandler(Card3DClickHandler clickHandler)
    {
        System.Reflection.FieldInfo cardDataField = typeof(Card3DClickHandler).GetField("cardData",
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
        if (cardDataField != null)
        {
            return cardDataField.GetValue(clickHandler) as CardData;
        }
        return null;
    }
    
    private bool IsCardBuildableType(CardData cardData)
    {
        string type = cardData.Type?.ToLower() ?? "";
        string subType = cardData.SubType?.ToLower() ?? "";
        string effect = cardData.Effect?.ToLower() ?? "";
        
        return subType.Contains("module") || 
               subType.Contains("facility") || 
               subType.Contains("ship") || 
               type.Contains("wonder") ||
               effect.Contains("wonder");
    }

    private bool HasRequiredBuildResources(Player player, CardData cardData)
    {
        if (cardData.BuildCost == null || cardData.BuildCost.Count == 0)
        {
            Debug.Log($"[AssemblerHandler] {cardData.Name} has no build cost - buildable");
            return true;
        }

        foreach (Resource resource in cardData.BuildCost)
        {
            int available = player.GetResourceAmount(buildLocation, resource.Type);
            if (available < resource.Amount)
            {
                return false;
            }
        }

        return true;
    }

    private void HandleBuildModeClick()
    {
        PlayerCameraController cameraController = FindFirstObjectByType<PlayerCameraController>();
        Camera activeCamera = cameraController?.GetActiveCamera();

        if (activeCamera == null)
        {
            activeCamera = Camera.main;
            if (activeCamera == null)
            {
                activeCamera = FindFirstObjectByType<Camera>();
            }
        }

        if (activeCamera == null)
        {
            Debug.LogError("No camera found for raycast");
            ExitBuildSelectionMode();
            return;
        }

        Ray ray = activeCamera.ScreenPointToRay(Input.mousePosition);
        RaycastHit hit;

        if (Physics.Raycast(ray, out hit))
        {
            GameObject clickedObject = hit.collider.gameObject;

            // Check if clicked object is a glowing tech
            if (glowingTechs.Contains(clickedObject))
            {
                // Suppress the card's click handler
                SuppressNextCardClick();
                BuildTech(clickedObject);
                return;
            }
        }

        ExitBuildSelectionMode();
    }

    private void BuildTech(GameObject techCard)
    {
        GameManager gameManager = GameManager.Instance;
        Player currentPlayer = gameManager.CurrentPlayer;

        Card3DClickHandler clickHandler = techCard.GetComponent<Card3DClickHandler>();
        CardData cardData = GetCardDataFromClickHandler(clickHandler);

        if (cardData == null || currentPlayer == null)
        {
            ExitBuildSelectionMode();
            return;
        }

        // Use action
        if (!gameManager.UseAction())
        {
            ExitBuildSelectionMode();
            return;
        }

        // Create undoable action before making changes
        bool isShip = cardData.SubType?.ToLower().Contains("ship") == true;
        BuildStructureAction buildAction = new BuildStructureAction(
            cardData, cardData.BuildCost, currentPlayer.PlayerId, buildLocation, isShip);

        // Deduct resources
        foreach (Resource resource in cardData.BuildCost)
        {
            currentPlayer.UseResource(buildLocation, resource.Type, resource.Amount);
        }

        // Create the built item based on type
        CreateBuiltItem(cardData, currentPlayer);

        GameLogManager logManager = GameLogManager.Instance;
        if (logManager != null)
        {
            string type = isShip ? "ship" : "module";
            logManager.AddLog($"Player {currentPlayer.PlayerId + 1} built {cardData.Name} ({type})");
        }

        // Register the undoable action
        UndoManager undoManager = UndoManager.Instance;
        undoManager?.RegisterAction(buildAction);

        // Update play area
        PlayAreaManager playAreaManager = PlayAreaManager.Instance;
        if (playAreaManager != null)
        {
            playAreaManager.RefreshPlayerPlayArea(gameManager.CurrentPlayerIndex);
        }

        // Update UI
        GameUI gameUI = FindFirstObjectByType<GameUI>();
        if (gameUI != null)
        {
            gameUI.RefreshUI();
        }

        ExitBuildSelectionMode();
    }
    
    private void CreateBuiltItem(CardData cardData, Player player)
    {
        string subType = cardData.SubType?.ToLower() ?? "";
        
        if (subType.Contains("ship"))
        {
            CreateShip(cardData, player);
        }
        else if (subType.Contains("module") || subType.Contains("facility") || cardData.Type?.ToLower().Contains("wonder") == true)
        {
            CreateModule(cardData, player);
        }
    }
    
    private void CreateShip(CardData cardData, Player player)
    {
        Ship ship = new Ship
        {
            Name = cardData.Name,
            CurrentLocation = buildLocation,
            DeltaVPerFuel = GetFloatFromParams(cardData, "deltaVPerFuel", 4f),
            CargoCapacity = GetIntFromParams(cardData, "cargoCapacity", 1),
            Strength = GetIntFromParams(cardData, "shipStrength", 0),
            IsConsumedOnSurvey = GetBoolFromParams(cardData, "consumedOnSurvey", false)
        };
        
        player.AddShip(ship);
        player.ShipsInPlay++;
        
        // Register with location (planet or orbit)
        PlanetBody planetBody = buildLocation.GetComponent<PlanetBody>();
        if (planetBody != null)
        {
            planetBody.RegisterShipArrival(ship, player);
        }

        OrbitLocation orbitLocation = buildLocation.GetComponent<OrbitLocation>();
        if (orbitLocation != null)
        {
            orbitLocation.RegisterShipArrival(ship, player);
        }
    }

    private void CreateModule(CardData cardData, Player player)
    {
        Module module = new Module
        {
            Name = cardData.Name,
            Type = GetModuleTypeFromSubType(cardData.SubType),
            PowerOutput = cardData.PowerOutput,
            PowerRequired = cardData.PowerRequired,
            IsWonder = cardData.IsWonder || cardData.Type?.ToLower().Contains("wonder") == true,
            VictoryPointValue = GetVPFromEffect(cardData.Effect),
            ProcessorDescription = cardData.Effect,
            Tier = cardData.Tier
        };

        player.AddModule(buildLocation, module);
    }

    
    private float GetFloatFromParams(CardData cardData, string key, float defaultValue)
    {
        if (cardData.EffectParams != null && cardData.EffectParams.ContainsKey(key))
        {
            return System.Convert.ToSingle(cardData.EffectParams[key]);
        }
        return defaultValue;
    }
    
    private int GetIntFromParams(CardData cardData, string key, int defaultValue)
    {
        if (cardData.EffectParams != null && cardData.EffectParams.ContainsKey(key))
        {
            return System.Convert.ToInt32(cardData.EffectParams[key]);
        }
        return defaultValue;
    }
    
    private bool GetBoolFromParams(CardData cardData, string key, bool defaultValue)
    {
        if (cardData.EffectParams != null && cardData.EffectParams.ContainsKey(key))
        {
            return System.Convert.ToBoolean(cardData.EffectParams[key]);
        }
        return defaultValue;
    }
    
    private ModuleType GetModuleTypeFromSubType(string subType)
    {
        if (string.IsNullOrEmpty(subType))
            return ModuleType.Other;
            
        string lower = subType.ToLower();
        if (lower.Contains("power"))
            return ModuleType.Power;
        if (lower.Contains("extractor"))
            return ModuleType.Extractor;
        if (lower.Contains("processor"))
            return ModuleType.Processor;
        if (lower.Contains("wonder"))
            return ModuleType.Wonder;
            
        return ModuleType.Other;
    }
    
    private int GetVPFromEffect(string effect)
    {
        if (string.IsNullOrEmpty(effect))
            return 0;
            
        System.Text.RegularExpressions.Match match = 
            System.Text.RegularExpressions.Regex.Match(effect, @"(\d+)\s*(?:VP|Victory\s*Points?)", 
                System.Text.RegularExpressions.RegexOptions.IgnoreCase);
                
        if (match.Success)
        {
            return int.Parse(match.Groups[1].Value);
        }
        
        return 0;
    }
}