using System.Collections;
using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Handles animations for the card detail display
/// </summary>
public class CardUIAnimator
{
    // References to UI components
    private GameObject detailPanel;
    private CanvasGroup canvasGroup;
    private RectTransform rectTransform;
    
    // Animation settings
    private float animationDuration = 0.3f;
    private AnimationCurve animationCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
    
    // Animation state
    private Coroutine currentAnimation;
    private MonoBehaviour coroutineRunner;
    
    public CardUIAnimator(GameObject detailPanel, float animationDuration, AnimationCurve animationCurve, MonoBehaviour coroutineRunner)
    {
        this.detailPanel = detailPanel;
        this.animationDuration = animationDuration;
        this.animationCurve = animationCurve;
        this.coroutineRunner = coroutineRunner;
        
        // Get or add required components
        this.canvasGroup = GetOrAddCanvasGroup();
        this.rectTransform = detailPanel.GetComponent<RectTransform>();
        
        if (this.rectTransform == null)
        {
            Debug.LogError("CardUIAnimator: DetailPanel does not have a RectTransform component!");
        }
    }
    
    /// <summary>
    /// Get or add a CanvasGroup component to the detail panel
    /// </summary>
    private CanvasGroup GetOrAddCanvasGroup()
    {
        CanvasGroup group = detailPanel.GetComponent<CanvasGroup>();
        if (group == null)
        {
            group = detailPanel.AddComponent<CanvasGroup>();
        }
        return group;
    }
    
    /// <summary>
    /// Animate showing the card detail panel
    /// </summary>
    public void ShowAnimation()
    {
        // Stop any current animation
        StopCurrentAnimation();
        
        // Start the show animation
        currentAnimation = coroutineRunner.StartCoroutine(AnimateShow());
    }
    
    /// <summary>
    /// Animate hiding the card detail panel
    /// </summary>
    public void HideAnimation(System.Action onComplete = null)
    {
        // Stop any current animation
        StopCurrentAnimation();
        
        // Start the hide animation
        currentAnimation = coroutineRunner.StartCoroutine(AnimateHide(onComplete));
    }
    
    /// <summary>
    /// Stop the current animation if one is playing
    /// </summary>
    private void StopCurrentAnimation()
    {
        if (currentAnimation != null)
        {
            coroutineRunner.StopCoroutine(currentAnimation);
            currentAnimation = null;
        }
    }
    
    /// <summary>
    /// Coroutine to animate showing the card detail panel
    /// </summary>
    private IEnumerator AnimateShow()
    {
        if (canvasGroup == null || rectTransform == null) yield break;
        
        // Start animation
        float elapsedTime = 0f;
        canvasGroup.alpha = 0f;
        rectTransform.localScale = Vector3.one * 0.8f;
        
        while (elapsedTime < animationDuration)
        {
            float t = animationCurve.Evaluate(elapsedTime / animationDuration);
            canvasGroup.alpha = t;
            rectTransform.localScale = Vector3.LerpUnclamped(Vector3.one * 0.8f, Vector3.one, t);
            
            elapsedTime += Time.deltaTime;
            yield return null;
        }
        
        // Ensure final state is set
        canvasGroup.alpha = 1f;
        rectTransform.localScale = Vector3.one;
        
        currentAnimation = null;
    }
    
    /// <summary>
    /// Coroutine to animate hiding the card detail panel
    /// </summary>
    private IEnumerator AnimateHide(System.Action onComplete)
    {
        if (canvasGroup == null || rectTransform == null) yield break;
        
        // Start animation
        float elapsedTime = 0f;
        
        while (elapsedTime < animationDuration)
        {
            float t = animationCurve.Evaluate(1f - (elapsedTime / animationDuration));
            canvasGroup.alpha = t;
            rectTransform.localScale = Vector3.LerpUnclamped(Vector3.one * 0.8f, Vector3.one, t);
            
            elapsedTime += Time.deltaTime;
            yield return null;
        }
        
        // Ensure final state is set
        canvasGroup.alpha = 0f;
        detailPanel.SetActive(false);
        
        // Call the completion callback if provided
        onComplete?.Invoke();
        
        currentAnimation = null;
    }
}