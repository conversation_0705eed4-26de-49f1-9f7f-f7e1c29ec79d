using UnityEngine;

/// <summary>
/// Undoable action for developing a technology
/// </summary>
public class DevelopTechnologyAction : UndoableAction
{
    private CardData cardData;
    private TechnologyCardData techData;
    private int scienceCost;
    private int playerId;
    
    public DevelopTechnologyAction(CardData cardData, TechnologyCardData techData, int scienceCost, int playerId)
    {
        this.cardData = cardData;
        this.techData = techData;
        this.scienceCost = scienceCost;
        this.playerId = playerId;
    }

    public override void Undo()
    {   
        // Get references
        GameManager gameManager = GameManager.Instance;
        if (gameManager == null || playerId >= gameManager.Players.Count)
            return;

        Player player = gameManager.Players[playerId];
        if (player == null)
            return;

        // Remove the technology from the player
        player.RemoveTechnology(techData.cardName);

        // Refund the science cost
        player.ScienceValue += scienceCost;

        // Refund the action
        if (gameManager.CurrentPlayerIndex == playerId)
        {
            gameManager.RefundAction();
        }

        // Add the card back to hand
        CardToHandSystem cardToHandSystem = CardToHandSystem.Instance;
        if (cardToHandSystem != null)
        {
            cardToHandSystem.AddCardToHand(cardData);
        }

        GameLogManager logManager = GameLogManager.Instance;
        if (logManager != null)
        {
            logManager.RemoveLastLog();
        }

        // IMPORTANT: Remove the technology card from the play area BEFORE refreshing
        PlayAreaManager playAreaManager = PlayAreaManager.Instance;
        if (playAreaManager != null)
        {
            // First get the player's play area and manually remove the technology card
            PlayerPlayArea playerArea = playAreaManager.GetPlayerPlayArea(playerId);
            if (playerArea != null)
            {
                playerArea.RemoveTechnologyCard(techData.cardName);
            }
            
            // Then refresh the play area to ensure everything is synchronized
            playAreaManager.RefreshPlayerPlayArea(playerId);
        }
    }

    public override void Redo()
    {
        // Get references
        GameManager gameManager = GameManager.Instance;
        if (gameManager == null || playerId >= gameManager.Players.Count)
            return;

        Player player = gameManager.Players[playerId];
        if (player == null)
            return;

        // Deduct science cost
        player.ScienceValue -= scienceCost;

        // Add to player's technologies again
        player.AddTechnology(techData);

        // Use up an action if it's the current player
        if (gameManager.CurrentPlayerIndex == playerId)
        {
            gameManager.UseAction();
        }

        GameLogManager logManager = GameLogManager.Instance;
        if (logManager != null)
        {
            logManager.AddLog($"Player {gameManager.CurrentPlayer.PlayerId + 1} developed {cardData.Name}");
        }

        // Remove the card from hand
        CardToHandSystem cardToHandSystem = CardToHandSystem.Instance;
        if (cardToHandSystem != null)
        {
            cardToHandSystem.RemoveCardFromHand(cardData);
        }

        // Update player's play area
        PlayAreaManager playAreaManager = PlayAreaManager.Instance;
        if (playAreaManager != null)
        {
            playAreaManager.RefreshPlayerPlayArea(playerId);
        }
    }
}