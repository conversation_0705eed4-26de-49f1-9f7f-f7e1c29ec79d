using System.Collections.Generic;
using UnityEngine; 

public class GenericInitiativeEffect : ICardEffect
{
    private string name;
    private string description;
    
    public GenericInitiativeEffect(string name, string description)
    {
        this.name = name;
        this.description = description;
    }
    
    public bool CanExecute(Player player, GameObject location, Dictionary<string, object> parameters)
    {
        return true;
    }
    
    public bool Execute(Player player, GameObject location, Dictionary<string, object> parameters)
    {
        Debug.Log($"Player {player.PlayerId} used initiative card: {name}");
        return true;
    }
    
    public string GetDescription()
    {
        return $"Initiative: {description}";
    }
}