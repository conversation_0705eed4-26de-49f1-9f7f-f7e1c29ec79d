// CardEffectRegistry.cs
using System.Collections.Generic;
using UnityEngine;
using System.Text.RegularExpressions;
using System;
using System.Linq;

public class CardEffectRegistry : MonoBehaviour
{
    private static CardEffectRegistry instance;
    public static CardEffectRegistry Instance => instance;
    
    private Dictionary<string, ICardEffect> cardEffects = new Dictionary<string, ICardEffect>();
    private ExcelCardParser cardParser;
    
    private void Awake()
    {
        instance = this;
        cardParser = GetComponent<ExcelCardParser>();
        RegisterAllEffects();
    }
    
    private void RegisterAllEffects()
    {
        // Parse all cards from CSV files
        var allCards = cardParser.ParseAllCards();
        
        foreach (var card in allCards)
        {
            ICardEffect effect = CreateEffectFromCardData(card);
            if (effect != null)
            {
                cardEffects[card.Name] = effect;
                Debug.Log($"Registered effect for card: {card.Name}");
            }
            else
            {
                Debug.LogWarning($"No effect created for card: {card.Name}");
            }
        }
        
        Debug.Log($"Registered {cardEffects.Count} card effects total");
    }
    
    private ICardEffect CreateEffectFromCardData(CardData card)
    {
        // Handle different card types based on type and sub-type
        string type = card.Type.ToLower();
        string subType = card.SubType.ToLower();
        string effect = card.Effect.ToLower();
        
        // Initiative cards (immediate effects)
        if (type.Contains("initiative"))
        {
            return CreateInitiativeEffect(card);
        }
        
        // Technology cards
        if (type.Contains("technology"))
        {
            // Processor modules
            if (subType.Contains("processor") || effect.Contains("activate:"))
            {
                return CreateProcessorEffect(card);
            }
            
            // Power modules
            if (subType.Contains("power") || card.PowerOutput > 0)
            {
                return CreatePowerEffect(card);
            }
            
            // Extractor modules
            if (subType.Contains("extractor") || effect.Contains("per deposit"))
            {
                return CreateExtractorEffect(card);
            }
            
            // Ship blueprints
            if (subType.Contains("ship"))
            {
                return CreateShipEffect(card);
            }
            
            // Facility/Module blueprints
            if (subType.Contains("facility") || subType.Contains("module"))
            {
                return CreateBlueprintEffect(card);
            }
            
            // Upgrade technologies
            if (subType.Contains("upgrade"))
            {
                return CreateUpgradeEffect(card);
            }
        }
        
        // Wonder cards
        if (type.Contains("wonder"))
        {
            return CreateWonderEffect(card);
        }
        
        // Default: try to parse as a generic effect
        return CreateGenericEffect(card);
    }
    
    private ICardEffect CreateProcessorEffect(CardData card)
    {
        // Parse input and output resources from effect text
        var inputs = ParseInputResources(card.Effect);
        var outputs = ParseOutputResources(card.Effect);
        int powerRequired = ParsePowerRequirement(card.Effect);
        
        // Special processors might have additional conditions
        if (card.Name.Contains("Vacuum Data Module"))
        {
            return new VacuumDataEffect(card.Name, powerRequired);
        }
        
        return new ResourceProcessingEffect(card.Name, powerRequired, inputs, outputs);
    }
    
    private ICardEffect CreatePowerEffect(CardData card)
    {
        int powerOutput = card.PowerOutput;
        if (powerOutput == 0)
        {
            // Try to parse from effect text
            var match = Regex.Match(card.Effect, @"(\d+)\s*power", RegexOptions.IgnoreCase);
            if (match.Success)
            {
                powerOutput = int.Parse(match.Groups[1].Value);
            }
        }
        
        return new PowerGenerationEffect(card.Name, powerOutput, card.BuildCost);
    }
    
    private ICardEffect CreateExtractorEffect(CardData card)
    {
        int powerRequired = ParsePowerRequirement(card.Effect);
        
        // Check if it has special extraction bonuses
        var match = Regex.Match(card.Effect, @"(\d+)\s*each.*per deposit", RegexOptions.IgnoreCase);
        int bonusPerDeposit = match.Success ? int.Parse(match.Groups[1].Value) : 1;
        
        return new ExtractorEffect(card.Name, powerRequired, bonusPerDeposit);
    }
    
    private ICardEffect CreateShipEffect(CardData card)
    {
        float deltaVPerFuel = 0;
        int cargoCapacity = 0;
        int strength = 0;
        bool isConsumedOnSurvey = false;
        
        // Parse from effect params or effect text
        if (card.EffectParams.ContainsKey("deltaVPerFuel"))
            deltaVPerFuel = Convert.ToSingle(card.EffectParams["deltaVPerFuel"]);
        else
        {
            var match = Regex.Match(card.Effect, @"(\d+)\s*delta-v per fuel", RegexOptions.IgnoreCase);
            if (match.Success)
                deltaVPerFuel = float.Parse(match.Groups[1].Value);
        }
        
        if (card.EffectParams.ContainsKey("cargoCapacity"))
            cargoCapacity = (int)card.EffectParams["cargoCapacity"];
        else
        {
            var match = Regex.Match(card.Effect, @"Cargo\s*(\d+)x(\d+)", RegexOptions.IgnoreCase);
            if (match.Success)
                cargoCapacity = int.Parse(match.Groups[1].Value) * int.Parse(match.Groups[2].Value);
        }
        
        if (card.EffectParams.ContainsKey("shipStrength"))
            strength = (int)card.EffectParams["shipStrength"];
        else
        {
            var match = Regex.Match(card.Effect, @"Strength\s*(\d+)", RegexOptions.IgnoreCase);
            if (match.Success)
                strength = int.Parse(match.Groups[1].Value);
        }
        
        if (card.EffectParams.ContainsKey("consumedOnSurvey"))
            isConsumedOnSurvey = (bool)card.EffectParams["consumedOnSurvey"];
        else
            isConsumedOnSurvey = card.Effect.ToLower().Contains("consumed on survey");
        
        return new ShipBuildEffect(card.Name, deltaVPerFuel, cargoCapacity, strength, card.BuildCost, isConsumedOnSurvey);
    }
    
    private ICardEffect CreateBlueprintEffect(CardData card)
    {
        // This is for non-ship buildable modules/facilities
        bool requiresAdvancedAssembler = card.SubType.ToLower().Contains("facility");
        return new ModuleBuildEffect(card.Name, card.BuildCost, requiresAdvancedAssembler);
        
    }
    
    private ICardEffect CreateUpgradeEffect(CardData card)
    {
        // Parse the upgrade effect
        string effect = card.Effect.ToLower();
        
        if (effect.Contains("additional power"))
        {
            var match = Regex.Match(effect, @"(\d+)\s*additional power", RegexOptions.IgnoreCase);
            int bonus = match.Success ? int.Parse(match.Groups[1].Value) : 1;
            return new TechnologyUpgradeEffect(card.Name, TechnologyEffectsManager.TechEffectType.PowerBonus, bonus);
        }
        
        if (effect.Contains("additional ore/ice"))
        {
            var match = Regex.Match(effect, @"(\d+)\s*additional", RegexOptions.IgnoreCase);
            int bonus = match.Success ? int.Parse(match.Groups[1].Value) : 1;
            return new TechnologyUpgradeEffect(card.Name, TechnologyEffectsManager.TechEffectType.OreIceExtractionBonus, bonus);
        }
        
        if (effect.Contains("delta-v per fuel") && effect.Contains("all ships"))
        {
            var match = Regex.Match(effect, @"\+(\d+)\s*delta-v", RegexOptions.IgnoreCase);
            float bonus = match.Success ? float.Parse(match.Groups[1].Value) : 1;
            return new TechnologyUpgradeEffect(card.Name, TechnologyEffectsManager.TechEffectType.DeltaVPerFuelBonus, bonus);
        }
        
        if (effect.Contains("+1 action"))
        {
            return new TechnologyUpgradeEffect(card.Name, TechnologyEffectsManager.TechEffectType.ActionBonus, 1);
        }
        
        // Generic upgrade
        return new GenericUpgradeEffect(card.Name, card.Effect);
    }
    
    private ICardEffect CreateWonderEffect(CardData card)
    {
        int victoryPoints = 1; // Default VP for wonders
        bool isPerTurn = card.Effect.ToLower().Contains("per turn");
        
        // Parse VP from effect if specified
        var match = Regex.Match(card.Effect, @"(\d+)\s*vp", RegexOptions.IgnoreCase);
        if (match.Success)
        {
            victoryPoints = int.Parse(match.Groups[1].Value);
        }
        
        return new WonderEffect(card.Name, victoryPoints, isPerTurn, card.BuildCost);
    }
    
    private ICardEffect CreateInitiativeEffect(CardData card)
    {
        // Initiative cards have immediate effects
        string effect = card.Effect.ToLower();
        
        if (effect.Contains("gain") && effect.Contains("science"))
        {
            var match = Regex.Match(effect, @"gain\s*(\d+)\s*science", RegexOptions.IgnoreCase);
            int amount = match.Success ? int.Parse(match.Groups[1].Value) : 1;
            return new GainScienceEffect(card.Name, amount);
        }
        
        if (effect.Contains("$"))
        {
            var match = Regex.Match(effect, @"\$(\d+)", RegexOptions.IgnoreCase);
            int amount = match.Success ? int.Parse(match.Groups[1].Value) : 1;
            return new GainMoneyEffect(card.Name, amount);
        }
        
        if (effect.Contains("steal"))
        {
            return new StealResourceEffect(card.Name);
        }
        
        // Generic initiative
        return new GenericInitiativeEffect(card.Name, card.Effect);
    }
    /*
    private ICardEffect CreateSpecialEffect(CardData card)
    {
        // Handle special cards like "Deep Space Antenna"
        if (card.Name.Contains("Deep Space Antenna"))
        {
            //return new DeepSpaceAntennaEffect();
        }
        
        //return new GenericEffect(card.Name, card.Effect);
    }
    */
    private ICardEffect CreateGenericEffect(CardData card)
    {
        return new GenericEffect(card.Name, card.Effect);
    }
    
    // Parsing helper methods
    private List<Resource> ParseInputResources(string effectText)
    {
        List<Resource> resources = new List<Resource>();
        
        // Look for pattern like "Activate: -X Resource, -Y Resource"
        var match = Regex.Match(effectText, @"Activate.*?:(.+?)[→,]", RegexOptions.IgnoreCase);
        if (match.Success)
        {
            string inputPart = match.Groups[1].Value;
            
            // Find all resource patterns like "-1 Carbon" or "-2 Ore"
            var resourceMatches = Regex.Matches(inputPart, @"-(\d+)\s+([a-zA-Z\s]+?)(?:,|$)");
            
            foreach (Match resourceMatch in resourceMatches)
            {
                int amount = int.Parse(resourceMatch.Groups[1].Value);
                string resourceName = resourceMatch.Groups[2].Value.Trim();
                ResourceType resourceType = ParseResourceType(resourceName);
                
                resources.Add(new Resource(resourceType, amount));
            }
        }
        
        return resources;
    }
    
    private List<Resource> ParseOutputResources(string effectText)
    {
        List<Resource> resources = new List<Resource>();
        
        // More flexible regex that can handle ": +1 Resource" format
        var match = Regex.Match(effectText, @"(?::|→|,)\s*\+(.+?)(?:$|\.|,)", RegexOptions.IgnoreCase);
        
        if (match.Success)
        {
            string outputPart = match.Groups[1].Value;
            
            // Find all resource patterns like "+1 Fuel" or "+2 Alloy"
            var resourceMatches = Regex.Matches(outputPart, @"(\d+)\s+([a-zA-Z\s\-]+?)(?:,|$|\s|\.)");
            
            foreach (Match resourceMatch in resourceMatches)
            {
                if (resourceMatch.Groups.Count >= 3)
                {
                    int amount = int.Parse(resourceMatch.Groups[1].Value);
                    string resourceName = resourceMatch.Groups[2].Value.Trim();
                    
                    ResourceType resourceType = ParseResourceType(resourceName);
                    
                    resources.Add(new Resource(resourceType, amount));
                }
            }
        }
        
        // Also check for this specific pattern: ": +1 Fuel"
        if (!resources.Any() && effectText.Contains("Fuel"))
        {
            match = Regex.Match(effectText, @":\s*\+(\d+)\s+Fuel", RegexOptions.IgnoreCase);
            if (match.Success)
            {
                int amount = int.Parse(match.Groups[1].Value);
                resources.Add(new Resource(ResourceType.Fuel, amount));
            }
        }
        
        return resources;
    }
    
    private int ParsePowerRequirement(string effectText)
    {
        // Default power requirement for most modules
        int defaultPower = 1;
        
        // Look for explicit power requirement
        var match = Regex.Match(effectText, @"(\d+)\s*Power[,:]", RegexOptions.IgnoreCase);
        if (match.Success)
        {
            return int.Parse(match.Groups[1].Value);
        }
        
        // Special cases
        if (effectText.ToLower().Contains("5 power"))
            return 5;
        if (effectText.ToLower().Contains("2 power"))
            return 2;
        
        return defaultPower;
    }
    
    private ResourceType ParseResourceType(string resourceName)
    {
        // This method would use the same logic as in ExcelCardParser
        switch (resourceName.ToLower().Replace(" ", ""))
        {
            case "ore": return ResourceType.Ore;
            case "ice": return ResourceType.Ice;
            case "carbon": return ResourceType.Carbon;
            case "silicon": return ResourceType.Silicon;
            case "rareearth":
            case "rareearths": return ResourceType.RareEarths;
            case "alloy":
            case "alloys": return ResourceType.Alloys;
            case "fuel": return ResourceType.Fuel;
            case "graphene": return ResourceType.Graphene;
            case "ceramic":
            case "ceramics": return ResourceType.Ceramics;
            case "microchip":
            case "microchips": return ResourceType.Microchips;
            case "superconductor":
            case "superconductors": return ResourceType.Superconductors;
            case "metallichydrogen": return ResourceType.MetallicHydrogen;
            case "antimatter": return ResourceType.Antimatter;
            case "helium3":
            case "helium-3": return ResourceType.Helium3;
            case "power": return ResourceType.Power;
            case "victorypoints":
            case "vp": return ResourceType.VP;
            case "$":
            case "dollars": return ResourceType.Dollars;
            case "science": return ResourceType.Science;
            default:
                Debug.LogWarning($"Unknown resource type: {resourceName}");
                return ResourceType.Ore;
        }
    }
    
    public ICardEffect GetEffect(string cardName)
    {
        if (cardEffects.TryGetValue(cardName, out var effect))
            return effect;
        
        Debug.LogWarning($"No effect found for card: {cardName}");
        return null;
    }
    
    public bool HasEffect(string cardName)
    {
        return cardEffects.ContainsKey(cardName);
    }
}