using UnityEngine;
using System.Collections.Generic;

public class WorldResourceDisplay : MonoBehaviour
{
    [SerializeField] public Transform resourceContainer;
    [SerializeField] private GameObject resourceColumnPrefab;
    [SerializeField] private GameObject resourceImagePrefab;
    [SerializeField] private float resourceSpacing = 30f;
    [SerializeField] private float columnSpacing = 40f;
    [SerializeField] private int maxResourcesPerColumn = 5;
    [SerializeField] public TMPro.TextMeshProUGUI resourcesHeaderText;

    private Dictionary<ResourceType, List<GameObject>> resourceImages = new Dictionary<ResourceType, List<GameObject>>();
    private Dictionary<ResourceType, List<GameObject>> resourceColumns = new Dictionary<ResourceType, List<GameObject>>();
    private ResourceManager resourceManager;
    
    private void Awake()
    {
        // Try to find ResourceManager
        FindResourceManager();
    }
    
    private void Start()
    {
        // Try again in Start in case the ResourceManager wasn't ready in Awake
        if (resourceManager == null)
        {
            FindResourceManager();
        }
    }
    
    private void FindResourceManager()
    {
        resourceManager = ResourceManager.Instance;
        
        // If still null, try to find it directly
        if (resourceManager == null)
        {
            resourceManager = FindFirstObjectByType<ResourceManager>();
        }
    }

    public void DisplayWorldResources(GameObject world, int playerId)
{
    // Make sure ResourceManager is available
    if (resourceManager == null)
    {
        FindResourceManager();
        
        if (resourceManager == null)
        {
            return;
        }
    }
    
    ClearDisplay();
    
    GameManager gameManager = GameManager.Instance;
    if (gameManager == null)
    {
        return;
    }
    
    if (world == null)
    {
        return;
    }
        
    if (playerId < 0 || playerId >= gameManager.Players.Count)
    {
        return;
    }
    
    Player player = gameManager.Players[playerId];
    if (player == null)
    {
        return;
    }
        
    Dictionary<ResourceType, int> resources = player.GetResourcesOnPlanet(world);
    
    // Skip if no resources
    if (resources.Count == 0)
    {
        if (resourcesHeaderText != null)
            resourcesHeaderText.gameObject.SetActive(false);
        return;
    }
        
    // Create columns and display resources
    int resourcesDisplayed = 0;
    foreach (var resource in resources)
    {
        if (resource.Value <= 0 || !IsPhysicalResource(resource.Key))
        {
            continue;
        }
            
        CreateResourceColumn(resource.Key, resource.Value);
        resourcesDisplayed++;
    }
    
    // Show the header text if we're displaying resources
    if (resourcesHeaderText != null)
        resourcesHeaderText.gameObject.SetActive(resourcesDisplayed > 0);
    
    // Reposition columns for proper spacing
    RepositionResourceColumns();
}

    public void ClearDisplay()
    {
        // Hide the header text
        if (resourcesHeaderText != null)
            resourcesHeaderText.gameObject.SetActive(false);

        // Clear existing columns and images
        foreach (var columnsList in resourceColumns.Values)
        {
            foreach (var column in columnsList)
            {
                if (column != null)
                {
                    Destroy(column);
                }
            }
        }

        resourceColumns.Clear();
        resourceImages.Clear();
    }


    private void CreateResourceColumn(ResourceType resourceType, int count)
    {

        // Check prefabs and references
        if (resourceContainer == null)
        {
            return;
        }

        if (resourceColumnPrefab == null)
        {
            return;
        }

        if (resourceImagePrefab == null)
        {
            return;
        }

        if (resourceManager == null)
        {
            return;
        }

        // Get resource sprite
        Sprite resourceSprite = resourceManager.GetResourceIcon(resourceType);

        // Calculate how many columns we need
        int columnsNeeded = Mathf.CeilToInt((float)count / maxResourcesPerColumn);

        List<GameObject> images = new List<GameObject>();

        // Create one or more columns based on the resource count
        for (int columnIndex = 0; columnIndex < columnsNeeded; columnIndex++)
        {
            // Create column
            GameObject column = Instantiate(resourceColumnPrefab, resourceContainer);
            column.name = $"Column_{resourceType}_{columnIndex}";

            // Calculate how many resources to put in this column
            int resourcesInThisColumn = Mathf.Min(maxResourcesPerColumn, count - (columnIndex * maxResourcesPerColumn));

            // Create resource images for this column
            for (int i = 0; i < resourcesInThisColumn; i++)
            {
                int resourceIndex = (columnIndex * maxResourcesPerColumn) + i;
                GameObject resourceImg = Instantiate(resourceImagePrefab, column.transform);
                resourceImg.name = $"Resource_{resourceType}_{resourceIndex}";

                // Set sprite
                UnityEngine.UI.Image image = resourceImg.GetComponent<UnityEngine.UI.Image>();
                if (image != null)
                {
                    image.sprite = resourceSprite;
                    image.preserveAspect = true;
                }

                // Position vertically within column
                RectTransform rect = resourceImg.GetComponent<RectTransform>();
                if (rect != null)
                {
                    rect.anchoredPosition = new Vector2(
                        0, // No horizontal offset within column
                        -i * resourceSpacing // Negative to go downward
                    );
                }

                images.Add(resourceImg);
            }

            // Store the column
            if (!resourceColumns.ContainsKey(resourceType))
            {
                resourceColumns[resourceType] = new List<GameObject>();
            }
            resourceColumns[resourceType].Add(column);
        }

        // Store the images for later reference
        resourceImages[resourceType] = images;
    }

    private void RepositionResourceColumns()
    {
        // Get all columns in order by resource type
        List<GameObject> allColumns = new List<GameObject>();
        List<ResourceType> resourceTypes = new List<ResourceType>(resourceColumns.Keys);
        resourceTypes.Sort((a, b) => a.ToString().CompareTo(b.ToString()));

        // Add columns in order by resource type
        foreach (ResourceType type in resourceTypes)
        {
            allColumns.AddRange(resourceColumns[type]);
        }

        // Position columns horizontally
        for (int i = 0; i < allColumns.Count; i++)
        {
            GameObject column = allColumns[i];
            RectTransform rect = column.GetComponent<RectTransform>();
            if (rect != null)
            {
                rect.anchoredPosition = new Vector2(i * columnSpacing, 0);
            }
        }
    }
    
    private bool IsPhysicalResource(ResourceType type)
    {
        return type != ResourceType.VP && 
               type != ResourceType.Science && 
               type != ResourceType.Dollars && 
               type != ResourceType.Power && 
               type != ResourceType.Strength && 
               type != ResourceType.Unknown;
    }
}