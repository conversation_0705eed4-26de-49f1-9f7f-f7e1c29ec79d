using UnityEngine;
using System.Collections.Generic;

public class StarBackground : MonoBeh<PERSON>our
{
    [Header("Star Layers")]
    [SerializeField] private int starsPerLayer = 500;
    [SerializeField] private float fieldWidth = 300f;
    [SerializeField] private float fieldHeight = 300f;
    
    [Header("Board Offset")]
    [SerializeField] private Vector3 boardCenter = new Vector3(100f, 0f, 0f);
    
    [Header("Layer Depths")]
    [SerializeField] private float layer1Depth = -50f;
    [SerializeField] private float layer2Depth = -100f;
    [SerializeField] private float layer3Depth = -150f;
    
    [Header("Parallax")]
    [SerializeField] private float parallaxFactor1 = 0.05f;
    [SerializeField] private float parallaxFactor2 = 0.1f;
    [SerializeField] private float parallaxFactor3 = 0.15f;
    
    [Header("Star Appearance")]
    [SerializeField] private float minStarSize = 0.5f;
    [SerializeField] private float maxStarSize = 2f;
    [SerializeField] private float minBrightness = 0.3f;
    [SerializeField] private float maxBrightness = 1f;
    [SerializeField] private Color starTint = new Color(0.9f, 0.95f, 1f);
    
    private Transform cameraTransform;
    private Vector3 lastCameraPosition;
    private List<Transform> layer1Stars = new List<Transform>();
    private List<Transform> layer2Stars = new List<Transform>();
    private List<Transform> layer3Stars = new List<Transform>();
    
    void Start()
    {
        // Find active camera
        Camera activeCamera = GetActiveCamera();
        if (activeCamera != null)
        {
            cameraTransform = activeCamera.transform;
            lastCameraPosition = cameraTransform.position;
        }
        
        CreateStarLayers();
    }
    
    void CreateStarLayers()
    {
        // Create parent objects for organization
        GameObject layer1Parent = new GameObject("Star Layer 1 (Close)");
        GameObject layer2Parent = new GameObject("Star Layer 2 (Mid)");
        GameObject layer3Parent = new GameObject("Star Layer 3 (Far)");
        
        layer1Parent.transform.parent = transform;
        layer2Parent.transform.parent = transform;
        layer3Parent.transform.parent = transform;
        
        // Create stars for each layer
        CreateStarsForLayer(layer1Parent.transform, layer1Stars, layer1Depth, 1f);
        CreateStarsForLayer(layer2Parent.transform, layer2Stars, layer2Depth, 0.7f);
        CreateStarsForLayer(layer3Parent.transform, layer3Stars, layer3Depth, 0.5f);
    }
    
    void CreateStarsForLayer(Transform parent, List<Transform> starList, float depth, float sizeMultiplier)
    {
        Material starMaterial = CreateStarMaterial();
        
        for (int i = 0; i < starsPerLayer; i++)
        {
            GameObject star = GameObject.CreatePrimitive(PrimitiveType.Quad);
            star.name = $"Star_{i}";
            star.transform.parent = parent;
            
            // Remove collider
            Destroy(star.GetComponent<Collider>());
            
            // Random position centered around board center
            float x = boardCenter.x + Random.Range(-fieldWidth/2, fieldWidth/2);
            float z = boardCenter.z + Random.Range(-fieldHeight/2, fieldHeight/2);
            star.transform.position = new Vector3(x, depth, z);
            
            // Random rotation to face camera
            star.transform.rotation = Quaternion.Euler(90, 0, 0);
            
            // Random size
            float size = Random.Range(minStarSize, maxStarSize) * sizeMultiplier;
            star.transform.localScale = Vector3.one * size;
            
            // Set material and brightness
            Renderer renderer = star.GetComponent<Renderer>();
            renderer.material = starMaterial;
            
            float brightness = Random.Range(minBrightness, maxBrightness);
            Color finalColor = starTint * brightness;
            finalColor.a = brightness;
            
            renderer.material.SetColor("_EmissionColor", finalColor);
            renderer.material.SetColor("_Color", finalColor);
            
            starList.Add(star.transform);
        }
    }
    
    Material CreateStarMaterial()
    {
        Material mat = new Material(Shader.Find("Universal Render Pipeline/Unlit"));
        mat.EnableKeyword("_EMISSION");
        mat.SetFloat("_Surface", 1); // Transparent
        mat.SetFloat("_Blend", 0); // Alpha blend
        mat.SetFloat("_DstBlend", 10); // OneMinusSrcAlpha
        mat.SetFloat("_SrcBlend", 5); // SrcAlpha
        mat.SetFloat("_ZWrite", 0);
        mat.SetFloat("_AlphaClip", 0);
        mat.renderQueue = 3000;
        
        // Create a simple star texture
        Texture2D starTexture = CreateStarTexture();
        mat.mainTexture = starTexture;
        mat.SetTexture("_BaseMap", starTexture);
        
        return mat;
    }
    
    Texture2D CreateStarTexture()
    {
        int size = 16; // Smaller size for more pixelated look
        Texture2D texture = new Texture2D(size, size, TextureFormat.RGBA32, false);
        Color[] pixels = new Color[size * size];

        // Create a square star pattern with retro/bit aesthetic
        for (int y = 0; y < size; y++)
        {
            for (int x = 0; x < size; x++)
            {
                float intensity = 0f;

                // Create a square star pattern
                int centerX = size / 2;
                int centerY = size / 2;
                int distFromCenterX = Mathf.Abs(x - centerX);
                int distFromCenterY = Mathf.Abs(y - centerY);

                // Core bright center (2x2 pixels)
                if (distFromCenterX <= 1 && distFromCenterY <= 1)
                {
                    intensity = 1f;
                }
                // Medium brightness ring (3x3 around center)
                else if (distFromCenterX <= 2 && distFromCenterY <= 2)
                {
                    intensity = 0.7f;
                }
                // Outer dim ring (4x4 around center)
                else if (distFromCenterX <= 3 && distFromCenterY <= 3)
                {
                    intensity = 0.3f;
                }

                pixels[y * size + x] = new Color(1, 1, 1, intensity);
            }
        }

        texture.SetPixels(pixels);
        texture.Apply();
        texture.filterMode = FilterMode.Point; // Point filtering for sharp pixels

        return texture;
    }

    void Update()
    {
        if (cameraTransform == null)
        {
            Camera activeCamera = GetActiveCamera();
            if (activeCamera != null)
            {
                cameraTransform = activeCamera.transform;
            }
            return;
        }

        // Update star alpha based on camera zoom
        UpdateStarVisibilityBasedOnZoom();
    }

    void UpdateStarVisibilityBasedOnZoom()
    {
        Camera activeCamera = GetActiveCamera();
        if (activeCamera == null) return;

        float zoomLevel = activeCamera.orthographicSize;

        // Calculate alpha based on zoom (fade out when zoomed in too much)
        float minZoom = 2f; // Fully transparent
        float maxZoom = 8f; // Fully opaque
        float alpha = Mathf.Clamp01((zoomLevel - minZoom) / (maxZoom - minZoom));

        // Apply to all layers with different fade rates
        UpdateLayerAlpha(layer1Stars, alpha * 0.8f);
        UpdateLayerAlpha(layer2Stars, alpha * 0.6f);
        UpdateLayerAlpha(layer3Stars, alpha * 0.4f);
    }

    void UpdateLayerAlpha(List<Transform> stars, float alpha)
    {
        foreach (Transform star in stars)
        {
            if (star != null)
            {
                Renderer renderer = star.GetComponent<Renderer>();
                if (renderer != null)
                {
                    Color color = renderer.material.color;
                    color.a = alpha;
                    renderer.material.color = color;

                    // Also update emission for glow effect
                    Color emission = renderer.material.GetColor("_EmissionColor");
                    emission.a = alpha;
                    renderer.material.SetColor("_EmissionColor", emission * alpha);
                }
            }
        }
    }

    void LateUpdate()
    {
        if (cameraTransform == null)
        {
            Camera activeCamera = GetActiveCamera();
            if (activeCamera != null)
            {
                cameraTransform = activeCamera.transform;
                lastCameraPosition = cameraTransform.position;
            }
            return;
        }
        
        // Calculate camera movement
        Vector3 cameraDelta = cameraTransform.position - lastCameraPosition;
        cameraDelta.y = 0; // Ignore vertical movement for parallax
        
        // Apply parallax to each layer
        ApplyParallaxToLayer(layer1Stars, cameraDelta, parallaxFactor1);
        ApplyParallaxToLayer(layer2Stars, cameraDelta, parallaxFactor2);
        ApplyParallaxToLayer(layer3Stars, cameraDelta, parallaxFactor3);
        
        lastCameraPosition = cameraTransform.position;
    }
    
    void ApplyParallaxToLayer(List<Transform> stars, Vector3 cameraDelta, float parallaxFactor)
    {
        foreach (Transform star in stars)
        {
            if (star != null)
            {
                star.position -= cameraDelta * parallaxFactor;
                
                // Wrap stars around if they go too far from board center
                Vector3 pos = star.position;
                float xDist = pos.x - boardCenter.x;
                float zDist = pos.z - boardCenter.z;
                
                if (Mathf.Abs(xDist) > fieldWidth) 
                    pos.x = boardCenter.x - xDist;
                if (Mathf.Abs(zDist) > fieldHeight) 
                    pos.z = boardCenter.z - zDist;
                    
                star.position = pos;
            }
        }
    }
    
    Camera GetActiveCamera()
    {
        // Try to get active camera from PlayerCameraController
        PlayerCameraController cameraController = FindFirstObjectByType<PlayerCameraController>();
        if (cameraController != null)
        {
            return cameraController.GetActiveCamera();
        }
        
        // Fallback to Camera.main
        return Camera.main;
    }
}