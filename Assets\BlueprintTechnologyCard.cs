using System.Collections.Generic;
using UnityEngine;

public enum BlueprintType
{
    Power,
    Extractor,
    Processor,
    Ship,
    Other
}

/// <summary>
/// Represents a blueprint technology card that allows players to build modules or ships
/// </summary>
public class BlueprintTechnologyCard : TechnologyCard
{
    
    [Header("Blueprint Information")]
    [SerializeField] private BlueprintType blueprintType;
    [SerializeField] private GameObject blueprintPrefab; // The prefab to instantiate when building
    [SerializeField] private List<Resource> buildCost = new List<Resource>(); // Resources required to build
    
    // For power modules
    [SerializeField] private int powerOutput = 0;
    
    // For ship blueprints
    [SerializeField] private int cargoCapacity = 0;
    [SerializeField] private float deltaVPerFuel = 0;
    [SerializeField] private int shipStrength = 0;
    [SerializeField] private bool isConsumedOnSurvey = false; // For survey probes
    
    // Properties
    public BlueprintType Type => blueprintType;
    public List<Resource> BuildCost => buildCost;
    public int PowerOutput => powerOutput;
    public int CargoCapacity => cargoCapacity;
    public float DeltaVPerFuel => deltaVPerFuel;
    public int ShipStrength => shipStrength;
    public bool IsConsumedOnSurvey => isConsumedOnSurvey;
    
    public override void Initialize()
    {
        base.Initialize();
        
        // Additional initialization based on blueprint type
        string additionalDescription = "";
        
        switch (blueprintType)
        {
            case BlueprintType.Power:
                additionalDescription = $"+{powerOutput} Power";
                break;
                
            case BlueprintType.Extractor:
                additionalDescription = "Activate: -1 Power, +1 each resource per deposit";
                break;
                
            case BlueprintType.Processor:
                // Processor-specific details would be in the card description
                break;
                
            case BlueprintType.Ship:
                additionalDescription = $"Cargo {cargoCapacity}, {deltaVPerFuel} delta-v per fuel";
                if (shipStrength > 0)
                    additionalDescription += $", Strength {shipStrength}";
                if (isConsumedOnSurvey)
                    additionalDescription += ", Ship is consumed at destination for survey";
                break;
        }
        
        // Append additional description to the card description
        if (!string.IsNullOrEmpty(additionalDescription) && effectText != null)
        {
            effectText.text = cardDescription + "\n" + additionalDescription;
        }
    }
    
    private void ConfigurePowerModule(GameObject module, Player player, GameObject location)
    {
        Module powerModule = new Module
        {
            Name = cardName,
            Type = ModuleType.Power,
            PowerOutput = powerOutput,
            PowerRequired = 0 // Power modules don't require power
        };
        
        player.AddModule(location, powerModule);
    }
    
    private void ConfigureMiningModule(GameObject module, Player player, GameObject location)
    {
        Module miningModule = new Module
        {
            Name = cardName,
            Type = ModuleType.Extractor,
            PowerOutput = 0,
            PowerRequired = 1 // Mining modules require 1 power to activate
        };
        
        player.AddModule(location, miningModule);
    }
    
    private void ConfigureProcessorModule(GameObject module, Player player, GameObject location)
    {
        Module processorModule = new Module
        {
            Name = cardName,
            Type = ModuleType.Processor,
            PowerOutput = 0,
            PowerRequired = 1 // Most processors require 1 power to activate
        };
        
        player.AddModule(location, processorModule);
    }
    
    private void ConfigureShip(GameObject shipObject, Player player, GameObject location)
    {
        Ship ship = new Ship
        {
            Name = cardName,
            CurrentLocation = location,
            DeltaVPerFuel = deltaVPerFuel,
            Strength = shipStrength,
            CargoCapacity = cargoCapacity
        };
        
        player.AddShip(ship);
        
        // Update ship count
        player.ShipsInPlay++;
    }
    
    // Override CreateDataCopy to include blueprint-specific data
    protected override TechnologyCardData CreateDataCopy()
    {
        BlueprintTechnologyCardData dataCopy = new BlueprintTechnologyCardData
        {
            // Copy base properties
            cardName = this.cardName,
            tier = this.tier,
            scienceValue = this.scienceValue,
            cardDescription = this.cardDescription,
            resourceCost = new List<Resource>(),
            
            // Copy blueprint-specific properties
            blueprintType = this.blueprintType,
            buildCost = new List<Resource>(),
            powerOutput = this.powerOutput,
            cargoCapacity = this.cargoCapacity,
            deltaVPerFuel = this.deltaVPerFuel,
            shipStrength = this.shipStrength,
            isConsumedOnSurvey = this.isConsumedOnSurvey
        };
        
        // Copy resource cost from base
        foreach (Resource resource in this.resourceCost)
        {
            dataCopy.resourceCost.Add(new Resource(resource.Type, resource.Amount));
        }
        
        // Copy build cost
        foreach (Resource resource in this.buildCost)
        {
            dataCopy.buildCost.Add(new Resource(resource.Type, resource.Amount));
        }
        
        return dataCopy;
    }
}