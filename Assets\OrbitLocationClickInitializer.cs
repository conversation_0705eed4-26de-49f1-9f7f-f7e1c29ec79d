using UnityEngine;

/// <summary>
/// Initializes click handlers for all orbital locations in the scene
/// </summary>
public class OrbitLocationClickInitializer : MonoBehaviour
{
    private void Start()
    {
        // Get the WorldManager
        WorldManager worldManager = WorldManager.Instance;
        if (worldManager == null)
        {
            Debug.LogError("WorldManager instance not found!");
            return;
        }

        // Get all orbital locations
        var orbitLocations = worldManager.GetAllOrbitLocations();
        
        // Add click handlers and colliders to all orbital locations
        foreach (var orbitLocation in orbitLocations)
        {
            if (orbitLocation != null)
            {
                // Add click handler if missing
                if (!orbitLocation.GetComponent<OrbitLocationClickHandler>())
                {
                    orbitLocation.AddComponent<OrbitLocationClickHandler>();
                    Debug.Log($"Added OrbitLocationClickHandler to {orbitLocation.name}");
                }

                // Add collider if missing (for click detection)
                if (orbitLocation.GetComponent<Collider>() == null)
                {
                    BoxCollider boxCollider = orbitLocation.AddComponent<BoxCollider>();
                    // Adjust collider size for orbital locations
                    boxCollider.size = new Vector3(1f, 0.1f, 1.4f);
                    Debug.Log($"Added BoxCollider to {orbitLocation.name} for click detection");
                }
            }
        }
    }
}
