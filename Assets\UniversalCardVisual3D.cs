using UnityEngine;

public class UniversalCardVisual3D : MonoBehaviour
{
    [SerializeField] private MeshRenderer cardRenderer;
    [SerializeField] private Material cardMaterial; // Base material to clone
    private CardData cardData;
    private Material instanceMaterial;
    
    public void SetupCard(CardData data)
    {
        cardData = data;
        LoadCardTexture();
        SetupInteractivity();
        SetupClickHandler();
    }
    
    private void SetupClickHandler()
    {
        // Add click handler component if not already present
        Card3DClickHandler clickHandler = GetComponent<Card3DClickHandler>();
        if (clickHandler == null)
        {
            clickHandler = gameObject.AddComponent<Card3DClickHandler>();
        }
        
        // Get the row index component
        CardRowIndex rowIndex = GetComponent<CardRowIndex>();
        if (rowIndex == null)
        {
            rowIndex = gameObject.AddComponent<CardRowIndex>();
        }
        
        // Initialize with card data and index from CardRowIndex
        clickHandler.Initialize(cardData, rowIndex.Index);
        
        // Make sure we have a collider for raycasting
        Collider collider = GetComponent<Collider>();
        if (collider == null)
        {
            BoxCollider boxCollider = gameObject.AddComponent<BoxCollider>();
            boxCollider.size = new Vector3(1f, 0.1f, 1.4f); // Adjust to match your card dimensions
        }
    }

    private void Awake()
    {
        // If cardRenderer is not assigned in the inspector, try to find it
        if (cardRenderer == null)
        {
            cardRenderer = GetComponent<MeshRenderer>();
            if (cardRenderer == null)
            {
                Debug.LogError("No MeshRenderer found on " + gameObject.name);
            }
        }
    }

    private void LoadCardTexture()
    {
        if (cardRenderer == null || cardMaterial == null)
        {
            Debug.LogError("Card Renderer or Material is not assigned!");
            return;
        }
        
        // Create instance of material
        instanceMaterial = new Material(cardMaterial);
        
        // Explicitly set the shader to URP/Unlit
        Shader unlitShader = Shader.Find("Universal Render Pipeline/Unlit");
        if (unlitShader != null)
        {
            instanceMaterial.shader = unlitShader;
        }
        else
        {
            Debug.LogError("Could not find URP/Unlit shader!");
        }
        
        string cardNameFormatted = cardData.Name.ToLower().Replace(" ", "");
        string imagePath = $"Cards/{cardData.Tier}{cardNameFormatted}";
        
        Texture2D cardTexture = Resources.Load<Texture2D>(imagePath);
        
        if (cardTexture != null)
        {
            
            // Apply texture
            instanceMaterial.mainTexture = cardTexture;
            instanceMaterial.SetTexture("_MainTex", cardTexture);
            instanceMaterial.SetTexture("_BaseMap", cardTexture); // For URP
            
            // Apply material to renderer
            cardRenderer.material = instanceMaterial;
        }
        else
        {
            Debug.LogError($"Card texture not found at: {imagePath}");
        }
    }
    
    public void SetupInteractivity()
    {

        // Add glow effect components
        CardActionChecker actionChecker = GetComponent<CardActionChecker>();
        if (actionChecker == null)
        {
            actionChecker = gameObject.AddComponent<CardActionChecker>();
        }

        CardGlowEffect glowEffect = GetComponent<CardGlowEffect>();
        if (glowEffect == null)
        {
            glowEffect = gameObject.AddComponent<CardGlowEffect>();
        }

        // Make sure the card has a collider for mouse detection
        Collider collider = GetComponent<Collider>();
        if (collider == null)
        {
            // Add a box collider that matches the card size
            BoxCollider boxCollider = gameObject.AddComponent<BoxCollider>();
            boxCollider.size = new Vector3(1f, 0.1f, 1.4f); // Adjust to match your card dimensions
            boxCollider.isTrigger = false; // Make sure it's not a trigger
        }

        // Initialize the action checker with card data
        if (cardData != null)
        {
            actionChecker.Initialize(cardData);
        }
    }

    private void OnDestroy()
    {
        // Clean up the instanced material
        if (instanceMaterial != null)
        {
            Destroy(instanceMaterial);
        }
    }
}