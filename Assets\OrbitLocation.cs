using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Represents an orbital location around a celestial body
/// </summary>
public class OrbitLocation : MonoBehaviour
{
    [Header("Orbit Information")]
    [SerializeField] private string orbitName;
    [SerializeField] private GameObject parentCelestialBody;
    [SerializeField] private float deltaVToSurface = 8.0f; // DeltaV to go from orbit to surface
    [SerializeField] private float deltaVFromSurface = 10.0f; // DeltaV to go from surface to orbit
    
    // Runtime state - similar to PlanetBody
    private List<Player> playersPresent = new List<Player>();
    private Player occupyingPlayer = null; // Player with military control
    private Dictionary<Player, int> playerShipStrength = new Dictionary<Player, int>();
    private bool hasBeenExplored = false;
    
    // Properties
    public string Name => orbitName;
    public GameObject ParentCelestialBody => parentCelestialBody;
    public Player OccupyingPlayer => occupyingPlayer;
    public bool HasBeenExplored => hasBeenExplored;
    
    private void Awake()
    {
        // Ensure the name is set
        if (string.IsNullOrEmpty(orbitName))
        {
            if (parentCelestialBody != null)
            {
                orbitName = $"Low {parentCelestialBody.name} Orbit";
            }
            else
            {
                // For special cases like Low Solar Orbit, use the GameObject name directly
                orbitName = gameObject.name;
            }
        }
    }
    
    /// <summary>
    /// Initialize this orbit location with a parent celestial body
    /// </summary>
    public void Initialize(GameObject celestialBody)
    {
        parentCelestialBody = celestialBody;

        if (string.IsNullOrEmpty(orbitName))
        {
            if (celestialBody != null)
            {
                orbitName = $"Low {celestialBody.name} Orbit";
            }
            else
            {
                // Special case for Low Solar Orbit - no parent celestial body
                orbitName = gameObject.name;
            }
        }

        // Get delta-V values from the parent planet if available
        if (celestialBody != null)
        {
            PlanetBody planetBody = celestialBody.GetComponent<PlanetBody>();
            if (planetBody != null)
            {
                deltaVToSurface = planetBody.GetDeltaVFromOrbit();
                deltaVFromSurface = planetBody.GetDeltaVToOrbit();
            }
        }
        // For Low Solar Orbit, keep default delta-V values since there's no surface to land on
    }
    
    /// <summary>
    /// Mark this orbit as explored by a player
    /// </summary>
    public void MarkAsExplored(Player player)
    {
        hasBeenExplored = true;
        
        if (!playersPresent.Contains(player))
        {
            playersPresent.Add(player);
        }
        
        Debug.Log($"Player {player.PlayerId} explored {orbitName}");
    }
    
    /// <summary>
    /// Register a ship arriving at this orbital location
    /// </summary>
    public void RegisterShipArrival(Ship ship, Player owner)
    {
        // Add the player to the list of present players if not already there
        if (!playersPresent.Contains(owner))
        {
            playersPresent.Add(owner);
        }
        
        // Update ship strength
        if (!playerShipStrength.ContainsKey(owner))
        {
            playerShipStrength[owner] = 0;
        }
        
        playerShipStrength[owner] += ship.Strength;
        
        // Update military occupation
        UpdateMilitaryOccupation();
        
        Debug.Log($"Ship {ship.Name} arrived at {orbitName}");
    }
    
    /// <summary>
    /// Register a ship departing from this orbital location
    /// </summary>
    public void RegisterShipDeparture(Ship ship, Player owner)
    {
        // Remove ship strength
        if (playerShipStrength.ContainsKey(owner))
        {
            playerShipStrength[owner] -= ship.Strength;
            
            // If player has no more ships or modules, remove from present players
            if (playerShipStrength[owner] <= 0)
            {
                playerShipStrength.Remove(owner);
                
                // Check if player has any modules here before removing
                bool hasModules = false;
                // This would check if player has modules in this orbit
                
                if (!hasModules)
                {
                    playersPresent.Remove(owner);
                }
            }
        }
        
        // Update military occupation
        UpdateMilitaryOccupation();
        
        Debug.Log($"Ship {ship.Name} departed from {orbitName}");
    }
    
    /// <summary>
    /// Update military occupation based on ship strength
    /// </summary>
    private void UpdateMilitaryOccupation()
    {
        Player strongestPlayer = null;
        int maxStrength = 0;
        
        foreach (var kvp in playerShipStrength)
        {
            if (kvp.Value > maxStrength)
            {
                maxStrength = kvp.Value;
                strongestPlayer = kvp.Key;
            }
        }
        
        occupyingPlayer = strongestPlayer;
    }
    
    /// <summary>
    /// Get the delta-V cost to move from this orbit to the surface
    /// </summary>
    public float GetDeltaVToSurface()
    {
        return deltaVToSurface;
    }
    
    /// <summary>
    /// Get the delta-V cost to move from the surface to this orbit
    /// </summary>
    public float GetDeltaVFromSurface()
    {
        return deltaVFromSurface;
    }
    
    /// <summary>
    /// Get all players present at this orbital location
    /// </summary>
    public List<Player> GetPlayersPresent()
    {
        return new List<Player>(playersPresent);
    }
    
    /// <summary>
    /// Check if this orbit location can be used for resource extraction
    /// Orbits typically don't have resources, but some might have special facilities
    /// </summary>
    public bool CanExtractResources()
    {
        // Most orbits don't have resources, but this could be overridden
        // for special cases like asteroid mining stations
        return false;
    }
}
