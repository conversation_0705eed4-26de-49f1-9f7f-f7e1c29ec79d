using System.Collections.Generic;
using System.Linq;
using UnityEngine; 

public class PowerGenerationEffect : ICardEffect
{
    private string moduleName;
    private int powerOutput;
    private List<Resource> buildCost;
    
    public PowerGenerationEffect(string moduleName, int powerOutput, List<Resource> buildCost)
    {
        this.moduleName = moduleName;
        this.powerOutput = powerOutput;
        this.buildCost = buildCost;
    }
    
    public bool CanExecute(Player player, GameObject location, Dictionary<string, object> parameters)
    {
        // Power modules are passive - they don't need to be executed
        return false;
    }
    
    public bool Execute(Player player, GameObject location, Dictionary<string, object> parameters)
    {
        // Power generation is passive
        return false;
    }
    
    public string GetDescription()
    {
        return $"Generates {powerOutput} power";
    }
}