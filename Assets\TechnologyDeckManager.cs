using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// Manages the technology deck and card row for the game
/// Using pre-made card images in a 3D scene
/// </summary>
public class TechnologyDeckManager : MonoBehaviour
{
    // Singleton pattern
    public static TechnologyDeckManager Instance { get; private set; }
    
    [Header("Card Setup")]
    [SerializeField] private GameObject universalCardPrefab;
    
    [Header("Card Row Settings")]
    [SerializeField] private int cardRowSize = 9;
    [SerializeField] private Transform cardRowParent;
    [SerializeField] private float cardSpacing = 1.5f;
    [SerializeField] private Vector3 cardRotation = new Vector3(45f, 0f, 0f);
    
    // Card parser
    private ExcelCardParser cardParser;
    
    // All cards organized by tier
    private List<CardData> startingTechs = new List<CardData>();
    private List<CardData> tier1Deck = new List<CardData>();
    private List<CardData> tier2Deck = new List<CardData>();
    private List<CardData> tier3Deck = new List<CardData>();
    
    // Current state
    private int currentTier = 1;
    private List<GameObject> cardRow = new List<GameObject>();
    private List<CardData> cardRowData = new List<CardData>();
    
    // Track taken cards
    private List<bool> cardTakenFlags = new List<bool>(); 
    
    // Incomplete wonder tracking
    private Dictionary<int, CardData> incompleteWonders = new Dictionary<int, CardData>();
    
    private GameUI gameUI;

    public delegate void InitializationCompleteHandler();
    public event InitializationCompleteHandler OnDeckInitializationComplete;

    // Add event for card row changes
    public delegate void CardRowChangedHandler();
    public event CardRowChangedHandler OnCardRowChanged;

    private void Awake()
    {
        // Singleton setup
        if (Instance != null && Instance != this)
        {
            Destroy(gameObject);
            return;
        }
        
        Instance = this;

        // Create parser
        cardParser = gameObject.AddComponent<ExcelCardParser>();
    }
    
    private void Start()
    {
        // Find UI after all objects are initialized
        gameUI = FindFirstObjectByType<GameUI>();
        
        // Initialize decks
        InitializeDecks();
        
        // Set up the card row
        SetupCardRow();
        
        // Update UI
        if (gameUI != null)
        {
            gameUI.RefreshUI();
        }
        
        // Trigger completion event
        OnDeckInitializationComplete?.Invoke();
    }

    /// <summary>
    /// Initialize all card decks from Excel file
    /// </summary>
    private void InitializeDecks()
    {
        // Parse all cards
        List<CardData> allCards = cardParser.ParseAllCards();
        
        // Sort cards into appropriate decks
        foreach (CardData card in allCards)
        {
            // Add multiple copies based on the count
            for (int i = 0; i < card.Count; i++)
            {
                switch (card.Tier)
                {
                    case 0:
                        startingTechs.Add(card);
                        break;
                    case 1:
                        tier1Deck.Add(card);
                        break;
                    case 2:
                        tier2Deck.Add(card);
                        break;
                    case 3:
                        tier3Deck.Add(card);
                        break;
                    default:
                        if (card.Type.ToLower().Contains("special"))
                        {
                            //specialCards.Add(card);
                        }
                        break;
                }
            }
        }
        
        // Shuffle the decks
        ShuffleDeck(tier1Deck);
        ShuffleDeck(tier2Deck);
        ShuffleDeck(tier3Deck);
    }
    
    /// <summary>
    /// Shuffle a deck of cards
    /// </summary>
    private void ShuffleDeck(List<CardData> deck)
    {
        for (int i = 0; i < deck.Count; i++)
        {
            int randomIndex = Random.Range(i, deck.Count);
            CardData temp = deck[i];
            deck[i] = deck[randomIndex];
            deck[randomIndex] = temp;
        }
    }
    
    /// <summary>
    /// Set up the initial card row
    /// </summary>
    private void SetupCardRow()
    {
        if (cardRowParent == null)
        {
            Debug.LogError("Card row parent transform is not assigned!");
            return;
        }
        
        // Clear existing cards
        foreach (GameObject card in cardRow)
        {
            if (card != null)
                Destroy(card);
        }
        cardRow.Clear();
        cardRowData.Clear();
        cardTakenFlags.Clear();
        
        // Fill the card row
        for (int i = 0; i < cardRowSize; i++)
        {
            DrawCardToRow();
        }
        
        // Position the cards
        RepositionCardRow();
    }
    
    /// <summary>
    /// Draw a card to the card row
    /// </summary>
    public void DrawCardToRow()
    {
        if (cardRow.Count >= cardRowSize)
            return;
            
        List<CardData> currentDeck = GetCurrentDeck();
        
        if (currentDeck.Count == 0)
        {
            // Move to next tier if current deck is empty
            if (currentTier < 3)
            {
                currentTier++;
                Debug.Log($"Moving to Tier {currentTier} technology deck");
                currentDeck = GetCurrentDeck();
            }
            else
            {
                Debug.Log("No more technology cards to draw. Game is in final rounds.");
                return;
            }
        }
        
        if (currentDeck.Count > 0)
        {
            // Draw the top card
            CardData cardData = currentDeck[0];
            currentDeck.RemoveAt(0);
            
            // Create the card object
            Vector3 position = Vector3.zero; // Will be repositioned
            
            // Always add at beginning (right side)
            // Provide the index explicitly (0) to track it in the card component
            GameObject cardObject = CreateCard3D(cardData, position, 0);
            
            // Add to row at the beginning (right side)
            cardRow.Insert(0, cardObject);
            cardRowData.Insert(0, cardData);
            cardTakenFlags.Insert(0, false); // Card is not taken initially
        }

        OnCardRowChanged?.Invoke();
    }
    
    /// <summary>
    /// Create a 3D card object from card data
    /// </summary>
    private GameObject CreateCard3D(CardData cardData, Vector3 position, int index)
    {
        if (universalCardPrefab == null)
        {
            Debug.LogError("Universal card prefab not assigned!");
            return null;
        }
        
        GameObject cardObject = Instantiate(universalCardPrefab, position, 
            Quaternion.Euler(cardRotation), cardRowParent);
        
        // Add a CardRowIndex component to store the index
        CardRowIndex rowIndexComponent = cardObject.AddComponent<CardRowIndex>();
        rowIndexComponent.Index = index;
        
        // Setup visual
        UniversalCardVisual3D visual = cardObject.GetComponent<UniversalCardVisual3D>();
        if (visual != null)
        {
            visual.SetupCard(cardData);
        }
        
        // Setup interaction - use the actual position in the row
        Card3DClickHandler clickHandler = cardObject.GetComponent<Card3DClickHandler>();
        if (clickHandler != null)
        {
            // Find the actual index of this card in the row
            int actualIndex = index; // Use the provided index directly
            clickHandler.Initialize(cardData, actualIndex);
        }
        
        // Add action checker for glow effect
        CardActionChecker actionChecker = cardObject.GetComponent<CardActionChecker>();
        if (actionChecker == null)
        {
            actionChecker = cardObject.AddComponent<CardActionChecker>();
        }
        actionChecker.Initialize(cardData);
        
        cardObject.name = $"Card_{cardData.Name.Replace(" ", "")}_{index}";
        
        return cardObject;
    }
    
    /// <summary>
    /// Get the current deck based on tier
    /// </summary>
    private List<CardData> GetCurrentDeck()
    {
        switch (currentTier)
        {
            case 1: return tier1Deck;
            case 2: return tier2Deck;
            case 3: return tier3Deck;
            default: return tier1Deck;
        }
    }
    
    /// <summary>
    /// Reposition all cards in the card row
    /// </summary>
    private void RepositionCardRow()
    {
        if (cardRowParent == null || cardRow.Count == 0)
            return;
        
        Vector3 startPos = cardRowParent.position;
        float rowWidth = (cardRow.Count - 1) * cardSpacing;
        float startX = startPos.x - (rowWidth / 2f);
        
        for (int i = 0; i < cardRow.Count; i++)
        {
            if (cardRow[i] != null)
            {
                Vector3 newPosition = new Vector3(
                    startX + (i * cardSpacing), 
                    startPos.y, 
                    startPos.z
                );
                
                cardRow[i].transform.position = newPosition;
                cardRow[i].transform.rotation = Quaternion.Euler(cardRotation);
                
                // Update the card index in the CardRowIndex component
                CardRowIndex rowIndex = cardRow[i].GetComponent<CardRowIndex>();
                if (rowIndex != null)
                {
                    rowIndex.Index = i;
                    
                    // Also update the Card3DClickHandler
                    Card3DClickHandler clickHandler = cardRow[i].GetComponent<Card3DClickHandler>();
                    if (clickHandler != null)
                    {
                        // Update with current card data and index
                        clickHandler.Initialize(cardRowData[i], i);
                    }
                }
            }
        }
    }
    
    /// <summary>
    /// Advance the card row (remove leftmost cards and slide everything down)
    /// </summary>
    public void AdvanceCardRow()
    {
        // Remove the leftmost two cards
        for (int i = 0; i < 2; i++)
        {
            if (cardRow.Count > 0)
            {
                int lastIndex = cardRow.Count - 1;
                GameObject card = cardRow[lastIndex];
                cardRow.RemoveAt(lastIndex);
                cardRowData.RemoveAt(lastIndex);
                cardTakenFlags.RemoveAt(lastIndex);
                
                if (card != null)
                {
                    Destroy(card);
                }
            }
        }
        
        // Draw new cards to fill the row (they'll be added on the right)
        while (cardRow.Count < cardRowSize)
        {
            DrawCardToRow();
        }
        
        // Reposition all cards
        RepositionCardRow();
        OnCardRowChanged?.Invoke();
    }

    /// <summary>
    /// Get the action cost for a card based on its position
    /// </summary>
    public int GetCardCost(int index)
    {
        // Now the costs are from left to right:
        // Leftmost 3 slots cost 3 actions
        // Middle 3 slots cost 2 actions
        // Rightmost 3 slots cost 1 action
        
        if (index < 4)
            return 3;
        else if (index < 8)
            return 2;
        else
            return 1;
    }

    /// <summary>
    /// Mark a card as taken but keep it in the row with invisible state
    /// </summary>
    public void MarkCardAsTaken(int index, CardData cardData)
    {
        if (index < 0 || index >= cardRow.Count)
        {
            Debug.LogWarning($"Invalid card index: {index}");
            return;
        }
        
        // If card is already taken, don't do anything
        if (cardTakenFlags[index])
        {
            Debug.LogWarning($"Card at index {index} is already taken!");
            return;
        }
        
        // Handle special card types as needed
        if (cardData.IsWonder)
        {
            GameManager gameManager = GameManager.Instance;
            if (gameManager != null)
            {
                int playerId = gameManager.CurrentPlayerIndex;
                if (incompleteWonders.ContainsKey(playerId))
                {
                    Debug.LogWarning($"Player {playerId} already has an incomplete wonder!");
                    return;
                }
                
                // Mark this wonder as incomplete for this player
                incompleteWonders[playerId] = cardData;
            }
        }
        
        // Handle initiative cards immediately
        if (cardData.IsInitiative)
        {
            ResolveInitiativeCard(cardData);
        }
        
        // Mark the card as taken (but keep it in place)
        cardTakenFlags[index] = true;
        
        // Make the card invisible by disabling renderers
        GameObject cardObject = cardRow[index];
        if (cardObject != null)
        {
            // Disable renderers
            Renderer[] renderers = cardObject.GetComponentsInChildren<Renderer>();
            foreach (Renderer renderer in renderers)
            {
                renderer.enabled = false;
            }
            
            // Disable colliders to prevent further interaction
            Collider[] colliders = cardObject.GetComponentsInChildren<Collider>();
            foreach (Collider collider in colliders)
            {
                collider.enabled = false;
            }
            
            // Add a component to mark this card as taken instead of using a tag
            if (!cardObject.GetComponent<CardTakenMarker>())
            {
                cardObject.AddComponent<CardTakenMarker>();
            }
        }
        
        //Debug.Log($"Marked card '{cardData.Name}' at index {index} as taken");
        
        // Notify listeners
        OnCardRowChanged?.Invoke();
    }

    /// <summary>
    /// Get a card object at the specified index
    /// </summary>
    public GameObject GetCardObject(int index)
    {
        if (index >= 0 && index < cardRow.Count)
            return cardRow[index];
        return null;
    }

    /// <summary>
    /// Check if a card at the specified index is already taken
    /// </summary>
    public bool IsCardTaken(int index)
    {
        if (index >= 0 && index < cardTakenFlags.Count)
            return cardTakenFlags[index];
        return false;
    }

    /// <summary>
    /// Take a card from the row without automatically drawing a new one or repositioning
    /// Instead, make the card invisible but keep it in place
    /// </summary>
    public CardData TakeCardFromRowNoRedraw(int index)
    {
        if (index < 0 || index >= cardRow.Count)
        {
            Debug.LogWarning($"Invalid card index: {index}");
            return null;
        }
                
        // Keep track of card data being taken
        CardData cardData = cardRowData[index];
        
        // If card is already taken, don't allow taking it again
        if (cardTakenFlags[index])
        {
            Debug.LogWarning($"Card at index {index} is already taken!");
            return null;
        }
        
        // Handle special card types as needed
        if (cardData.IsWonder)
        {
            GameManager gameManager = GameManager.Instance;
            if (gameManager != null)
            {
                int playerId = gameManager.CurrentPlayerIndex;
                if (incompleteWonders.ContainsKey(playerId))
                {
                    Debug.LogWarning($"Player {playerId} already has an incomplete wonder!");
                    return null;
                }
                
                // Mark this wonder as incomplete for this player
                incompleteWonders[playerId] = cardData;
            }
        }
        
        // Handle initiative cards immediately
        if (cardData.IsInitiative)
        {
            ResolveInitiativeCard(cardData);
        }
        
        // Get the card object
        GameObject cardObject = cardRow[index];
        
        // Make the card invisible by disabling renderers
        if (cardObject != null)
        {
            // Disable renderers
            Renderer[] renderers = cardObject.GetComponentsInChildren<Renderer>();
            foreach (Renderer renderer in renderers)
            {
                renderer.enabled = false;
            }
            
            // Disable colliders to prevent further interaction
            Collider[] colliders = cardObject.GetComponentsInChildren<Collider>();
            foreach (Collider collider in colliders)
            {
                collider.enabled = false;
            }
            
            // Add a component to mark this card as taken
            if (!cardObject.GetComponent<CardTakenMarker>())
            {
                cardObject.AddComponent<CardTakenMarker>();
            }
        }
        
        // Mark the card as taken in our tracking list
        cardTakenFlags[index] = true;
        
        // NOTE: We are NOT removing the card from cardRow or cardRowData anymore
        // Just marking it as taken and leaving it in place
        
        // Notify listeners
        OnCardRowChanged?.Invoke();
        return cardData;
    }

    /// <summary>
    /// Restore a previously taken card (for use in testing or special gameplay situations)
    /// </summary>
    public void RestoreCard(int index)
    {
        if (index >= 0 && index < cardTakenFlags.Count && cardTakenFlags[index])
        {
            cardTakenFlags[index] = false;
            
            // Make the card visible again
            GameObject cardObject = cardRow[index];
            if (cardObject != null)
            {
                // Enable renderers
                Renderer[] renderers = cardObject.GetComponentsInChildren<Renderer>();
                foreach (Renderer renderer in renderers)
                {
                    renderer.enabled = true;
                }
                
                // Enable colliders
                Collider[] colliders = cardObject.GetComponentsInChildren<Collider>();
                foreach (Collider collider in colliders)
                {
                    collider.enabled = true;
                }
                
                // Remove the taken marker component
                CardTakenMarker marker = cardObject.GetComponent<CardTakenMarker>();
                if (marker != null)
                {
                    Destroy(marker);
                }
            }
            OnCardRowChanged?.Invoke();
        }
    }

    /// <summary>
    /// Resolve an initiative card's immediate effect
    /// </summary>
    private void ResolveInitiativeCard(CardData card)
    {
        // TODO: Implement initiative card effects
        // This will be handled by the CardEffectResolver
        Debug.Log($"Resolving initiative card: {card.Name}");
    }
    
    /// <summary>
    /// Mark a wonder as complete
    /// </summary>
    public void CompleteWonder(int playerId, CardData wonder)
    {
        if (incompleteWonders.ContainsKey(playerId) && incompleteWonders[playerId] == wonder)
        {
            incompleteWonders.Remove(playerId);
            Debug.Log($"Player {playerId} completed wonder: {wonder.Name}");
        }
    }
    
    /// <summary>
    /// Check if a player has an incomplete wonder
    /// </summary>
    public bool PlayerHasIncompleteWonder(int playerId)
    {
        return incompleteWonders.ContainsKey(playerId);
    }
    
    /// <summary>
    /// Get current tier
    /// </summary>
    public int GetCurrentTier()
    {
        return currentTier;
    }
    
    /// <summary>
    /// Get remaining cards in the current deck
    /// </summary>
    public int GetRemainingCardsInCurrentDeck()
    {
        return GetCurrentDeck().Count;
    }

    /// <summary>
    /// Get the size of the card row
    /// </summary>
    public int GetCardRowSize()
    {
        return cardRowSize;
    }
    
    /// <summary>
    /// Get card data at a specific index in the row
    /// </summary>
    public CardData GetCardAtIndex(int index)
    {
        if (index >= 0 && index < cardRowData.Count)
        {
            return cardRowData[index];
        }
        return null;
    }

    /// <summary>
    /// Get technology cards for the effects manager
    /// </summary>
    public List<TechnologyCard> GetTechCardsForEffectsManager()
    {
        //TODO Replace this with the actual logic to retrieve the technology cards
        return new List<TechnologyCard>();
    }
}