using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;

/// <summary>
/// Orchestrates action buttons for card detail display
/// </summary>
public class CardActionButtonManager
{
    public CardActionButtonUI buttonUI;
    private CardActionButtonLogic buttonLogic;
    public CardActionIndicatorManager indicatorManager;
    public CardActionProcessorHandler processorHandler;
    private CardActionMarketHandler marketHandler;
    
    // Current card state
    private CardData currentCardData;
    private int currentCardIndex;
    private CardDetailDisplay.CardSource currentCardSource;
    private GameObject currentSourceObject;

    public CardActionButtonManager(
        CardDetailDisplay cardDetailDisplay,
        Button primaryActionButton,
        Button secondaryActionButton,
        Button tertiaryActionButton,
        Button button4, Button button5, Button button6, Button button7, Button button8,
        TextMeshProUGUI primaryActionText, TextMeshProUGUI secondaryActionText,
        TextMeshProUGUI tertiaryActionText, TextMeshProUGUI button4Text,
        TextMeshProUGUI button5Text, TextMeshProUG<PERSON> button6Text,
        TextMeshProUG<PERSON> button7Text, TextMeshProUG<PERSON> button8Text,
        Transform actionButtonContainer,
        GameObject actionIndicatorPrefab,
        Transform actionIndicatorContainer,
        Transform secondaryActionIndicatorContainer,
        Transform tertiaryActionIndicatorContainer,
        Transform button4ActionIndicatorContainer,
        Transform button5ActionIndicatorContainer,
        Transform button6ActionIndicatorContainer,
        Transform button7ActionIndicatorContainer,
        Transform button8ActionIndicatorContainer,
        EarthMarketUI earthMarketUI)
    {
        // Initialize UI manager
        buttonUI = new CardActionButtonUI(
            primaryActionButton, secondaryActionButton, tertiaryActionButton,
            button4, button5, button6, button7, button8,
            primaryActionText, secondaryActionText, tertiaryActionText,
            button4Text, button5Text, button6Text, button7Text, button8Text,
            actionButtonContainer);

        // Initialize indicator manager
        indicatorManager = new CardActionIndicatorManager(
            actionIndicatorPrefab, actionIndicatorContainer,
            secondaryActionIndicatorContainer, tertiaryActionIndicatorContainer,
            button4ActionIndicatorContainer, button5ActionIndicatorContainer,
            button6ActionIndicatorContainer, button7ActionIndicatorContainer,
            button8ActionIndicatorContainer);

        // Initialize handlers
        processorHandler = new CardActionProcessorHandler(buttonUI, indicatorManager);
        marketHandler = new CardActionMarketHandler(cardDetailDisplay, earthMarketUI);
        
        // Initialize logic
        buttonLogic = new CardActionButtonLogic(
            cardDetailDisplay, buttonUI, indicatorManager, 
            processorHandler, marketHandler);

        buttonUI.HideAllButtons();
    }

    public void SetCardData(CardData cardData, int cardIndex, CardDetailDisplay.CardSource cardSource, GameObject sourceObject)
    {
        currentCardData = cardData;
        currentCardIndex = cardIndex;
        currentCardSource = cardSource;
        currentSourceObject = sourceObject;
        
        buttonLogic.SetCardData(cardData, cardIndex, cardSource, sourceObject);
    }

    public void ConfigureButtons()
    {
        buttonUI.HideAllButtons();
        buttonLogic.ConfigureButtons(currentCardSource);
    }

    public void ResetDetailPanelUI()
    {
        processorHandler.ClearProcessorUI();
        buttonUI.ResetDetailPanelUI();
        buttonUI.CleanupProcessorIcons();
        buttonUI.HideAllButtons();
        indicatorManager.ClearAllIndicators();
        marketHandler.ResetDetailPanelUI();
    }

}