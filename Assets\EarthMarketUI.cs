using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class EarthMarketUI : MonoBehaviour
{
    [Header("Ore")]
    [SerializeField] private Button oreBuyButton;
    [SerializeField] private Button oreSellButton;
    [SerializeField] private TextMeshProUGUI orePriceText;

    [Header("Ice")]
    [SerializeField] private Button iceBuyButton;
    [SerializeField] private Button iceSellButton;
    [SerializeField] private TextMeshProUGUI icePriceText;

    [Header("Carbon")]
    [SerializeField] private Button carbonBuyButton;
    [SerializeField] private Button carbonSellButton;
    [SerializeField] private TextMeshProUGUI carbonPriceText;

    [Header("Silicon")]
    [SerializeField] private Button siliconBuyButton;
    [SerializeField] private Button siliconSellButton;
    [SerializeField] private TextMeshProUGUI siliconPriceText;

    [Header("Rare Earths")]
    [SerializeField] private Button rareEarthsBuyButton;
    [SerializeField] private Button rareEarthsSellButton;
    [SerializeField] private TextMeshProUGUI rareEarthsPriceText;

    [Header("Alloys")]
    [SerializeField] private Button alloysBuyButton;
    [SerializeField] private Button alloysSellButton;
    [SerializeField] private TextMeshProUGUI alloysPriceText;

    [Header("Fuel")]
    [SerializeField] private Button fuelBuyButton;
    [SerializeField] private Button fuelSellButton;
    [SerializeField] private TextMeshProUGUI fuelPriceText;

    [Header("Ceramics")]
    [SerializeField] private Button ceramicsBuyButton;
    [SerializeField] private Button ceramicsSellButton;
    [SerializeField] private TextMeshProUGUI ceramicsPriceText;

    [Header("Graphene")]
    [SerializeField] private Button grapheneBuyButton;
    [SerializeField] private Button grapheneSellButton;
    [SerializeField] private TextMeshProUGUI graphenePriceText;

    [Header("Microchips")]
    [SerializeField] private Button microchipsBuyButton;
    [SerializeField] private Button microchipsSellButton;
    [SerializeField] private TextMeshProUGUI microchipsPriceText;

    [Header("Helium3")]
    [SerializeField] private Button helium3BuyButton;
    [SerializeField] private Button helium3SellButton;
    [SerializeField] private TextMeshProUGUI helium3PriceText;

    [Header("Superconductors")]
    [SerializeField] private Button superconductorsBuyButton;
    [SerializeField] private Button superconductorsSellButton;
    [SerializeField] private TextMeshProUGUI superconductorsPriceText;

    [Header("Metallic Hydrogen")]
    [SerializeField] private Button metallicHydrogenBuyButton;
    [SerializeField] private Button metallicHydrogenSellButton;
    [SerializeField] private TextMeshProUGUI metallicHydrogenPriceText;

    [Header("Antimatter")]
    [SerializeField] private Button antimatterBuyButton;
    [SerializeField] private Button antimatterSellButton;
    [SerializeField] private TextMeshProUGUI antimatterPriceText;
    [Header("Selection State")]
    [SerializeField] private Button primaryActionButton;
    [SerializeField] private Transform actionIndicatorContainer;
    [SerializeField] private GameObject actionIndicatorPrefab;
    [Header("Selection Mode UI")]
    [SerializeField] private TextMeshProUGUI selectionQuantityText;
    [SerializeField] private TextMeshProUGUI selectionCostText;
    [SerializeField] private Image selectionResourceImage;

    private int selectionModeCount = 0;
    private int selectionModeTotalValue = 0;
    private int selectionModePrice = 0;
    private ResourceType selectedResourceType;
    private bool isBuyMode;
    private bool isInSelectionMode = false;
    private GameObject activeActionIndicator;
    private System.Collections.Generic.List<int> usedPrices = new System.Collections.Generic.List<int>();


    private void Start()
    {
        SetupButtonListeners();
        UpdatePriceTexts();
    }

    private void SetupButtonListeners()
    {
        if (oreBuyButton != null) oreBuyButton.onClick.AddListener(() => BuyResource(ResourceType.Ore));
        if (oreSellButton != null) oreSellButton.onClick.AddListener(() => SellResource(ResourceType.Ore));

        if (iceBuyButton != null) iceBuyButton.onClick.AddListener(() => BuyResource(ResourceType.Ice));
        if (iceSellButton != null) iceSellButton.onClick.AddListener(() => SellResource(ResourceType.Ice));

        if (carbonBuyButton != null) carbonBuyButton.onClick.AddListener(() => BuyResource(ResourceType.Carbon));
        if (carbonSellButton != null) carbonSellButton.onClick.AddListener(() => SellResource(ResourceType.Carbon));

        if (siliconBuyButton != null) siliconBuyButton.onClick.AddListener(() => BuyResource(ResourceType.Silicon));
        if (siliconSellButton != null) siliconSellButton.onClick.AddListener(() => SellResource(ResourceType.Silicon));

        if (rareEarthsBuyButton != null) rareEarthsBuyButton.onClick.AddListener(() => BuyResource(ResourceType.RareEarths));
        if (rareEarthsSellButton != null) rareEarthsSellButton.onClick.AddListener(() => SellResource(ResourceType.RareEarths));

        if (alloysBuyButton != null) alloysBuyButton.onClick.AddListener(() => BuyResource(ResourceType.Alloys));
        if (alloysSellButton != null) alloysSellButton.onClick.AddListener(() => SellResource(ResourceType.Alloys));

        if (fuelBuyButton != null) fuelBuyButton.onClick.AddListener(() => BuyResource(ResourceType.Fuel));
        if (fuelSellButton != null) fuelSellButton.onClick.AddListener(() => SellResource(ResourceType.Fuel));

        if (ceramicsBuyButton != null) ceramicsBuyButton.onClick.AddListener(() => BuyResource(ResourceType.Ceramics));
        if (ceramicsSellButton != null) ceramicsSellButton.onClick.AddListener(() => SellResource(ResourceType.Ceramics));

        if (grapheneBuyButton != null) grapheneBuyButton.onClick.AddListener(() => BuyResource(ResourceType.Graphene));
        if (grapheneSellButton != null) grapheneSellButton.onClick.AddListener(() => SellResource(ResourceType.Graphene));

        if (microchipsBuyButton != null) microchipsBuyButton.onClick.AddListener(() => BuyResource(ResourceType.Microchips));
        if (microchipsSellButton != null) microchipsSellButton.onClick.AddListener(() => SellResource(ResourceType.Microchips));

        if (helium3BuyButton != null) helium3BuyButton.onClick.AddListener(() => BuyResource(ResourceType.Helium3));
        if (helium3SellButton != null) helium3SellButton.onClick.AddListener(() => SellResource(ResourceType.Helium3));

        if (superconductorsBuyButton != null) superconductorsBuyButton.onClick.AddListener(() => BuyResource(ResourceType.Superconductors));
        if (superconductorsSellButton != null) superconductorsSellButton.onClick.AddListener(() => SellResource(ResourceType.Superconductors));

        if (metallicHydrogenBuyButton != null) metallicHydrogenBuyButton.onClick.AddListener(() => BuyResource(ResourceType.MetallicHydrogen));
        if (metallicHydrogenSellButton != null) metallicHydrogenSellButton.onClick.AddListener(() => SellResource(ResourceType.MetallicHydrogen));

        if (antimatterBuyButton != null) antimatterBuyButton.onClick.AddListener(() => BuyResource(ResourceType.Antimatter));
        if (antimatterSellButton != null) antimatterSellButton.onClick.AddListener(() => SellResource(ResourceType.Antimatter));
    }

    private void UpdatePriceTexts()
    {
        ResourceManager resourceManager = ResourceManager.Instance;
        if (resourceManager == null) return;

        if (orePriceText != null) orePriceText.text = $"${resourceManager.GetMarketBuyPrice(ResourceType.Ore)}";
        if (icePriceText != null) icePriceText.text = $"${resourceManager.GetMarketBuyPrice(ResourceType.Ice)}";
        if (carbonPriceText != null) carbonPriceText.text = $"${resourceManager.GetMarketBuyPrice(ResourceType.Carbon)}";
        if (siliconPriceText != null) siliconPriceText.text = $"${resourceManager.GetMarketBuyPrice(ResourceType.Silicon)}";
        if (rareEarthsPriceText != null) rareEarthsPriceText.text = $"${resourceManager.GetMarketBuyPrice(ResourceType.RareEarths)}";
        if (alloysPriceText != null) alloysPriceText.text = $"${resourceManager.GetMarketBuyPrice(ResourceType.Alloys)}";
        if (fuelPriceText != null) fuelPriceText.text = $"${resourceManager.GetMarketBuyPrice(ResourceType.Fuel)}";
        if (ceramicsPriceText != null) ceramicsPriceText.text = $"${resourceManager.GetMarketBuyPrice(ResourceType.Ceramics)}";
        if (graphenePriceText != null) graphenePriceText.text = $"${resourceManager.GetMarketBuyPrice(ResourceType.Graphene)}";
        if (microchipsPriceText != null) microchipsPriceText.text = $"${resourceManager.GetMarketBuyPrice(ResourceType.Microchips)}";
        if (helium3PriceText != null) helium3PriceText.text = $"${resourceManager.GetMarketBuyPrice(ResourceType.Helium3)}";
        if (superconductorsPriceText != null) superconductorsPriceText.text = $"${resourceManager.GetMarketBuyPrice(ResourceType.Superconductors)}";
        if (metallicHydrogenPriceText != null) metallicHydrogenPriceText.text = $"${resourceManager.GetMarketBuyPrice(ResourceType.MetallicHydrogen)}";
        if (antimatterPriceText != null) antimatterPriceText.text = $"${resourceManager.GetMarketBuyPrice(ResourceType.Antimatter)}";
    }

    private void UpdateSinglePriceText(ResourceType resourceType)
    {
        ResourceManager resourceManager = ResourceManager.Instance;
        if (resourceManager == null) return;

        TextMeshProUGUI priceText = GetPriceTextForResource(resourceType);
        if (priceText != null)
        {
            priceText.text = $"${resourceManager.GetMarketBuyPrice(resourceType)}";
        }
    }

    private void UpdateButtonStates()
    {
        GameManager gameManager = GameManager.Instance;
        WorldManager worldManager = WorldManager.Instance;

        if (gameManager == null || worldManager == null) return;

        Player currentPlayer = gameManager.CurrentPlayer;
        GameObject earth = worldManager.GetCelestialBodyByName("Earth");

        if (currentPlayer == null || earth == null) return;

        if (isInSelectionMode)
        {
            // In selection mode, only update the selected resource's buttons
            UpdateSellButtonState(selectedResourceType, GetSellButtonForResource(selectedResourceType), currentPlayer, earth);
            UpdateBuyButtonState(selectedResourceType, GetBuyButtonForResource(selectedResourceType), currentPlayer);
        }
        else
        {
            // Not in selection mode, update all buttons
            UpdateSellButtonState(ResourceType.Ore, oreSellButton, currentPlayer, earth);
            UpdateBuyButtonState(ResourceType.Ore, oreBuyButton, currentPlayer);

            UpdateSellButtonState(ResourceType.Ice, iceSellButton, currentPlayer, earth);
            UpdateBuyButtonState(ResourceType.Ice, iceBuyButton, currentPlayer);

            UpdateSellButtonState(ResourceType.Carbon, carbonSellButton, currentPlayer, earth);
            UpdateBuyButtonState(ResourceType.Carbon, carbonBuyButton, currentPlayer);

            UpdateSellButtonState(ResourceType.Silicon, siliconSellButton, currentPlayer, earth);
            UpdateBuyButtonState(ResourceType.Silicon, siliconBuyButton, currentPlayer);

            UpdateSellButtonState(ResourceType.RareEarths, rareEarthsSellButton, currentPlayer, earth);
            UpdateBuyButtonState(ResourceType.RareEarths, rareEarthsBuyButton, currentPlayer);

            UpdateSellButtonState(ResourceType.Alloys, alloysSellButton, currentPlayer, earth);
            UpdateBuyButtonState(ResourceType.Alloys, alloysBuyButton, currentPlayer);

            UpdateSellButtonState(ResourceType.Fuel, fuelSellButton, currentPlayer, earth);
            UpdateBuyButtonState(ResourceType.Fuel, fuelBuyButton, currentPlayer);

            UpdateSellButtonState(ResourceType.Ceramics, ceramicsSellButton, currentPlayer, earth);
            UpdateBuyButtonState(ResourceType.Ceramics, ceramicsBuyButton, currentPlayer);

            UpdateSellButtonState(ResourceType.Graphene, grapheneSellButton, currentPlayer, earth);
            UpdateBuyButtonState(ResourceType.Graphene, grapheneBuyButton, currentPlayer);

            UpdateSellButtonState(ResourceType.Microchips, microchipsSellButton, currentPlayer, earth);
            UpdateBuyButtonState(ResourceType.Microchips, microchipsBuyButton, currentPlayer);

            UpdateSellButtonState(ResourceType.Helium3, helium3SellButton, currentPlayer, earth);
            UpdateBuyButtonState(ResourceType.Helium3, helium3BuyButton, currentPlayer);

            UpdateSellButtonState(ResourceType.Superconductors, superconductorsSellButton, currentPlayer, earth);
            UpdateBuyButtonState(ResourceType.Superconductors, superconductorsBuyButton, currentPlayer);

            UpdateSellButtonState(ResourceType.MetallicHydrogen, metallicHydrogenSellButton, currentPlayer, earth);
            UpdateBuyButtonState(ResourceType.MetallicHydrogen, metallicHydrogenBuyButton, currentPlayer);

            UpdateSellButtonState(ResourceType.Antimatter, antimatterSellButton, currentPlayer, earth);
            UpdateBuyButtonState(ResourceType.Antimatter, antimatterBuyButton, currentPlayer);
        }
    }


    private void UpdateSellButtonState(ResourceType resourceType, Button sellButton, Player player, GameObject earth)
    {
        if (sellButton == null) return;

        int availableAmount = player.GetResourceAmount(earth, resourceType);

        // If we're in selection mode for this resource
        if (isInSelectionMode && selectedResourceType == resourceType)
        {
            if (isBuyMode)
            {
                // In buy mode, add the pending purchase count to available amount
                availableAmount += selectionModeCount;
            }
            else
            {
                // In sell mode, subtract the pending sale count
                availableAmount -= selectionModeCount;
            }
        }

        sellButton.gameObject.SetActive(availableAmount > 0);
    }

    private void BuyResource(ResourceType resourceType)
    {
        if (isInSelectionMode && selectedResourceType == resourceType)
        {
            if (!isBuyMode)
            {
                // Undo last sell - remove the last price that was used
                if (usedPrices.Count > 0)
                {
                    int lastUsedPrice = usedPrices[usedPrices.Count - 1];
                    usedPrices.RemoveAt(usedPrices.Count - 1);
                    selectionModeTotalValue -= lastUsedPrice;
                    selectionModeCount--;

                    // Restore the price for next operation
                    selectionModePrice = lastUsedPrice;
                }

                if (selectionModeCount <= 0)
                {
                    ExitSelectionMode();
                    return;
                }
            }
            else
            {
                // Add to existing buy selection
                usedPrices.Add(selectionModePrice);
                selectionModeTotalValue += selectionModePrice;
                selectionModeCount++;
                selectionModePrice++; // Price increases for next unit
            }
            UpdateSelectionModeUI();
            UpdateSelectedResourcePriceText();
        }
        else
        {
            // Start new buy selection
            EnterSelectionMode(resourceType, true);
        }
    }

    private void SellResource(ResourceType resourceType)
    {
        if (isInSelectionMode && selectedResourceType == resourceType)
        {
            if (isBuyMode)
            {
                // Undo last buy - remove the last price that was used
                if (usedPrices.Count > 0)
                {
                    int lastUsedPrice = usedPrices[usedPrices.Count - 1];
                    usedPrices.RemoveAt(usedPrices.Count - 1);
                    selectionModeTotalValue -= lastUsedPrice;
                    selectionModeCount--;
                    selectionModePrice = lastUsedPrice; // Restore price
                }

                if (selectionModeCount <= 0)
                {
                    ExitSelectionMode();
                    return;
                }
            }
            else
            {
                // Add to existing sell selection
                usedPrices.Add(selectionModePrice);
                selectionModeTotalValue += selectionModePrice;
                selectionModeCount++;
                selectionModePrice = Mathf.Max(1, selectionModePrice - 1); // Price decreases for next unit
            }
            UpdateSelectionModeUI();
            UpdateSelectedResourcePriceText();
        }
        else
        {
            // Start new sell selection
            EnterSelectionMode(resourceType, false);
        }
    }

    private int GetOriginalPrice(ResourceType resourceType)
    {
        ResourceManager resourceManager = ResourceManager.Instance;
        return resourceManager != null ? resourceManager.GetMarketBuyPrice(resourceType) : 1;
    }

    private void EnterSelectionMode(ResourceType resourceType, bool buyMode)
    {
        selectedResourceType = resourceType;
        isBuyMode = buyMode;
        isInSelectionMode = true;
        usedPrices.Clear(); // Clear the price history

        ResourceManager resourceManager = ResourceManager.Instance;
        if (resourceManager != null)
        {
            if (isBuyMode)
            {
                selectionModePrice = resourceManager.GetMarketBuyPrice(resourceType);
                selectionModeCount = 1;
                usedPrices.Add(selectionModePrice);
                selectionModeTotalValue = selectionModePrice;
                selectionModePrice++; // Set next price
            }
            else
            {
                selectionModePrice = resourceManager.GetMarketSellPrice(resourceType);
                selectionModeCount = 1;
                usedPrices.Add(selectionModePrice);
                selectionModeTotalValue = selectionModePrice;
                selectionModePrice = Mathf.Max(1, selectionModePrice - 1); // Set next price
            }
        }

        HideAllResourceButtons();
        ShowSelectedResourceButtons(resourceType);
        SetupPrimaryButton();
        UpdateSelectionModeUI();
        UpdateSelectedResourcePriceText();
    }

    private void UpdateSelectionModeUI()
    {
        if (selectionQuantityText != null && selectionCostText != null && selectionResourceImage != null)
        {
            selectionQuantityText.gameObject.SetActive(true);
            selectionCostText.gameObject.SetActive(true);
            selectionResourceImage.gameObject.SetActive(true);

            ResourceManager resourceManager = ResourceManager.Instance;
            if (resourceManager != null)
            {
                Sprite resourceSprite = resourceManager.GetResourceIcon(selectedResourceType);
                if (resourceSprite != null)
                {
                    selectionResourceImage.sprite = resourceSprite;
                }
            }

            string action = isBuyMode ? "Buy" : "Sell";
            selectionQuantityText.text = $"{action} {selectionModeCount}";
            selectionCostText.text = $"for ${selectionModeTotalValue}?";
        }

        // Update button states whenever selection changes
        UpdateButtonStates();
    }

    private void UpdateSelectedResourcePriceText()
    {
        TextMeshProUGUI priceText = GetPriceTextForResource(selectedResourceType);
        if (priceText != null)
        {
            priceText.text = $"${selectionModePrice}";
        }
    }

    private void HideAllResourceButtons()
    {
        SetButtonsActive(ResourceType.Ore, false);
        SetButtonsActive(ResourceType.Ice, false);
        SetButtonsActive(ResourceType.Carbon, false);
        SetButtonsActive(ResourceType.Silicon, false);
        SetButtonsActive(ResourceType.RareEarths, false);
        SetButtonsActive(ResourceType.Alloys, false);
        SetButtonsActive(ResourceType.Fuel, false);
        SetButtonsActive(ResourceType.Ceramics, false);
        SetButtonsActive(ResourceType.Graphene, false);
        SetButtonsActive(ResourceType.Microchips, false);
        SetButtonsActive(ResourceType.Helium3, false);
        SetButtonsActive(ResourceType.Superconductors, false);
        SetButtonsActive(ResourceType.MetallicHydrogen, false);
        SetButtonsActive(ResourceType.Antimatter, false);
    }

    private void ShowSelectedResourceButtons(ResourceType resourceType)
    {
        // Hide all buttons first
        HideAllResourceButtons();

        // Only show the selected resource's buttons
        Button buyButton = GetBuyButtonForResource(resourceType);
        Button sellButton = GetSellButtonForResource(resourceType);

        if (buyButton != null) buyButton.gameObject.SetActive(true);
        if (sellButton != null) sellButton.gameObject.SetActive(true);

        // Update button states to handle sell button visibility correctly
        UpdateButtonStates();
    }

    private void UpdateBuyButtonState(ResourceType resourceType, Button buyButton, Player player)
    {
        if (buyButton == null) return;

        ResourceManager resourceManager = ResourceManager.Instance;
        if (resourceManager == null) return;

        // If we're in selection mode for this resource and selling, always allow buy (it's an undo)
        if (isInSelectionMode && selectedResourceType == resourceType && !isBuyMode)
        {
            buyButton.interactable = true;
            return;
        }

        int nextPurchasePrice = resourceManager.GetMarketBuyPrice(resourceType);
        int totalCostSoFar = 0;

        // If we're in selection mode for this resource and buying
        if (isInSelectionMode && selectedResourceType == resourceType && isBuyMode)
        {
            nextPurchasePrice = selectionModePrice; // This is already set to the next price
            totalCostSoFar = selectionModeTotalValue; // Current transaction total
        }

        bool canAfford = player.Money >= (totalCostSoFar + nextPurchasePrice);
        buyButton.interactable = canAfford;
    }

    private void SetButtonsActive(ResourceType resourceType, bool active)
    {
        Button buyButton = GetBuyButtonForResource(resourceType);
        Button sellButton = GetSellButtonForResource(resourceType);

        if (buyButton != null) buyButton.gameObject.SetActive(active);
        if (sellButton != null) sellButton.gameObject.SetActive(active);
    }

    private Button GetBuyButtonForResource(ResourceType resourceType)
    {
        switch (resourceType)
        {
            case ResourceType.Ore: return oreBuyButton;
            case ResourceType.Ice: return iceBuyButton;
            case ResourceType.Carbon: return carbonBuyButton;
            case ResourceType.Silicon: return siliconBuyButton;
            case ResourceType.RareEarths: return rareEarthsBuyButton;
            case ResourceType.Alloys: return alloysBuyButton;
            case ResourceType.Fuel: return fuelBuyButton;
            case ResourceType.Ceramics: return ceramicsBuyButton;
            case ResourceType.Graphene: return grapheneBuyButton;
            case ResourceType.Microchips: return microchipsBuyButton;
            case ResourceType.Helium3: return helium3BuyButton;
            case ResourceType.Superconductors: return superconductorsBuyButton;
            case ResourceType.MetallicHydrogen: return metallicHydrogenBuyButton;
            case ResourceType.Antimatter: return antimatterBuyButton;
            default: return null;
        }
    }

    private Button GetSellButtonForResource(ResourceType resourceType)
    {
        switch (resourceType)
        {
            case ResourceType.Ore: return oreSellButton;
            case ResourceType.Ice: return iceSellButton;
            case ResourceType.Carbon: return carbonSellButton;
            case ResourceType.Silicon: return siliconSellButton;
            case ResourceType.RareEarths: return rareEarthsSellButton;
            case ResourceType.Alloys: return alloysSellButton;
            case ResourceType.Fuel: return fuelSellButton;
            case ResourceType.Ceramics: return ceramicsSellButton;
            case ResourceType.Graphene: return grapheneSellButton;
            case ResourceType.Microchips: return microchipsSellButton;
            case ResourceType.Helium3: return helium3SellButton;
            case ResourceType.Superconductors: return superconductorsSellButton;
            case ResourceType.MetallicHydrogen: return metallicHydrogenSellButton;
            case ResourceType.Antimatter: return antimatterSellButton;
            default: return null;
        }
    }

    private void SetupPrimaryButton()
    {
        if (primaryActionButton == null) return;

        primaryActionButton.gameObject.SetActive(true);

        TextMeshProUGUI buttonText = primaryActionButton.GetComponentInChildren<TextMeshProUGUI>();
        if (buttonText != null)
        {
            buttonText.text = isBuyMode ? "Buy" : "Sell";
        }

        primaryActionButton.onClick.RemoveAllListeners();
        primaryActionButton.onClick.AddListener(OnPrimaryButtonClicked);

        CreateActionIndicator();
    }

    private void CreateActionIndicator()
    {
        if (actionIndicatorContainer == null || actionIndicatorPrefab == null) return;

        if (activeActionIndicator != null)
        {
            Destroy(activeActionIndicator);
        }

        activeActionIndicator = Instantiate(actionIndicatorPrefab, actionIndicatorContainer);
        activeActionIndicator.SetActive(true);
        activeActionIndicator.transform.localScale = new Vector3(1.5f, 1.5f, 1.5f);
    }

    private void OnPrimaryButtonClicked()
    {
        GameManager gameManager = GameManager.Instance;
        ResourceManager resourceManager = ResourceManager.Instance;

        if (gameManager == null || resourceManager == null || gameManager.ActionsRemaining <= 0)
            return;

        Player currentPlayer = gameManager.CurrentPlayer;
        if (currentPlayer == null) return;

        // Store price changes for undo
        int[] priceChanges = new int[selectionModeCount];
        int currentPrice = isBuyMode ?
            resourceManager.GetMarketBuyPrice(selectedResourceType) :
            resourceManager.GetMarketSellPrice(selectedResourceType);

        for (int i = 0; i < selectionModeCount; i++)
        {
            priceChanges[i] = currentPrice;
            if (isBuyMode)
                currentPrice++;
            else
                currentPrice = Mathf.Max(1, currentPrice - 1);
        }

        // Create undoable action BEFORE making changes
        MarketTransactionAction transactionAction = new MarketTransactionAction(
            selectedResourceType,
            selectionModeCount,
            selectionModeTotalValue,
            isBuyMode,
            currentPlayer.PlayerId,
            priceChanges
        );

        // Use action directly to avoid double undo registration
        gameManager.SetActionsRemaining(gameManager.ActionsRemaining - 1);

        // Execute all transactions with suppressed logging
        for (int i = 0; i < selectionModeCount; i++)
        {
            if (isBuyMode)
            {
                resourceManager.BuyFromMarket(currentPlayer, selectedResourceType, 1, true);
            }
            else
            {
                resourceManager.SellToMarket(currentPlayer, selectedResourceType, 1, true);
            }
        }

        // Add single combined log message
        GameLogManager logManager = GameLogManager.Instance;
        if (logManager != null)
        {
            string action = isBuyMode ? "bought" : "sold";
            logManager.AddLog($"Player {currentPlayer.PlayerId + 1} {action} {selectionModeCount} {selectedResourceType} for ${selectionModeTotalValue}");
        }

        // Register the undoable action AFTER making changes
        UndoManager undoManager = UndoManager.Instance;
        if (undoManager != null)
        {
            undoManager.RegisterAction(transactionAction);
        }

        GameUI gameUI = FindFirstObjectByType<GameUI>();
        if (gameUI != null)
        {
            gameUI.RefreshUI();
        }

        RefreshUI();
        ExitSelectionMode();
    }

    private void RefreshUI()
    {
        PlayAreaManager playAreaManager = PlayAreaManager.Instance;
        GameManager gameManager = GameManager.Instance;

        if (playAreaManager != null && gameManager != null)
        {
            playAreaManager.RefreshPlayerPlayArea(gameManager.CurrentPlayer.PlayerId);
        }

        WorldResourceDisplay resourceDisplay = FindFirstObjectByType<WorldResourceDisplay>();
        if (resourceDisplay != null)
        {
            WorldManager worldManager = WorldManager.Instance;
            GameObject earth = worldManager.GetCelestialBodyByName("Earth");
            if (earth != null && gameManager != null)
            {
                resourceDisplay.DisplayWorldResources(earth, gameManager.CurrentPlayer.PlayerId);
            }
        }

        UpdateButtonStates();
    }

    public void ExitSelectionMode()
    {
        isInSelectionMode = false;
        selectionModeCount = 0;
        selectionModeTotalValue = 0;
        selectionModePrice = 0;
        usedPrices.Clear(); // Clear price history

        if (primaryActionButton != null)
        {
            primaryActionButton.gameObject.SetActive(false);
        }

        if (activeActionIndicator != null)
        {
            Destroy(activeActionIndicator);
            activeActionIndicator = null;
        }

        if (selectionQuantityText != null) selectionQuantityText.gameObject.SetActive(false);
        if (selectionCostText != null) selectionCostText.gameObject.SetActive(false);
        if (selectionResourceImage != null) selectionResourceImage.gameObject.SetActive(false);

        ShowAllResourceButtons();
        UpdatePriceTexts(); // Reset all price texts to actual market values
    }

    private void ShowAllResourceButtons()
    {
        SetButtonsActive(ResourceType.Ore, true);
        SetButtonsActive(ResourceType.Ice, true);
        SetButtonsActive(ResourceType.Carbon, true);
        SetButtonsActive(ResourceType.Silicon, true);
        SetButtonsActive(ResourceType.RareEarths, true);
        SetButtonsActive(ResourceType.Alloys, true);
        SetButtonsActive(ResourceType.Fuel, true);
        SetButtonsActive(ResourceType.Ceramics, true);
        SetButtonsActive(ResourceType.Graphene, true);
        SetButtonsActive(ResourceType.Microchips, true);
        SetButtonsActive(ResourceType.Helium3, true);
        SetButtonsActive(ResourceType.Superconductors, true);
        SetButtonsActive(ResourceType.MetallicHydrogen, true);
        SetButtonsActive(ResourceType.Antimatter, true);

        UpdateButtonStates();
    }

    private TextMeshProUGUI GetPriceTextForResource(ResourceType resourceType)
    {
        switch (resourceType)
        {
            case ResourceType.Ore: return orePriceText;
            case ResourceType.Ice: return icePriceText;
            case ResourceType.Carbon: return carbonPriceText;
            case ResourceType.Silicon: return siliconPriceText;
            case ResourceType.RareEarths: return rareEarthsPriceText;
            case ResourceType.Alloys: return alloysPriceText;
            case ResourceType.Fuel: return fuelPriceText;
            case ResourceType.Ceramics: return ceramicsPriceText;
            case ResourceType.Graphene: return graphenePriceText;
            case ResourceType.Microchips: return microchipsPriceText;
            case ResourceType.Helium3: return helium3PriceText;
            case ResourceType.Superconductors: return superconductorsPriceText;
            case ResourceType.MetallicHydrogen: return metallicHydrogenPriceText;
            case ResourceType.Antimatter: return antimatterPriceText;
            default: return null;
        }
    }
    public void ShowMarket()
    {
        gameObject.SetActive(true);
        UpdatePriceTexts();
        UpdateButtonStates();
    }

    public void HideMarket()
    {
        ExitSelectionMode();
        gameObject.SetActive(false);
    }

    public void UpdatePricesAfterTransaction()
    {
        UpdatePriceTexts();
        UpdateButtonStates();
    }

}