using System.Collections.Generic;
using System.Linq;
using UnityEngine; // Added for GameObject

public class VacuumDataEffect : ICardEffect
{
    private string moduleName;
    private int powerRequired;
    
    public VacuumDataEffect(string moduleName, int powerRequired)
    {
        this.moduleName = moduleName;
        this.powerRequired = powerRequired;
    }
    
    public bool CanExecute(Player player, GameObject location, Dictionary<string, object> parameters)
    {
        // Check power
        if (player.GetAvailablePower(location) < powerRequired)
            return false;
        
        // Check if location is valid (barren, ice-shell, or orbit)
        PlanetBody planetBody = location.GetComponent<PlanetBody>();
        if (planetBody != null)
        {
            if (planetBody.Type != PlanetBody.BodyType.Barren && 
                planetBody.Type != PlanetBody.BodyType.IceShell)
            {
                return false;
            }
        }
        // If no PlanetBody component, assume it's an orbit location (valid)
        
        return true;
    }
    
    public bool Execute(Player player, GameObject location, Dictionary<string, object> parameters)
    {
        if (!CanExecute(player, location, parameters))
            return false;
            
        // Add Science
        player.ScienceValue += 1;
        
        Debug.Log($"Player {player.PlayerId} used Vacuum Data Module at {location.name}: +1 Science");
        return true;
    }
    
    public string GetDescription()
    {
        return "Gain 1 Science (only on barren/ice-shell planets or in orbit)";
    }
}