using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine; 
// Module build effects
public class ModuleBuildEffect : ICardEffect
{
    private string name;
    private List<Resource> buildCost;
    private bool requiresAdvancedAssembler;
    
    public ModuleBuildEffect(string name, List<Resource> buildCost, bool requiresAdvancedAssembler)
    {
        this.name = name;
        this.buildCost = buildCost;
        this.requiresAdvancedAssembler = requiresAdvancedAssembler;
    }
    
    public bool CanExecute(Player player, GameObject location, Dictionary<string, object> parameters)
    {
        // Check if player has resources to build
        foreach (var resource in buildCost)
        {
            if (player.GetResourceAmount(location, resource.Type) < resource.Amount)
                return false;
        }
        
        // Check if player has required assembler type
        if (requiresAdvancedAssembler)
        {
            // Advanced Assembler required for facilities
            // Earth has one by default
            if (location.name != "Earth")
            {
                bool hasAdvancedAssembler = false;
                foreach (Module module in player.GetModulesOnPlanet(location))
                {
                    if (module.Name == "Advanced Assembler")
                    {
                        hasAdvancedAssembler = true;
                        break;
                    }
                }
                
                if (!hasAdvancedAssembler)
                    return false;
            }
        }
        else
        {
            // Basic Assembler required for modules
            // Earth has one by default
            if (location.name != "Earth")
            {
                bool hasAssembler = false;
                foreach (Module module in player.GetModulesOnPlanet(location))
                {
                    if (module.Name == "Assembler Module" || module.Name == "Advanced Assembler")
                    {
                        hasAssembler = true;
                        break;
                    }
                }
                
                // Check for Universal Builder ship
                if (!hasAssembler)
                {
                    foreach (Ship ship in player.GetShipsAtLocation(location))
                    {
                        if (ship.Name == "Universal Builder")
                        {
                            hasAssembler = true;
                            break;
                        }
                    }
                }
                
                if (!hasAssembler)
                    return false;
            }
        }
        
        return true;
    }
    
    public bool Execute(Player player, GameObject location, Dictionary<string, object> parameters)
    {
        // Deduct resources for building
        foreach (var resource in buildCost)
        {
            if (!player.UseResource(location, resource.Type, resource.Amount))
                return false;
        }
        
        // Create the module - determine type from name or parameters
        ModuleType moduleType = DetermineModuleType();
        
        Module newModule = new Module
        {
            Name = name,
            Type = moduleType,
            PowerOutput = 0, // Default, will be set if it's a power module
            PowerRequired = 0 // Default, will be set if it's a processor or extractor
        };
        
        // Set module-specific properties
        ConfigureModuleProperties(newModule, parameters);
        
        // Add to player's modules
        player.AddModule(location, newModule);
        
        Debug.Log($"Player {player.PlayerId} built {name} at {location.name}");
        return true;
    }
    
    private ModuleType DetermineModuleType()
    {
        string lowerName = name.ToLower();
        
        if (lowerName.Contains("power") || lowerName.Contains("solar") || lowerName.Contains("reactor"))
            return ModuleType.Power;
        else if (lowerName.Contains("extractor") || lowerName.Contains("drill") || lowerName.Contains("mine"))
            return ModuleType.Extractor;
        else if (lowerName.Contains("processor") || lowerName.Contains("factory") || lowerName.Contains("foundry"))
            return ModuleType.Processor;
        else if (lowerName.Contains("wonder") || lowerName.Contains("monument"))
            return ModuleType.Wonder;
        else
            return ModuleType.Other;
    }
    
    private void ConfigureModuleProperties(Module module, Dictionary<string, object> parameters)
    {
        // Set properties based on module type
        switch (module.Type)
        {
            case ModuleType.Power:
                // Check parameters for power output
                if (parameters != null && parameters.ContainsKey("powerOutput"))
                {
                    module.PowerOutput = Convert.ToInt32(parameters["powerOutput"]);
                }
                else
                {
                    // Default power output (1 for basic modules, more for advanced)
                    module.PowerOutput = name.ToLower().Contains("advanced") ? 2 : 1;
                }
                break;
                
            case ModuleType.Extractor:
                // Extractors typically require 1 power
                module.PowerRequired = 1;
                break;
                
            case ModuleType.Processor:
                // Processors typically require 1 power, but some may need more
                module.PowerRequired = name.ToLower().Contains("advanced") ? 2 : 1;
                break;
                
            case ModuleType.Wonder:
                // Set VP value for wonders
                if (parameters != null && parameters.ContainsKey("victoryPoints"))
                {
                    module.VictoryPointValue = Convert.ToInt32(parameters["victoryPoints"]);
                }
                else
                {
                    // Default VP for wonders
                    module.VictoryPointValue = 1;
                }
                module.IsWonder = true;
                break;
        }
        
        // Set any module description if provided
        if (parameters != null && parameters.ContainsKey("description"))
        {
            module.ProcessorDescription = parameters["description"].ToString();
        }
    }
    
    public string GetDescription()
    {
        string costString = string.Join(", ", buildCost.Select(r => $"{r.Amount} {r.Type}"));
        string requirementString = requiresAdvancedAssembler ? 
            "Requires Advanced Assembler" : "Requires Assembler";
        
        return $"Build cost: {costString}. {requirementString}.";
    }
}