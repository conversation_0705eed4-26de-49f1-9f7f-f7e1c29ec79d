using System.Collections.Generic;
using UnityEngine; 
// Generic upgrade effect
public class GenericUpgradeEffect : ICardEffect
{
    private string name;
    private string description;
    
    public GenericUpgradeEffect(string name, string description)
    {
        this.name = name;
        this.description = description;
    }
    
    public bool CanExecute(Player player, GameObject location, Dictionary<string, object> parameters)
    {
        return true;
    }
    
    public bool Execute(Player player, GameObject location, Dictionary<string, object> parameters)
    {
        Debug.Log($"Player {player.PlayerId} applied upgrade: {name}");
        return true;
    }
    
    public string GetDescription()
    {
        return description;
    }
}