using UnityEngine;

/// <summary>
/// Extension methods for Player to support undo functionality
/// </summary>
public static class PlayerExtensionForUndo
{
    /// <summary>
    /// Remove a technology from the player's technologies (for undo functionality)
    /// </summary>
    public static bool RemoveTechnology(this Player player, string technologyName)
    {
        // Get the player's technologies
        var technologies = player.GetTechnologies();
        
        // Find the technology to remove
        for (int i = 0; i < technologies.Count; i++)
        {
            if (technologies[i].cardName == technologyName)
            {
                // Call the internal method to modify the technologies list
                return RemoveTechnologyInternal(player, technologies[i]);
            }
        }
        
        Debug.LogWarning($"Technology {technologyName} not found in player's technologies!");
        return false;
    }
    
    /// <summary>
    /// Overload that takes the actual TechnologyCardData reference
    /// </summary>
    public static bool RemoveTechnology(this Player player, TechnologyCardData technology)
    {
        return RemoveTechnologyInternal(player, technology);
    }

    /// <summary>
    /// Internal method to remove a technology using reflection
    /// </summary>
    private static bool RemoveTechnologyInternal(Player player, TechnologyCardData technology)
    {
        // Get the technologies field using reflection
        System.Reflection.FieldInfo technologiesField = typeof(Player).GetField(
            "technologies",
            System.Reflection.BindingFlags.NonPublic |
            System.Reflection.BindingFlags.Instance);

        if (technologiesField == null)
        {
            Debug.LogError("Could not find 'technologies' field in Player class!");
            return false;
        }

        // Get the technologies list
        var technologies = technologiesField.GetValue(player) as System.Collections.Generic.List<TechnologyCardData>;
        if (technologies == null)
        {
            Debug.LogError("Player's technologies field is not a List<TechnologyCardData>!");
            return false;
        }

        // Remove the technology
        bool removed = technologies.Remove(technology);

        if (removed)
        {
            // Remove any effects this technology provided
            TechnologyEffectsManager effectsManager = TechnologyEffectsManager.Instance;
            if (effectsManager != null)
            {
                effectsManager.UpdateTechnologyEffects();
            }
        }
        else
        {
            Debug.LogWarning($"Could not remove technology {technology.cardName} from player {player.PlayerId}");
        }
        
        return removed;
    }
}