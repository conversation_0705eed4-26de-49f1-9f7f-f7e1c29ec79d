using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using TMPro;
using System.Collections.Generic;

public class CardRowUI : MonoBehaviour
{
    [Header("Card Row Settings")]
    [SerializeField] private GameObject cardUIPrefab; // Prefab for 2D UI card
    [SerializeField] private Transform cardRowContainer; // Parent for card UI elements
    [SerializeField] private float cardSpacing = 10f;
    [SerializeField] private float cardWidth = 150f;
    [SerializeField] private float cardHeight = 210f;
    [SerializeField] private bool enableDebugLogging = true; // New debug flag
    
    [Header("Cost Display")]
    [SerializeField] private Color lowCostColor = Color.green; // For 1 action cost
    [SerializeField] private Color mediumCostColor = Color.yellow; // For 2 action cost
    [SerializeField] private Color highCostColor = Color.red; // For 3 action cost
    
    private List<GameObject> cardUIElements = new List<GameObject>();
    private TechnologyDeckManager deckManager;
    
    private void Start()
    {
        deckManager = TechnologyDeckManager.Instance;
        
        if (deckManager == null)
        {
            Debug.LogError("TechnologyDeckManager not found!");
            return;
        }
        
        // Extra check to make sure cardRowContainer is set
        if (cardRowContainer == null)
        {
            cardRowContainer = transform.Find("CardRowContainer");
            if (cardRowContainer == null)
            {
                Debug.LogError("CardRowContainer not found! Card UI will not work correctly.");
                return;
            }
        }
        
        DebugLog("CardRowUI Start - cardRowContainer active: " + cardRowContainer.gameObject.activeSelf);
        
        // Initialize the card row UI
        InitializeCardRow();
        
        // Subscribe to deck manager events
        deckManager.OnDeckInitializationComplete += InitializeCardRow;
        deckManager.OnCardRowChanged += RefreshCardRow;
    }
    
    private void OnDestroy()
    {
        // Unsubscribe from events when destroyed
        if (deckManager != null)
        {
            deckManager.OnDeckInitializationComplete -= InitializeCardRow;
            deckManager.OnCardRowChanged -= RefreshCardRow;
        }
    }
    
    // Debug helper method
    private void DebugLog(string message)
    {
        if (enableDebugLogging)
        {
            //Debug.Log("[CardRowUI] " + message);
        }
    }
    
    /// <summary>
    /// Initialize or refresh the card row UI
    /// </summary>
    public void InitializeCardRow()
    {
        if (cardRowContainer == null)
        {
            Debug.LogError("CardRowContainer is null in InitializeCardRow!");
            return;
        }
        
        // Make sure container is active
        if (!cardRowContainer.gameObject.activeSelf)
        {
            DebugLog("CardRowContainer was inactive - activating it");
            cardRowContainer.gameObject.SetActive(true);
        }
        
        // Keep track of which cards are already taken
        Dictionary<int, bool> cardTakenStates = new Dictionary<int, bool>();
        foreach (GameObject cardUI in cardUIElements)
        {
            if (cardUI != null)
            {
                CardIndexTracker tracker = cardUI.GetComponent<CardIndexTracker>();
                CardTakenMarker marker = cardUI.GetComponent<CardTakenMarker>();
                
                if (tracker != null && marker != null)
                {
                    cardTakenStates[tracker.CardIndex] = true;
                }
            }
        }
        
        // Clear existing UI elements
        ClearCardRow();
        
        if (deckManager == null)
        {
            DebugLog("DeckManager is null in InitializeCardRow");
            return;
        }
        
        // Get current card row data
        int cardCount = deckManager.GetCardRowSize();
        DebugLog($"Creating {cardCount} cards in row");
        
        // Create cards from right to left
        for (int i = cardCount - 1; i >= 0; i--)
        {
            CardData cardData = deckManager.GetCardAtIndex(i);
            if (cardData != null)
            {
                CreateCardUIElement(cardData, i);
                
                // If this card was taken, update its visual state
                if (cardTakenStates.ContainsKey(i) && cardTakenStates[i])
                {
                    // Find the newly created card UI
                    foreach (GameObject cardUI in cardUIElements)
                    {
                        CardIndexTracker tracker = cardUI.GetComponent<CardIndexTracker>();
                        if (tracker != null && tracker.CardIndex == i)
                        {
                            SetCardUIInvisible(cardUI);
                            break;
                        }
                    }
                }
                // Also check with deckManager if card is taken
                else if (deckManager.IsCardTaken(i))
                {
                    // Find the newly created card UI
                    foreach (GameObject cardUI in cardUIElements)
                    {
                        CardIndexTracker tracker = cardUI.GetComponent<CardIndexTracker>();
                        if (tracker != null && tracker.CardIndex == i)
                        {
                            SetCardUIInvisible(cardUI);
                            break;
                        }
                    }
                }
            }
        }
        
        // Position the cards
        PositionCards();
    }
    
    /// <summary>
    /// Create a UI element for a card
    /// </summary>
    private void CreateCardUIElement(CardData cardData, int index)
    {
        if (cardUIPrefab == null || cardRowContainer == null) return;
        
        GameObject cardUI = Instantiate(cardUIPrefab, cardRowContainer);
        cardUI.name = $"CardUI_{cardData.Name}_{index}";
        
        // Add card index tracker component
        CardIndexTracker indexTracker = cardUI.AddComponent<CardIndexTracker>();
        indexTracker.CardIndex = index;
        
        // Set up the card visual
        SetupCardVisual(cardUI, cardData, index);

        // Set up glow effect
        SetupCardGlowEffect(cardUI, cardData);

        // Add click handler
        Button cardButton = cardUI.GetComponent<Button>();
        if (cardButton != null)
        {
            int cardIndex = index; // Capture for closure
            cardButton.onClick.AddListener(() => OnCardClicked(cardData, cardIndex));
        }
        
        // Insert at the beginning to maintain right-to-left order
        cardUIElements.Insert(0, cardUI);
    }

    /// <summary>
    /// Set up card glow effect for UI cards
    /// </summary>
    private void SetupCardGlowEffect(GameObject cardUI, CardData cardData)
    {
        // Add hover effects to the card
        EventTrigger eventTrigger = cardUI.GetComponent<EventTrigger>();
        if (eventTrigger == null)
        {
            eventTrigger = cardUI.AddComponent<EventTrigger>();
        }

        // Clear any existing triggers
        eventTrigger.triggers.Clear();

        // Add pointer enter event
        EventTrigger.Entry enterEntry = new EventTrigger.Entry();
        enterEntry.eventID = EventTriggerType.PointerEnter;
        enterEntry.callback.AddListener((data) => OnCardHoverEnter(cardUI));
        eventTrigger.triggers.Add(enterEntry);

        // Add pointer exit event
        EventTrigger.Entry exitEntry = new EventTrigger.Entry();
        exitEntry.eventID = EventTriggerType.PointerExit;
        exitEntry.callback.AddListener((data) => OnCardHoverExit(cardUI));
        eventTrigger.triggers.Add(exitEntry);

        // Add this UI card to the action checker system to control when it should glow
        CardActionChecker actionChecker = cardUI.AddComponent<CardActionChecker>();
        actionChecker.Initialize(cardData);
    }


    /// <summary>
    /// Handle hover enter for UI card glow effect
    /// </summary>
    private void OnCardHoverEnter(GameObject cardUI)
    {
        // Skip hover effects if card has been taken
        if (cardUI.GetComponent<CardTakenMarker>() != null)
        {
            return;
        }

        CardActionChecker actionChecker = cardUI.GetComponent<CardActionChecker>();
        if (actionChecker != null && actionChecker.ShouldGlow())
        {
            // Apply glow effect by brightening the image
            Image cardImage = cardUI.transform.Find("CardImage")?.GetComponent<Image>();
            if (cardImage != null)
            {
                cardImage.color = new Color(1f, 1f, 1f, 1f); // Full bright
            }

            // Scale up slightly
            cardUI.transform.localScale = Vector3.one * 1.05f;
        }
    }

    /// <summary>
    /// Handle hover exit for UI card glow effect
    /// </summary>
    private void OnCardHoverExit(GameObject cardUI)
    {
        // Skip hover effects if card has been taken
        if (cardUI.GetComponent<CardTakenMarker>() != null)
        {
            return;
        }

        // Reset image color
        Image cardImage = cardUI.transform.Find("CardImage")?.GetComponent<Image>();
        if (cardImage != null)
        {
            cardImage.color = Color.white;
        }

        // Reset scale
        cardUI.transform.localScale = Vector3.one;
    }

    /// <summary>
    /// Restore a previously taken card's visibility in the card row
    /// </summary>
    public void RestoreCard(int cardIndex)
    {
        // Find the specific card UI element with this index
        GameObject cardToRestore = null;
        foreach (GameObject cardUI in cardUIElements)
        {
            if (cardUI != null)
            {
                CardIndexTracker tracker = cardUI.GetComponent<CardIndexTracker>();
                if (tracker != null && tracker.CardIndex == cardIndex)
                {
                    cardToRestore = cardUI;
                    break;
                }
            }
        }

        if (cardToRestore != null)
        {
            // Only restore this specific card
            // The issue might be with shared materials or some other UI component affecting all cards

            // Remove the marker component first - this is important
            CardTakenMarker marker = cardToRestore.GetComponent<CardTakenMarker>();
            if (marker != null)
            {
                Destroy(marker);
            }

            // Make just this card visible again
            Image[] images = cardToRestore.GetComponentsInChildren<Image>(true);
            foreach (Image img in images)
            {
                // Create a new material instance to avoid shared materials
                if (img.material != null && img.material.shader != null)
                {
                    img.material = new Material(img.material);
                }

                Color color = img.color;
                color.a = 1f;
                img.color = color;
            }

            // Make text visible again
            TextMeshProUGUI[] texts = cardToRestore.GetComponentsInChildren<TextMeshProUGUI>(true);
            foreach (TextMeshProUGUI text in texts)
            {
                Color color = text.color;
                color.a = 1f;
                text.color = color;
            }

            // Re-enable button functionality for just this card
            Button button = cardToRestore.GetComponent<Button>();
            if (button != null)
            {
                button.interactable = true;
            }

            // Ensure this card is active
            cardToRestore.SetActive(true);

        }
        else
        {
            Debug.LogWarning($"Could not find card UI element for index {cardIndex}");
        }
    }

    /// <summary>
    /// Set up the visual elements of a card UI
    /// </summary>
    private void SetupCardVisual(GameObject cardUI, CardData cardData, int index)
    {
        // Find UI components
        Image cardImage = cardUI.transform.Find("CardImage")?.GetComponent<Image>();
        TextMeshProUGUI nameText = cardUI.transform.Find("CardName")?.GetComponent<TextMeshProUGUI>();
        TextMeshProUGUI costText = cardUI.transform.Find("CardCost")?.GetComponent<TextMeshProUGUI>();
        Image costBackground = cardUI.transform.Find("CostBackground")?.GetComponent<Image>();
        
        // Load and set card image
        if (cardImage != null)
        {
            string cardNameFormatted = cardData.Name.ToLower().Replace(" ", "");
            string imagePath = $"Cards/{cardData.Tier}{cardNameFormatted}";
            Texture2D cardTexture = Resources.Load<Texture2D>(imagePath);
            
            if (cardTexture != null)
            {
                cardImage.sprite = Sprite.Create(
                    cardTexture,
                    new Rect(0, 0, cardTexture.width, cardTexture.height),
                    new Vector2(0.5f, 0.5f)
                );
            }
        }
        
        // Set card name
        if (nameText != null)
        {
            nameText.text = cardData.Name;
        }
        
        // Set cost and color
        int cost = deckManager.GetCardCost(index);
        if (costText != null)
        {
            costText.text = cost.ToString();
        }
        
        if (costBackground != null)
        {
            switch (cost)
            {
                case 1:
                    costBackground.color = lowCostColor;
                    break;
                case 2:
                    costBackground.color = mediumCostColor;
                    break;
                case 3:
                    costBackground.color = highCostColor;
                    break;
            }
        }
    }
    
    /// <summary>
    /// Position the cards in the row
    /// </summary>
    private void PositionCards()
    {
        float totalWidth = (cardUIElements.Count * cardWidth) + ((cardUIElements.Count - 1) * cardSpacing);
        float startX = totalWidth / 2f;
        
        for (int i = 0; i < cardUIElements.Count; i++)
        {
            RectTransform rectTransform = cardUIElements[i].GetComponent<RectTransform>();
            if (rectTransform != null)
            {
                // Position from right to left
                rectTransform.anchoredPosition = new Vector2(
                    startX - (i * (cardWidth + cardSpacing)) - (cardWidth / 2f),
                    0
                );
                
                rectTransform.sizeDelta = new Vector2(cardWidth, cardHeight);
            }
        }
    }
    
    /// <summary>
    /// Clear all cards from the UI
    /// </summary>
    private void ClearCardRow()
    {
        DebugLog($"Clearing card row - {cardUIElements.Count} elements");
        foreach (GameObject cardUI in cardUIElements)
        {
            if (cardUI != null)
            {
                Destroy(cardUI);
            }
        }
        cardUIElements.Clear();
    }
    
    /// <summary>
    /// Handle card click
    /// </summary>
    private void OnCardClicked(CardData cardData, int index)
    {
        // Show card details in the detail display panel in the same way 3D cards do
        if (CardDetailDisplay.Instance != null)
        {
            // Use the actual GameObject as the source object
            GameObject sourceObj = null;
            foreach (GameObject cardUI in cardUIElements)
            {
                CardIndexTracker tracker = cardUI.GetComponent<CardIndexTracker>();
                if (tracker != null && tracker.CardIndex == index)
                {
                    sourceObj = cardUI;
                    break;
                }
            }
            
            // Pass CardSource.CardRow to match 3D behavior
            CardDetailDisplay.Instance.ShowCard(cardData, index, CardDetailDisplay.CardSource.CardRow, sourceObj);
        }
    }
    
    /// <summary>
    /// Refresh the card row (called when cards are taken or the row advances)
    /// </summary>
    public void RefreshCardRow()
    {
        DebugLog("RefreshCardRow called");
        InitializeCardRow();
    }
    
    /// <summary>
    /// Mark a specific card as taken
    /// </summary>
    public void MarkCardAsTaken(int index)
    {
        DebugLog($"MarkCardAsTaken called for index {index}");
        
        // Find the card UI element with this index
        GameObject cardToHide = null;
        foreach (GameObject cardUI in cardUIElements)
        {
            if (cardUI != null)
            {
                CardIndexTracker tracker = cardUI.GetComponent<CardIndexTracker>();
                if (tracker != null && tracker.CardIndex == index)
                {
                    cardToHide = cardUI;
                    break;
                }
            }
        }
        
        if (cardToHide != null)
        {
            SetCardUIInvisible(cardToHide);
            DebugLog($"Made card UI invisible for index {index}");
        }
        else
        {
            DebugLog($"Could not find card UI element for index {index}");
        }
    }
    
    /// <summary>
    /// Make a card UI element invisible
    /// </summary>
    private void SetCardUIInvisible(GameObject cardUI)
    {
        // Make all images transparent
        Image[] images = cardUI.GetComponentsInChildren<Image>(true);
        foreach (Image img in images)
        {
            Color color = img.color;
            color.a = 0;
            img.color = color;
        }
        
        // Make all text transparent
        TextMeshProUGUI[] texts = cardUI.GetComponentsInChildren<TextMeshProUGUI>(true);
        foreach (TextMeshProUGUI text in texts)
        {
            Color color = text.color;
            color.a = 0;
            text.color = color;
        }
        
        // Disable button functionality
        Button button = cardUI.GetComponent<Button>();
        if (button != null)
        {
            button.interactable = false;
        }
        
        // Add a marker component to indicate it's taken
        if (!cardUI.GetComponent<CardTakenMarker>())
        {
            cardUI.AddComponent<CardTakenMarker>();
        }
    }
}