// UniversalCard.cs
using UnityEngine;
using System.Collections.Generic;

public class UniversalCard : MonoBehaviour
{
    private CardData data;
    private ICardEffect effect;
    
    public void Initialize(CardData data, ICardEffect effect)
    {
        this.data = data;
        this.effect = effect;
        
        // Setup visuals
        //SetupVisuals();
    }
    
    public bool CanActivate(Player player, GameObject location)
    {
        return effect?.CanExecute(player, location, new Dictionary<string, object>()) ?? false;
    }
    
    public bool Activate(Player player, GameObject location)
    {
        return effect?.Execute(player, location, new Dictionary<string, object>()) ?? false;
    }
}