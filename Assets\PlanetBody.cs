using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Represents a planet or moon in the game
/// </summary>
public class PlanetBody : MonoBehaviour
{
    public enum BodyType
    {
        Barren,
        IceShell,
        Atmospheric,
        GasGiant
    }
    
    [Header("Planet Information")]
    [SerializeField] private string bodyName;
    [SerializeField] private BodyType bodyType;
    [SerializeField] private bool canAerobrake = false;
    [SerializeField] private bool highRadiation = false;
    [SerializeField] private List<ResourceDeposit> deposits = new List<ResourceDeposit>();
    
    [Header("Orbit Information")]
    [SerializeField] private float deltaVToOrbit = 10.0f; // DeltaV to go from surface to orbit
    [SerializeField] private float deltaVFromOrbit = 8.0f; // DeltaV to go from orbit to surface
    
    // Runtime state
    private List<Player> playersPresent = new List<Player>();
    private Player occupyingPlayer = null; // Player with military control
    private Dictionary<Player, int> playerShipStrength = new Dictionary<Player, int>();
    private bool hasBeenSurveyed = false;
    
    // Properties
    public string Name => bodyName;
    public BodyType Type => bodyType;
    public bool CanAerobrake => canAerobrake;
    public bool HighRadiation => highRadiation;
    public Player OccupyingPlayer => occupyingPlayer;
    public bool HasBeenSurveyed => hasBeenSurveyed;
    
    private void Awake()
    {
        // Ensure the name is set
        if (string.IsNullOrEmpty(bodyName))
        {
            bodyName = gameObject.name;
        }
    }
    
    /// <summary>
    /// Get all deposits on this planet
    /// </summary>
    public List<ResourceDeposit> GetDeposits()
    {
        return new List<ResourceDeposit>(deposits);
    }
    
    /// <summary>
    /// Mine this planet's resources for a player
    /// </summary>
    public void Mine(Player player)
    {
        // Don't mine if player doesn't have mining equipment here
        // This would be handled by the calling code typically
        
        // Apply technology effects via the TechnologyEffectsManager
        TechnologyEffectsManager effectsManager = TechnologyEffectsManager.Instance;
        
        // For each deposit, give the player resources
        foreach (ResourceDeposit deposit in deposits)
        {
            int baseAmount = 1;
            int amount = baseAmount;
            
            // Apply technology bonuses if available
            if (effectsManager != null)
            {
                switch (deposit.ResourceType)
                {
                    case ResourceType.Ore:
                    case ResourceType.Ice:
                        amount = effectsManager.GetAdditionalOreIce(baseAmount);
                        break;
                        
                    case ResourceType.Carbon:
                    case ResourceType.Silicon:
                        amount = effectsManager.GetAdditionalNonMetal(baseAmount);
                        break;
                        
                    case ResourceType.RareEarths:
                        amount = effectsManager.GetAdditionalRareEarths(baseAmount);
                        break;
                }
            }
            
            // Add resources to the player at this location
            player.AddResource(gameObject, deposit.ResourceType, amount);
            Debug.Log($"Player {player.PlayerId} mined {amount} {deposit.ResourceType} from {bodyName}");
        }
    }
    
    /// <summary>
    /// Mark this planet as surveyed
    /// </summary>
    public void MarkAsSurveyed(Player player)
    {
        hasBeenSurveyed = true;
        
        // Trigger additional effects 
        // For example, player gets science or VP from survey
        
        Debug.Log($"Player {player.PlayerId} surveyed {bodyName}");
    }
    
    /// <summary>
    /// Update military occupation status
    /// </summary>
    public void UpdateMilitaryOccupation()
    {
        // Find the player with the highest ship strength
        Player strongestPlayer = null;
        int highestStrength = 0;
        bool isTied = false;
        
        foreach (var kvp in playerShipStrength)
        {
            if (kvp.Value > highestStrength)
            {
                highestStrength = kvp.Value;
                strongestPlayer = kvp.Key;
                isTied = false;
            }
            else if (kvp.Value == highestStrength && highestStrength > 0)
            {
                isTied = true;
            }
        }
        
        // If there's a tie, no one has military occupation
        if (isTied)
        {
            if (occupyingPlayer != null)
            {
                Debug.Log($"{bodyName} is no longer occupied due to a military standoff");
                occupyingPlayer = null;
            }
        }
        else if (strongestPlayer != occupyingPlayer)
        {
            string previousOccupier = (occupyingPlayer != null) ? $"Player {occupyingPlayer.PlayerId}" : "no one";
            string newOccupier = (strongestPlayer != null) ? $"Player {strongestPlayer.PlayerId}" : "no one";
            
            Debug.Log($"{bodyName} military occupation changed from {previousOccupier} to {newOccupier}");
            occupyingPlayer = strongestPlayer;
        }
    }
    
    /// <summary>
    /// Register a ship arriving at this location
    /// </summary>
    public void RegisterShipArrival(Ship ship, Player owner)
    {
        // Add the player to the list of present players if not already there
        if (!playersPresent.Contains(owner))
        {
            playersPresent.Add(owner);
        }
        
        // Update ship strength
        if (!playerShipStrength.ContainsKey(owner))
        {
            playerShipStrength[owner] = 0;
        }
        
        playerShipStrength[owner] += ship.Strength;
        
        // Update military occupation
        UpdateMilitaryOccupation();
        
        Debug.Log($"Ship {ship.Name} arrived at {bodyName}");
    }
    
    /// <summary>
    /// Register a ship departing from this location
    /// </summary>
    public void RegisterShipDeparture(Ship ship, Player owner)
    {
        // Remove ship strength
        if (playerShipStrength.ContainsKey(owner))
        {
            playerShipStrength[owner] -= ship.Strength;
            
            // If player has no more ships or modules, remove from present players
            if (playerShipStrength[owner] <= 0)
            {
                playerShipStrength.Remove(owner);
                
                // Check if player has any modules here before removing
                bool hasModules = false;
                // This would check if player has modules on this planet
                
                if (!hasModules)
                {
                    playersPresent.Remove(owner);
                }
            }
        }
        
        // Update military occupation
        UpdateMilitaryOccupation();
        
        Debug.Log($"Ship {ship.Name} departed from {bodyName}");
    }
    
    /// <summary>
    /// Get the delta-V cost to move from this body to orbit
    /// </summary>
    public float GetDeltaVToOrbit()
    {
        return deltaVToOrbit;
    }
    
    /// <summary>
    /// Get the delta-V cost to move from orbit to this body
    /// </summary>
    public float GetDeltaVFromOrbit()
    {
        // Apply aerobraking discount if possible
        float cost = deltaVFromOrbit;
        if (canAerobrake)
        {
            cost *= 0.5f; // 50% discount for aerobraking
        }
        
        return cost;
    }
    
    /// <summary>
    /// Get all players present at this location
    /// </summary>
    public List<Player> GetPlayersPresent()
    {
        return new List<Player>(playersPresent);
    }
}