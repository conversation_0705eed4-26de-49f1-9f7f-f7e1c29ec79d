using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Types of effects that Earth cards can have
/// </summary>
public enum EarthCardEffectType
{
    // Tier 1 effects
    AllPlayersGainBasicResources,
    AllPlayersGainMoney,
    AllPlayersGainOreIce,
    AllPlayersGainCarbonSilicon,
    AllPlayersGainScience,
    RichDeposits,
    ShipStrengthVictoryPoints,
    
    // Tier 2 effects
    MegaExtraction,
    CoastalFloods,
    GlobalWarming,
    MarketFrenzy,
    LithographyBreakthrough,
    MaterialsBreakthrough,
    SustainablePlastics,
    AIBreakthrough,
    MilitaryConflict,
    SurveyScience,
    FinancialCrisis,
    Corruption,
    Imperialism,
    Exploitation,
    Subjugation,
    
    // Tier 3 effects
    ArmsRace,
    ColonialTaxation,
    SpaceOpera,
    ExoticMaterialsResearch,
    ExplorationVictoryPoints,
    DominationVictoryPoints,
    ExpansionVictoryPoints,
    ConstructionVictoryPoints,
    PowerVictoryPoints,
    MiningVictoryPoints,
    IndustryVictoryPoints,
    AlienInvasion,
    AstrobiologyBreakthrough,
    AISingularity,
    
    // Special effects
    ElectionChoice, // Player must choose between two options
    Custom // Custom effect handled by special code
}

/// <summary>
/// Represents an Earth card with its effects
/// </summary>
[System.Serializable]
public class EarthCard
{
    public string Name { get; set; }
    public string Description { get; set; }
    public int Tier { get; set; }
    public EarthCardEffectType EffectType { get; set; }
    
    // Optional additional parameters for specific card types
    public List<ResourceType> ResourcesAffected { get; set; } = new List<ResourceType>();
    public int ResourceAmount { get; set; } = 1;
    public int MoneyAmount { get; set; } = 0;
    public int ScienceAmount { get; set; } = 0;
    public int VictoryPointAmount { get; set; } = 0;
    
    // For election cards with choices
    public string Option1Text { get; set; }
    public string Option2Text { get; set; }
    public EarthCardEffectType Option1Effect { get; set; }
    public EarthCardEffectType Option2Effect { get; set; }
    
    /// <summary>
    /// Get a color based on the card's tier
    /// </summary>
    public Color GetTierColor()
    {
        switch (Tier)
        {
            case 1:
                return new Color(0.2f, 0.6f, 0.2f); // Green for Tier 1
            case 2:
                return new Color(0.9f, 0.6f, 0.1f); // Orange for Tier 2
            case 3:
                return new Color(0.8f, 0.1f, 0.1f); // Red for Tier 3
            default:
                return Color.white;
        }
    }
}