count,name,type,sub-type,science,build cost,effect,sol encyclopedia,real quote,sci-fi
4,Universal Processor Module,Processor,Module,5,"1 Alloy, 1 Ceramic, 1 Graphene, 1 Microchip",Activate (1 Power): Perform any of the basic processor actions (see Earth for recipes),"The dream of a single deployable fabrication system drove decades of research. The Universal Processor was a breakthrough from a Mars-based design collective during the economic boom following the Ice Wars.\nSol Encyclopedia, Vol. 5: Post-Scarcity",,
4,Quantum Forge Module,Processor,Module,4,"2 Alloy, 1 Ceramic, 1 Microchip","Activate (1 Power, 1 Rare Earths, 1 Ceramic): +1 Superconductor","Reaction chambers balanced particles in entangled states to shape matter with atomic precision previously thought impossible. These forges became instrumental in the synthesis of exotic materials needed for megastructure construction.\nSol Encyclopedia, Vol. 5: Post-Scarcity",,
4,Mass Driver,Processor,Facility,3,"3 Alloy, 1 Microchip",Activate (2 Power): Send up to 4 cargo to orbit,"Originally designed for lunar launch systems, the mass driver matured into a critical logistics tool. These electromagnetic rail platforms launched cargo to orbit without fuel, turning isolated worlds into efficient resource pipelines. Their emergence marked the transition from chemical dependence to infrastructure-led mobility.\nSol Encyclopedia, Vol. 3: The Inner Worlds",,
3,Quantum Computer Module,Processor,Module,5,"2 Alloy, 3 Microchip, 1 Superconductor",Activate (2 Power): +2 Science,"These modules offered more than raw processing - they coordinated entire planetary industries, forecasted economic perturbations, and even helped stabilize AI psychology. Each was a compact singularity of decision-making power.\nSol Encyclopedia, Vol. 5: Post-Scarcity",,
4,Sabatier Reactor Module,Processor,Module,4,"1 Alloy, 1 Ceramic, 1 Microchip","Activate (1 Power, 1 Carbon, 1 Ice): +3 Fuel","Mass production of hydrocarbon fuels in space was an important step to achieving Earth-independent supply chains.\nSol Encyclopedia, Vol. 3: The Inner Worlds",,
4,Mag Forge Array,Processor,Facility,4,"3 Alloy, 1 Ceramic","Activate (2 Power, 3 Ore, 1 Carbon): +4 Alloy","The fusion economy sparked a renaissance in alloy science. Superconducting coil and plasma torus field technologies enabled the synthesis of exotic alloys under extreme conditions.\nSol Encyclopedia, Vol. 5: Post-Scarcity",,
3,Regolith Adsorption Separator,Extractor,Facility,4,"2 Alloy, 1 Ceramic, 1 Microchip",Auto activate at Moon surface (2 Power): +1 Helium-3,"Moon dust proved more useful than expected. By using selective chemical adsorption, lunar facilities could isolate Helium-3 from regolith. Once dismissed as a theoretical fusion fuel, Helium-3’s practical extraction marked a turning point in the solar power balance.\nSol Encyclopedia, Vol. 5: Post-Scarcity",,
4,Thermal Bore Drill,Extractor,Facility,4,"2 Alloy, 2 Ceramic",Auto activate (2 Power): +3 Ore per deposit,"Conventional mining tools failed against the crust of planetary bodies baked in eons of pressure. The Thermal Bore Drill used concentrated thermal lances to vaporize rock, exposing ore veins un-perturbed for billions of years.\nSol Encyclopedia, Vol. 5: Post-Scarcity",,
3,Cryo Fracture Rig,Extractor,Facility,4,"2 Alloy, 1 Ceramic, 1 Microchip",Auto activate (2 Power): +3 Ice per deposit,"Ice-shell moons presented a unique challenge: mining materials buried beneath kilometers of frozen surface. The Cryo Fracture Rig shattered these challenges, literally, by inducing controlled cryogenic fractures to liberate volatiles and water ice at scale. Outer system life thrived on the success of the new technology.\nSol Encyclopedia, Vol. 4: Ice War",,
3,Crust Sorter,Extractor,Facility,4,"3 Alloy, 1 Ceramic, 1 Microchip",Auto activate (2 Power): +2 Silicon and Rare Earths per deposit,"The Iotech Mk.II crust sorter deployed particle scanning arrays and targeted phase transitions to separate valuable rare minerals with precision. It was the ace in the hole for the outer worlds, providing elemental lifeblood for solar panels and advanced electronics.\nSol Encyclopedia, Vol. 4: Ice War",,
3,Sequestration Array,Extractor,Facility,3,"2 Alloy, 2 Graphene",Auto activate (2 Power): +2 Carbon per deposit,"By utilizing vast graphene membrane molecular sieves, even the rarified atmosphere of Mars could be used as the feedstock for the mass production of carbon compounds.\nSol Encyclopedia, Vol. 3: The Inner Worlds",,
3,Helium Scoop Module,Extractor,Module,4,"1 Alloy, 1 Graphene, 1 Microchip",Auto activate at Gas giant surface or orbit (1 Power): Gain +1 Helium-3,"Growing demand for fusion power encouraged the development of new Helium-3 fuel sources. Scoop ships hovering with magnetic stabilizers collected trace Helium-3 from the upper atmosphere of Saturn.\nSol Encyclopedia, Vol. 4: Ice War",,
4,Von Braun Wheel,Habitation,Facility,4,"2 Alloy, 2 Ceramic, 1 Graphene, 1 Microchip",+2 VP and +3 $ per turn. Operational in orbit only. Limit 1 per orbit,"With artificial spin gravity and a combination of comfort and utility, the Von Braun Wheel became the standard for luxury orbitals.\nSol Encyclopedia, Vol. 5: Post-Scarcity",,
3,Naval Administration Module,Habitation,Module,4,"2 Alloy, 1 Ceramic, 1 Microchip",+1 VP per turn if you occupy this world. Moving ships from this world or its orbit costs no actions. Limit 1 per world,"Occupation meant more than strength - it required control. Naval logistics stations were one of the first non-tourist applications of space habitat technology.\nSol Encyclopedia, Vol. 4: Ice War",,
3,Lava Tube Habitats,Habitation,Facility,4,"3 Alloy, 1 Graphene, 1 Microchip",+3 VP per turn. Barren world only,"Sub-surface lava tubes offered natural radiation shielding and thermal stability. Colonies built inside them were immune to surface hazards and difficult to assault. These habitats gave new life to barren worlds - transforming dead rocks into homes for thousands.\nSol Encyclopedia, Vol. 4: Ice War",,
3,Gallium Arsenide Solar Module,Power,Module,4,4 Rare Earths,4 Power. Build in orbit only,,,
4,Tokamak Fusion Module,Power,Module,5,"1 Alloy, 1 Ceramic, 1 Microchip, 1 Helium-3, 1 Superconductor",5 power,"Deuterium-Tritium fusion power - while easier to initiate - was found to be prohibitively difficult in practice due to its high neutron flux. Aneutronic fusion - the same He³-He³ reaction that powers the proton-proton chain at the core of our sun - is what allowed for the fusion economy to finally emerge at scale.\nSol Encyclopedia, Vol. 5: Post-Scarcity",,
4,Autonomous Assembler,Ship,Ship,5,"1 Alloy, 1 Graphene, 1 Microchip","Cargo 2x2. 10 delta-v per fuel. Build in orbit. Can't move to surface. Activate: Build a module, facility, or ship",,,
3,Frigate,Ship,Ship,3,"2 Alloy, 2 Ceramic, 1 Microchip",Cargo 2x3. 6 delta-v per fuel. Strength 2,,,
3,Graphene Dirigible,Ship,Ship,2,"2 Graphene, 1 Microchip",Cargo 2x4. No fuel required. Can only move between atmospheric body's surface and orbit,,,"""If you are in a shipwreck and all the boats are gone, a piano top buoyant enough to keep you afloat that comes along makes a fortuitous life preserver. But this is not to say that the best way to design a life preserver is in the form of a piano top. I think that we are clinging to a great many piano tops""\nBuckminster Fuller"
3,Asteroid Tug,Ship,Ship,4,"3 Alloy, 2 Ceramic, 1 Microchip",Cargo 2x4. 10 delta-v per fuel. Build in orbit. Can't move to surface. Activate at asteroid belt: Gain +3 any one basic resource,"The second generation of deep space miners ferried whole asteroid bodies into orbit for processing.\nSol Encyclopedia, Vol. 5: ","""Giant mirrors focus sunlight and heat up asteroid rock to boil out the gases. Grinders break up the dried rocks into gravel and dust, and centrifuges separate dense from light elements""\nKurzgesagt ""Unlimited Resources From Space""",
3,Fusion Electric Drives,Upgrade,,4,,Your ships with any Helium-3 cargo move without consuming fuel,,,
3,Mycotecture,Upgrade,,3,,Once per turn you may pay 1 Carbon instead of 1 Alloy in any cost,,,
3,Merchant Spaceports,Upgrade,,3,,You may trade with the market at any world,,,
1,Space Elevator,Wonder,Facility,,"1 Alloy, 5 Graphene, 1 Superconductor",Build in orbit. 2 VP/turn. Activate (1 Power): Move up to 6 cargo to or from surface,,,
1,Dyson Swarm,Wonder,Facility,,"9 Silicon, 3 Alloy, 2 Superconductor","Build in low solar orbit. 1 VP/turn per power module. Develop the Rectenna power module (Cost: 1 Silicon, 1 Alloy, produces 3 Power)",,,"""We can rarely see far enough ahead to know which road leads to damnation. Whoever concerns himself with big technology, either to push it forward or to stop it, is gambling in human lives."" \nFreeman Dyson ""Disturbing the Universe"""
1,Prototype Fusion Reactor,Wonder,Facility,,"2 Alloy, 3 Ceramic, 1 Microchip, 1 Helium-3",6 Power,,,"""To feel it's there in your hands, to release this energy that fuels the stars, to let it do your bidding. To perform these miracles, to lift a million tons of rock into the sky"" \nFreeman Dyson"
1,Orbital Ring,Wonder,Facility,,"4 Alloy, 2 Ceramic, 4 Graphene, 2 Superconductor",Build in orbit. 2 VP/turn. +2 Actions,,"""Orbital rings could make space as accessible as the sea""\nPaul Birch ""Orbital Ring Systems and Mass-Stream Technology""",
1,Asteroid Colony,Wonder,Facility,,"4 Alloy, 1 Ceramic, 1 Graphene, 1 Microchip",Build at Asteroid belt. 2 VP/turn. Activate (1 Power): +1 Any basic resource,,,
1,Tethered Ring Launcher,Wonder,Facility,,"3 Alloy, 4 Graphene, 1 Superconductor",Build on surface. 2VP/turn. Activate (1 Power): Move a ship to orbit,,"""To decarbonize off-world supply lines, future space vehicles will be launched with an electrically powered mass driver that is supported in the stratosphere by the Tethered Ring. [...] The sum of the inertial force vector and the tensile force vector is a vector that is equal and opposite to the gravity force vector. The combined inertial and tensile forces prevent the ring from falling due to the force of gravity""\nPhil Swan ""A Brief Explanation of the Tethered Ring""",
1,Lofstrom Launch Loop,Wonder,Facility,,"4 Alloy, 2 Graphene",Build on surface. 2 VP/turn. Activate (3 Power): Move a ship to orbit,"The Launch Loop is an Earth­ surface ­based launching utility that stores energy and momentum in a very long, small cross­section iron rotor loop moving at high velocity. The downward forces necessary to deflect the rotor from its otherwise straight path support a magnetically­ levitated track system, control cables, and vehicles at high altitudes against gravity\nSol Encyclopedia, Vol. 3: The Inner Worlds","""Low ­cost space utilities such as the Launch Loop will replace rockets in high volume applications, making space settlement and industrialization economically practical""\nKeith Lofstrom ""The Launch Loop""",
3,Mining Initiative II,Initiative,,,,Gain 1 of each resource per deposit at a location where you own an extractor,,,
3,Research Initiative II,Initiative,,,,Gain 3 science,,,
3,Military Initiative II,Initiative,,,,Steal 2 resources from a player where you have the highest strength,,,
3,Naval Initiative II,Initiative,,,,Your next ship build this turn costs 2 less Alloy and no action,,,
3,Construction Initiative II,Initiative,,,,"Build a module or facility, it costs 2 less Alloy",,,