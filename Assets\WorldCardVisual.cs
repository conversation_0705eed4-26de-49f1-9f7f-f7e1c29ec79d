using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

/// <summary>
/// Handles the visual representation of a world card in a player's play area
/// </summary>
public class WorldCardVisual : MonoBehaviour
{
    // Reference to the celestial body this card represents
    private GameObject celestialBody;

    // Resource counts display
    private Dictionary<ResourceType, int> resourceCounts = new Dictionary<ResourceType, int>();

    private void Awake()
    {

    }

    /// <summary>
    /// Set up this card to represent a celestial body
    /// </summary>
    public void SetupCard(GameObject body)
    {
        celestialBody = body;
    }

    /// <summary>
    /// Get the celestial body this card represents
    /// </summary>
    public GameObject GetCelestialBody()
    {
        return celestialBody;
    }

    /// <summary>
    /// Handle when the mouse is released on the card
    /// </summary>
    public void OnMouseUp()
    {
        // Check if mouse is over specific UI elements
        if (IsMouseOverSpecificUI())
        {
            return;
        }

        // Check if this click should be suppressed for build modes
        if (AssemblerHandler.ShouldSuppressCardClick())
        {
            Debug.Log("World card click suppressed for build mode");
            return;
        }

        // Show card details in the detail display panel
        if (CardDetailDisplay.Instance != null && celestialBody != null)
        {
            ShowWorldCardDetail(celestialBody, gameObject);
        }
    }

    private bool IsMouseOverSpecificUI()
    {
        Vector2 mousePos = Input.mousePosition;

        // Check detail panel
        CardDetailDisplay detailDisplay = CardDetailDisplay.Instance;
        if (detailDisplay != null)
        {
            GameObject detailPanel = detailDisplay.GetDetailPanel();
            if (detailPanel != null && detailPanel.activeInHierarchy)
            {
                RectTransform panelRect = detailPanel.GetComponent<RectTransform>();
                if (panelRect != null && RectTransformUtility.RectangleContainsScreenPoint(panelRect, mousePos))
                {
                    Debug.Log("Mouse is over detail panel");
                    return true;
                }
            }
        }

        // Check log panel
        GameLogManager logManager = GameLogManager.Instance;
        if (logManager != null)
        {
            // Use reflection to access private field
            System.Reflection.FieldInfo logPanelField = typeof(GameLogManager).GetField("logPanelTransform",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            if (logPanelField != null)
            {
                RectTransform logPanelRect = logPanelField.GetValue(logManager) as RectTransform;
                if (logPanelRect != null && RectTransformUtility.RectangleContainsScreenPoint(logPanelRect, mousePos))
                {
                    Debug.Log("Mouse is over log panel");
                    return true;
                }
            }
        }

        return false;
    }

    /// <summary>
    /// Static utility method to show a world card detail for any celestial body
    /// </summary>
    public static void ShowWorldCardDetail(GameObject celestialBody, GameObject sourceObject)
    {
        if (celestialBody == null || CardDetailDisplay.Instance == null)
            return;

        // Get the PlanetBody component
        PlanetBody planetBody = celestialBody.GetComponent<PlanetBody>();
        if (planetBody == null)
            return;

        // Get the planet name
        string planetName = planetBody.Name;
        if (string.IsNullOrEmpty(planetName))
        {
            planetName = celestialBody.name;
        }

        // Create a basic CardData with planet info
        CardData planetData = new CardData
        {
            Name = planetName,
            Type = "World",
            SubType = planetBody.Type.ToString(),
            Tier = 0,
            Effect = CreatePlanetDescription(planetBody),
            // Add a fake SolEncyclopedia section for flavor text
            SolEncyclopedia = $"Sol Encyclopedia: {planetName} is a place in the Solar System."
        };

        // Show card in detail display
        CardDetailDisplay.Instance.ShowCard(planetData, -1, CardDetailDisplay.CardSource.WorldCard, sourceObject);
    }

    /// <summary>
    /// Create a description of the planet for the card
    /// </summary>
    private static string CreatePlanetDescription(PlanetBody planetBody)
    {
        string desc = $"{planetBody.Name} - {planetBody.Type}";

        if (planetBody.CanAerobrake)
        {
            desc += "\nAerobraking available";
        }

        // Add deposit information
        List<ResourceDeposit> deposits = planetBody.GetDeposits();
        if (deposits.Count > 0)
        {
            desc += "";
            Dictionary<ResourceType, int> depositCounts = new Dictionary<ResourceType, int>();

            foreach (ResourceDeposit deposit in deposits)
            {
                if (!depositCounts.ContainsKey(deposit.ResourceType))
                {
                    depositCounts[deposit.ResourceType] = 0;
                }
                depositCounts[deposit.ResourceType]++;
            }

            foreach (var kvp in depositCounts)
            {
                desc += $"\n- {kvp.Key}: {kvp.Value}";
            }
        }

        return desc;
    }

    

    /// <summary>
    /// Get the planet body information including aerobraking capability
    /// </summary>
    public bool CanAerobrake()
    {
        if (celestialBody == null) return false;

        PlanetBody planetBody = celestialBody.GetComponent<PlanetBody>();
        if (planetBody != null)
        {
            return planetBody.CanAerobrake;
        }
        return false;
    }
}