using UnityEngine;
using System.Collections;

public class PlayerCameraController : MonoBeh<PERSON><PERSON>
{
    [Header("Camera References")]
    [SerializeField] private Camera mainCamera; // The default camera
    [SerializeField] public Camera[] playerCameras; // Array of cameras for each player area
    
    [Header("Animation Settings")]
    [SerializeField] private float transitionDuration = 0.5f; // Duration of the camera transition
    [SerializeField] private AnimationCurve transitionCurve = AnimationCurve.EaseInOut(0, 0, 1, 1); // Animation curve for smooth transitions
    
    [Header("Camera Movement")]
    [SerializeField] private float dragSpeed = 40.0f; // Speed of drag movement
    [SerializeField] private float keyMoveSpeed = 40.0f; // Speed of keyboard movement
    [SerializeField] private float zMoveRange = 30.0f; // How far up/down the camera can move from its starting position
    
    [Header("Zoom Settings")]
    [SerializeField] private float zoomSpeed = 1.0f; // Speed of zoom when using scroll wheel
    [SerializeField] private float minZoomSize = 2.0f; // Minimum orthographic size (zoomed in)
    [SerializeField] private float maxZoomSize = 15.0f; // Maximum orthographic size (zoomed out)
    
    // State tracking
    private int activePlayerCamera = -1; // -1 means main camera is active
    private Coroutine currentTransition;
    private Vector3 lastMousePosition;
    private bool isDragging = false;
    private bool isAtTopBoundary = false;
    private bool hasReleasedAtTop = false;
    private bool hasScrolledDown = false;

    // Store original camera positions
    private Vector3 mainCameraOriginalPosition;
    private Quaternion mainCameraOriginalRotation;
    private Vector3[] playerCameraOriginalPositions;
    private Quaternion[] playerCameraOriginalRotations;
    
    // Store original orthographic sizes
    private float mainCameraOriginalSize;
    private float[] playerCameraOriginalSizes;

    private Vector3 mainCameraLastPosition;
    private Quaternion mainCameraLastRotation;
    private float mainCameraLastSize;
    private bool hasStoredMainCameraPosition = false;

    private void Awake()
    {
        // Store original camera transforms
        StoreOriginalCameraTransforms();
    }
    
    private void Start()
    {
        // Ensure main camera is active at start
        if (mainCamera != null)
        {
            mainCamera.gameObject.SetActive(true);
            
            // Make sure main camera has an AudioListener
            if (mainCamera.GetComponent<AudioListener>() == null)
            {
                mainCamera.gameObject.AddComponent<AudioListener>();
            }
        }
        
        // Disable all player cameras initially
        if (playerCameras != null)
        {
            foreach (Camera playerCam in playerCameras)
            {
                if (playerCam != null)
                {
                    playerCam.gameObject.SetActive(false);
                    
                    // Remove AudioListener from player cameras
                    AudioListener listener = playerCam.GetComponent<AudioListener>();
                    if (listener != null)
                    {
                        Destroy(listener);
                    }
                }
            }
        }
    }
    
    /// <summary>
    /// Store the original positions and rotations of all cameras
    /// </summary>
    private void StoreOriginalCameraTransforms()
    {
        
        if (mainCamera != null)
        {
            mainCameraOriginalPosition = mainCamera.transform.position;
            mainCameraOriginalRotation = mainCamera.transform.rotation;
            
            // Store original orthographic size
            if (mainCamera.orthographic)
            {
                mainCameraOriginalSize = mainCamera.orthographicSize;
            }
        }
        
        if (playerCameras != null && playerCameras.Length > 0)
        {
            playerCameraOriginalPositions = new Vector3[playerCameras.Length];
            playerCameraOriginalRotations = new Quaternion[playerCameras.Length];
            playerCameraOriginalSizes = new float[playerCameras.Length];
            
            for (int i = 0; i < playerCameras.Length; i++)
            {
                if (playerCameras[i] != null)
                {
                    playerCameraOriginalPositions[i] = playerCameras[i].transform.position;
                    playerCameraOriginalRotations[i] = playerCameras[i].transform.rotation;
                    
                    // Store original orthographic size
                    if (playerCameras[i].orthographic)
                    {
                        playerCameraOriginalSizes[i] = playerCameras[i].orthographicSize;
                    }
                }
            }
        }
    }
    
    private void Update()
    {
        // Handle movement for the currently active camera only
        Camera currentCamera = GetActiveCamera();
        if (currentCamera != null && currentCamera != mainCamera)
        {
            HandleDragMovement(currentCamera, activePlayerCamera);
            HandleKeyboardMovement(currentCamera, activePlayerCamera);
            HandleZooming(currentCamera, activePlayerCamera);
        }
        HandlePlayerAreaNavigation();
    }
    
    /// <summary>
    /// Handle navigation between player areas using A/D or arrow keys
    /// </summary>
    private void HandlePlayerAreaNavigation()
{
    // Only process inputs if no transition is currently in progress
    if (currentTransition == null)
    {
        // Left navigation (A or left arrow)
        if (Input.GetKeyDown(KeyCode.A) || Input.GetKeyDown(KeyCode.LeftArrow))
        {
            if (activePlayerCamera != -1)
            {
                // Calculate the previous player index (with wrap-around)
                int previousIndex = activePlayerCamera - 1;
                if (previousIndex < 0)
                    previousIndex = playerCameras.Length - 1;
                    
                // Toggle to the previous player's camera
                TogglePlayerCamera(previousIndex);
            }
        }
        // Right navigation (D or right arrow)
        else if (Input.GetKeyDown(KeyCode.D) || Input.GetKeyDown(KeyCode.RightArrow))
        {
            if (activePlayerCamera != -1)
            {
                // Calculate the next player index (with wrap-around)
                int nextIndex = (activePlayerCamera + 1) % playerCameras.Length;
                    
                // Toggle to the next player's camera
                TogglePlayerCamera(nextIndex);
            }
        }
        // Add specific keys for going to main camera
        else if (Input.GetKeyDown(KeyCode.Escape) || Input.GetKeyDown(KeyCode.M))
        {
            if (activePlayerCamera != -1)
            {
                // Always transition back to main camera
                TogglePlayerCamera(-1);
            }
        }
    }
}

    /// <summary>
    /// Get the currently active camera
    /// </summary>
    public Camera GetActiveCamera()
    {
        if (activePlayerCamera != -1 && activePlayerCamera < playerCameras.Length)
        {
            return playerCameras[activePlayerCamera];
        }
        return mainCamera;
    }
    
    /// <summary>
    /// Handle mouse drag movement for camera
    /// </summary>
    private void HandleDragMovement(Camera camera, int cameraIndex)
    {
        // Start dragging on left mouse button down
        if (Input.GetMouseButtonDown(0))
        {
            isDragging = true;
            lastMousePosition = Input.mousePosition;
        }
        // End dragging on left mouse button up
        else if (Input.GetMouseButtonUp(0))
        {
            isDragging = false;
        }
        
        // Move camera while dragging
        if (isDragging)
        {
            Vector3 delta = Input.mousePosition - lastMousePosition;
            
            // Only apply movement in Z direction (vertical)
            float zMovement = delta.y * dragSpeed * Time.deltaTime;
            
            // Apply movement
            MoveCamera(camera, cameraIndex, zMovement);
            
            // Update last position
            lastMousePosition = Input.mousePosition;
        }
    }
    
    /// <summary>
    /// Handle keyboard movement for camera
    /// </summary>
    private void HandleKeyboardMovement(Camera camera, int cameraIndex)
    {
        float zMovement = 0;
        bool wUpPressed = Input.GetKey(KeyCode.W) || Input.GetKey(KeyCode.UpArrow);
        bool wUpReleased = Input.GetKeyUp(KeyCode.W) || Input.GetKeyUp(KeyCode.UpArrow);
        
        // Check if we're at the minimum Z boundary
        bool atMinZBound = IsAtMinZBound(camera, cameraIndex);
        
        // Handle W/Up key press
        if (wUpPressed)
        {
            if (atMinZBound)
            {
                // We're at the top
                if (!hasScrolledDown)
                {
                    // Never scrolled down, so immediately go back to main camera
                    if (currentTransition == null && activePlayerCamera != -1)
                    {
                        isAtTopBoundary = false;
                        hasReleasedAtTop = false;
                        TogglePlayerCamera(-1);
                        return;
                    }
                }
                else if (!isAtTopBoundary)
                {
                    // First time reaching the top after scrolling down
                    isAtTopBoundary = true;
                    hasReleasedAtTop = false;
                }
                // Already at the top, keep the camera locked here
                zMovement = 0;
            }
            else
            {
                // Normal camera movement (not at the top)
                zMovement -= keyMoveSpeed * Time.deltaTime;
            }
        }
        // Handle W/Up key release
        else if (wUpReleased)
        {
            if (atMinZBound && isAtTopBoundary)
            {
                if (hasReleasedAtTop)
                {
                    // Second release at the top - return to main camera
                    if (currentTransition == null && activePlayerCamera != -1)
                    {
                        ResetCameraFlags();
                        TogglePlayerCamera(-1);
                        return;
                    }
                }
                else
                {
                    // First release at the top - mark that we've released
                    hasReleasedAtTop = true;
                }
            }
        }
        
        // Move camera down with S or down arrow
        if (Input.GetKey(KeyCode.S) || Input.GetKey(KeyCode.DownArrow))
        {
            // Mark that we've scrolled down
            hasScrolledDown = true;
            
            // Reset top boundary flags when moving down
            isAtTopBoundary = false;
            hasReleasedAtTop = false;
            
            zMovement += keyMoveSpeed * Time.deltaTime;
        }
        
        // Apply movement only if there's actual movement to be made
        if (zMovement != 0)
        {
            MoveCamera(camera, cameraIndex, zMovement);
            
            // If moving down, mark that we've scrolled down
            if (zMovement > 0)
            {
                hasScrolledDown = true;
            }
        }
    }
    
    private void ResetCameraFlags()
    {
        isAtTopBoundary = false;
        hasReleasedAtTop = false;
        hasScrolledDown = false;
    }

    /// <summary>
    /// Check if the camera is at the minimum Z boundary
    /// </summary>
    private bool IsAtMinZBound(Camera camera, int cameraIndex)
    {
        // Can't be at min Z bound if we're using the main camera or invalid index
        if (cameraIndex < 0 || camera == null || cameraIndex >= playerCameraOriginalPositions.Length)
            return false;
            
        // Get original position as reference
        Vector3 originalPosition = playerCameraOriginalPositions[cameraIndex];
        
        // Check if camera is at or beyond the minimum Z bound (with a small threshold)
        float threshold = 0.01f; // Add a small threshold to account for floating point imprecision
        return camera.transform.position.z <= originalPosition.z + threshold;
    }

    /// <summary>
    /// Handle scroll wheel zooming for camera
    /// </summary>
    private void HandleZooming(Camera camera, int cameraIndex)
    {
        if (camera.orthographic)
        {
            // Get scroll wheel input
            float scrollDelta = Input.mouseScrollDelta.y;
            
            if (scrollDelta != 0)
            {
                // Calculate new orthographic size (negative because scrolling up should zoom in)
                float newSize = camera.orthographicSize - scrollDelta * zoomSpeed;
                
                // Clamp the zoom level between min and max values
                camera.orthographicSize = Mathf.Clamp(newSize, minZoomSize, maxZoomSize);
            }
        }
    }
    
    /// <summary>
    /// Move camera with z-axis constraints
    /// </summary>
    private void MoveCamera(Camera camera, int cameraIndex, float zMovement)
    {
        if (camera == null) return;
        
        // Get the original position as reference
        Vector3 originalPosition;
        
        // Get current position and apply movement
        Vector3 position = camera.transform.position;
        position.z += zMovement;
        
        // Apply different constraints for main camera vs player cameras
        if (cameraIndex == -1)
        {
            // Main camera can move freely within its own range
            originalPosition = mainCameraOriginalPosition;
            
            // For main camera, we might want to define a separate range or no range at all
            // Here we're not applying any constraints for the main camera
            // But you could add custom constraints if needed
        }
        else if (cameraIndex >= 0 && cameraIndex < playerCameraOriginalPositions.Length)
        {
            // Player cameras are constrained to their defined range
            originalPosition = playerCameraOriginalPositions[cameraIndex];
            
            // Calculate allowed Z range for player cameras
            float minAllowedZ = originalPosition.z;
            float maxAllowedZ = originalPosition.z + zMoveRange;
            
            // Clamp position for player cameras only
            position.z = Mathf.Clamp(position.z, minAllowedZ, maxAllowedZ);
        }
        else
        {
            Debug.LogError($"Invalid camera index: {cameraIndex}");
            return;
        }
        
        // Apply position
        camera.transform.position = position;
        
    }
    
    /// <summary>
    /// Toggle the camera view for a specific player
    /// </summary>
    /// <param name="playerIndex">The index of the player (0-3), or -1 for main camera</param>
    public void TogglePlayerCamera(int playerIndex)
    {
        ResetCameraFlags();

        // Special case for returning to main camera
        if (playerIndex == -1)
        {
            
            // Cancel any ongoing transition
            if (currentTransition != null)
            {
                StopCoroutine(currentTransition);
            }
            
            currentTransition = StartCoroutine(TransitionToCamera(-1));
            return;
        }
        
        // For player cameras, validate the index
        if (playerCameras == null || playerIndex < 0 || playerIndex >= playerCameras.Length)
        {
            Debug.LogWarning($"Invalid player camera index: {playerIndex}");
            return;
        }
        
        // Cancel any ongoing transition
        if (currentTransition != null)
        {
            StopCoroutine(currentTransition);
        }
        
        // If this player camera is already active, switch back to main camera
        if (activePlayerCamera == playerIndex)
        {
            currentTransition = StartCoroutine(TransitionToCamera(-1));
        }
        // Otherwise switch to this player's camera
        else
        {
            currentTransition = StartCoroutine(TransitionToCamera(playerIndex));
        }
    }

    /// <summary>
    /// Transition smoothly between cameras
    /// </summary>
    /// <param name="targetCameraIndex">The index of the target camera (-1 for main camera)</param>
    private IEnumerator TransitionToCamera(int targetCameraIndex)
    {
        // Determine source and target cameras
        Camera sourceCamera = (activePlayerCamera == -1) ? mainCamera : playerCameras[activePlayerCamera];
        Camera targetCamera = (targetCameraIndex == -1) ? mainCamera : playerCameras[targetCameraIndex];

        // Store main camera position when switching away from it
        if (activePlayerCamera == -1 && targetCameraIndex != -1)
        {
            mainCameraLastPosition = sourceCamera.transform.position;
            mainCameraLastRotation = sourceCamera.transform.rotation;
            if (sourceCamera.orthographic)
            {
                mainCameraLastSize = sourceCamera.orthographicSize;
            }
            hasStoredMainCameraPosition = true;
        }

        // Get original positions
        Vector3 sourceOriginalPos = (activePlayerCamera == -1) ?
            mainCameraOriginalPosition : playerCameraOriginalPositions[activePlayerCamera];
        Vector3 targetOriginalPos;
        Quaternion targetOriginalRotation;
        float targetOriginalSize;

        // Use stored position for main camera if available, otherwise use original
        if (targetCameraIndex == -1 && hasStoredMainCameraPosition)
        {
            targetOriginalPos = mainCameraLastPosition;
            targetOriginalRotation = mainCameraLastRotation;
            targetOriginalSize = mainCameraLastSize;
        }
        else if (targetCameraIndex == -1)
        {
            targetOriginalPos = mainCameraOriginalPosition;
            targetOriginalRotation = mainCameraOriginalRotation;
            targetOriginalSize = mainCameraOriginalSize;
        }
        else
        {
            targetOriginalPos = playerCameraOriginalPositions[targetCameraIndex];
            targetOriginalRotation = playerCameraOriginalRotations[targetCameraIndex];
            targetOriginalSize = playerCameraOriginalSizes[targetCameraIndex];
        }

        // Calculate offset from original for source camera (to preserve any user scrolling)
        // We'll only transfer offset when going between player cameras, not to/from main camera
        float sourceZOffset = 0;
        if (activePlayerCamera != -1 && targetCameraIndex != -1)
        {
            // Only transfer offset between player cameras
            sourceZOffset = sourceCamera.transform.position.z - sourceOriginalPos.z;
        }

        // Calculate offset from original size for source camera (to preserve any user zooming)
        float sourceSizeOffset = 0;
        if (sourceCamera.orthographic)
        {
            // We'll always transfer zoom level as that's a natural expectation
            float sourceOriginalSize = (activePlayerCamera == -1) ?
                mainCameraOriginalSize : playerCameraOriginalSizes[activePlayerCamera];
            sourceSizeOffset = sourceCamera.orthographicSize - sourceOriginalSize;
        }

        if (sourceCamera == null || targetCamera == null)
        {
            Debug.LogError("Source or target camera is null!");
            yield break;
        }

        // Activate the target camera (but don't disable source yet)
        targetCamera.gameObject.SetActive(true);

        // Store initial transform values
        Vector3 startPosition = sourceCamera.transform.position;
        Quaternion startRotation = sourceCamera.transform.rotation;
        float startFOV = sourceCamera.orthographic ? sourceCamera.orthographicSize : sourceCamera.fieldOfView;

        // Target should be at its original position plus any carried offset
        Vector3 targetPosition = targetOriginalPos;
        targetPosition.z += sourceZOffset; // Carry over the Z offset from source camera

        Quaternion targetRotation = targetOriginalRotation;

        // Target FOV should be original plus any carried offset (for zooming)
        float targetFOV;
        if (targetCamera.orthographic)
        {
            targetFOV = targetOriginalSize + sourceSizeOffset;
            targetFOV = Mathf.Clamp(targetFOV, minZoomSize, maxZoomSize); // Ensure it's within bounds
        }
        else
        {
            targetFOV = targetCamera.fieldOfView;
        }

        // Create a temporary camera for the transition
        GameObject tempCameraObj = new GameObject("TransitionCamera");
        Camera tempCamera = tempCameraObj.AddComponent<Camera>();
        tempCamera.CopyFrom(sourceCamera);

        AudioListener tempListener = tempCamera.GetComponent<AudioListener>();
        if (tempListener != null)
        {
            Destroy(tempListener);
        }

        // Make sure our temporary camera renders instead of source or target
        sourceCamera.enabled = false;
        targetCamera.enabled = false;
        tempCamera.enabled = true;

        // Perform the transition
        float elapsed = 0f;
        while (elapsed < transitionDuration)
        {
            float t = transitionCurve.Evaluate(elapsed / transitionDuration);

            // Update position, rotation, and FOV
            tempCamera.transform.position = Vector3.Lerp(startPosition, targetPosition, t);
            tempCamera.transform.rotation = Quaternion.Slerp(startRotation, targetRotation, t);

            if (tempCamera.orthographic)
            {
                tempCamera.orthographicSize = Mathf.Lerp(startFOV, targetFOV, t);
            }
            else
            {
                tempCamera.fieldOfView = Mathf.Lerp(startFOV, targetFOV, t);
            }

            elapsed += Time.deltaTime;
            yield return null;
        }

        // Set target camera position and zoom to respect the original values plus any carried offsets
        targetCamera.transform.position = targetPosition;
        targetCamera.transform.rotation = targetRotation;

        if (targetCamera.orthographic)
        {
            targetCamera.orthographicSize = targetFOV;
        }

        // Transition complete - activate target camera
        tempCamera.enabled = false;
        targetCamera.enabled = true;

        // If switching to main camera, disable the previous player camera
        if (targetCameraIndex == -1 && activePlayerCamera != -1)
        {
            playerCameras[activePlayerCamera].gameObject.SetActive(false);
        }
        // If switching from main camera to player camera, disable main camera
        else if (targetCameraIndex != -1 && activePlayerCamera == -1)
        {
            mainCamera.gameObject.SetActive(false);
        }
        // If switching between player cameras, disable the previous one
        else if (targetCameraIndex != -1 && activePlayerCamera != -1)
        {
            playerCameras[activePlayerCamera].gameObject.SetActive(false);
        }

        // Update active camera index
        activePlayerCamera = targetCameraIndex;
        ResetCameraFlags();

        // Clean up
        Destroy(tempCameraObj);
        currentTransition = null;
    }
}