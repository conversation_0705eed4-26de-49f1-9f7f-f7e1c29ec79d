// Modified DeltaVMapInitializer.cs
using UnityEngine;
using System.Collections;

public class DeltaVMapInitializer : MonoBehaviour
{
    [System.Serializable]
    public class PathDefinition
    {
        public GameObject from;
        public GameObject to;
        public float deltaVCost;
        public GameObject pathVisual;
        public bool isBidirectional = false;
        public float returnDeltaVCost = -1;
    }
    
    [SerializeField] private GameObject[] celestialBodies;
    [SerializeField] private PathDefinition[] paths;
    [SerializeField] private GameObject solarSystemGraphObject; // Add this reference
    
    private void Awake()
    {
        // Start a coroutine that will wait until SolarSystemGraph is available
        StartCoroutine(InitializeGraphWhenReady());
    }
    
    private IEnumerator InitializeGraphWhenReady()
    {
        // If we have a direct reference, use that
        SolarSystemGraph graph = null;
        
        if (solarSystemGraphObject != null)
        {
            graph = solarSystemGraphObject.GetComponent<SolarSystemGraph>();
        }
        
        // If no direct reference or it's null, try to find it in the scene
        if (graph == null)
        {
            graph = FindFirstObjectByType<SolarSystemGraph>();
        }
        
        // Wait until SolarSystemGraph.Instance is not null
        int attemptsRemaining = 50; // Limit the number of attempts
        while (SolarSystemGraph.Instance == null && attemptsRemaining > 0)
        {
            yield return new WaitForSeconds(0.1f);
            attemptsRemaining--;
            
            // Try to find it again if we couldn't find it before
            if (graph == null)
            {
                graph = FindFirstObjectByType<SolarSystemGraph>();
                if (graph != null)
                {
                    // If we found it, we might need to wait for its Awake to finish
                    yield return null;
                }
            }
        }
        
        if (SolarSystemGraph.Instance == null)
        {
            Debug.LogError("Failed to find SolarSystemGraph.Instance after multiple attempts. Make sure there is a GameObject with SolarSystemGraph component in the scene.");
            yield break;
        }
        
        // Now that we have the instance, initialize the graph
        InitializeGraph();
    }
    
    private void InitializeGraph()
    {
        SolarSystemGraph graph = SolarSystemGraph.Instance;
        
        if (graph == null)
        {
            Debug.LogError("Graph instance is still null! Make sure SolarSystemGraph exists in the scene.");
            return;
        }
        
        // Add all celestial bodies
        foreach (var body in celestialBodies)
        {
            if (body == null)
            {
                Debug.LogError("Null celestial body found in array!");
                continue;
            }
            
            graph.AddNode(body);
            //Debug.Log($"Added {body.name} to graph");
        }
        
        // Add all paths
        foreach (var path in paths)
        {
            if (path.from == null || path.to == null)
            {
                Debug.LogError("Null reference in path definition!");
                continue;
            }
            
            // Add the forward path
            graph.AddDirectionalPath(path.from, path.to, path.deltaVCost, path.pathVisual);
            //Debug.Log($"Added path from {path.from.name} to {path.to.name} with cost {path.deltaVCost}");
            
            // If it's bidirectional, add the return path too
            if (path.isBidirectional)
            {
                float returnCost = path.returnDeltaVCost >= 0 ? path.returnDeltaVCost : path.deltaVCost;
                graph.AddDirectionalPath(path.to, path.from, returnCost, path.pathVisual);
                //Debug.Log($"Added return path from {path.to.name} to {path.from.name} with cost {returnCost}");
            }
        }
    }
}