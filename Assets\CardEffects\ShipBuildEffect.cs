using System.Collections.Generic;
using UnityEngine;

// Ship blueprint effects
public class ShipBuildEffect : ICardEffect
{
    private string name;
    private float deltaVPerFuel;
    private int cargoCapacity;
    private int strength;
    private List<Resource> buildCost;
    private bool isConsumedOnSurvey;
    
    public ShipBuildEffect(string name, float deltaVPerFuel, int cargoCapacity, int strength, List<Resource> buildCost, bool isConsumedOnSurvey)
    {
        this.name = name;
        this.deltaVPerFuel = deltaVPerFuel;
        this.cargoCapacity = cargoCapacity;
        this.strength = strength;
        this.buildCost = buildCost;
        this.isConsumedOnSurvey = isConsumedOnSurvey;
    }
    
    public bool CanExecute(Player player, GameObject location, Dictionary<string, object> parameters)
    {
        // Check if player has resources to build
        foreach (var resource in buildCost)
        {
            if (player.GetResourceAmount(location, resource.Type) < resource.Amount)
                return false;
        }
        
        // Check if player has an Advanced Assembler (required for ships)
        // Earth has an Advanced Assembler by default
        if (location.name != "Earth")
        {
            bool hasAdvancedAssembler = false;
            foreach (Module module in player.GetModulesOnPlanet(location))
            {
                if (module.Name == "Advanced Assembler")
                {
                    hasAdvancedAssembler = true;
                    break;
                }
            }
            
            if (!hasAdvancedAssembler)
                return false;
        }
        
        // Check if player has reached their ship limit
        if (player.ShipsInPlay >= player.MaxShips)
            return false;
            
        return true;
    }
    
    public bool Execute(Player player, GameObject location, Dictionary<string, object> parameters)
    {
        // Deduct resources for building
        foreach (var resource in buildCost)
        {
            if (!player.UseResource(location, resource.Type, resource.Amount))
                return false;
        }
        
        // Create the ship
        Ship newShip = new Ship
        {
            Name = name,
            CurrentLocation = location,
            DeltaVPerFuel = deltaVPerFuel,
            CargoCapacity = cargoCapacity,
            Strength = strength,
            IsConsumedOnSurvey = isConsumedOnSurvey
        };
        
        // Add to player's fleet
        player.AddShip(newShip);
        player.ShipsInPlay++;
        
        // Register with the planet
        PlanetBody planetBody = location.GetComponent<PlanetBody>();
        if (planetBody != null)
        {
            planetBody.RegisterShipArrival(newShip, player);
        }
        
        Debug.Log($"Player {player.PlayerId} built {name} at {location.name}");
        return true;
    }
    
    public string GetDescription()
    {
        string description = $"Ship: Cargo {GetCargoSizeText()}, {deltaVPerFuel} delta-v per fuel";
        
        if (strength > 0)
            description += $", Strength {strength}";
            
        if (isConsumedOnSurvey)
            description += ", Ship is consumed at destination for survey";
            
        return description;
    }
    
    private string GetCargoSizeText()
    {
        // Convert total capacity to dimensions
        int cargoWidth = 2;
        int cargoHeight = (cargoCapacity + 1) / 2; // Round up division
        
        // Special common cases
        if (cargoCapacity == 1) { cargoWidth = 1; cargoHeight = 1; }
        else if (cargoCapacity == 2) { cargoWidth = 1; cargoHeight = 2; }
        else if (cargoCapacity == 4) { cargoWidth = 2; cargoHeight = 2; }
        else if (cargoCapacity == 6) { cargoWidth = 2; cargoHeight = 3; }
        else if (cargoCapacity == 8) { cargoWidth = 2; cargoHeight = 4; }
        else if (cargoCapacity == 9) { cargoWidth = 3; cargoHeight = 3; }
        
        return $"{cargoWidth}x{cargoHeight}";
    }
}