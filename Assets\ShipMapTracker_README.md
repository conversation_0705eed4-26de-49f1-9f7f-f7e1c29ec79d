# Ship Map Tracker System

This system tracks ships in the play area and displays them as 3D ship pieces on the map at their respective world locations.

## Setup Instructions

1. **Add ShipMapTrackerInitializer to the scene:**
   - Create an empty GameObject in your scene
   - Add the `ShipMapTrackerInitializer` component to it
   - Assign the ship prefabs in the inspector:
     - Red Ship Prefab: `Assets/prefabs/RedRocket Variant.prefab`
     - Green Ship Prefab: `Assets/prefabs/GreenRocket Variant.prefab`
     - Blue Ship Prefab: `Assets/prefabs/BlueRocket Variant.prefab`
     - Yellow Ship Prefab: `Assets/prefabs/YellowRocket Variant.prefab`

2. **Player Color Mapping:**
   - Player 0 (first player): Red ships
   - Player 1 (second player): Green ships
   - Player 2 (third player): Blue ships
   - Player 3 (fourth player): Yellow ships

## How It Works

### Ship Positioning
Ships are positioned relative to their world's transform using this pattern:
- If the world is at position (x, y, z):
  - 1st ship: (x-2, y+1, z+2.5)
  - 2nd ship: (x-1, y+1, z+2.5)
  - 3rd ship: (x, y+1, z+2.5)
  - 4th ship: (x+1, y+1, z+2.5)
  - 5th ship: (x-2, y+1, z+3.5)
  - 6th ship: (x-1, y+1, z+3.5)
  - And so on...

Ships are arranged in rows of 4, with each row spaced 1 unit further in the Z direction.

### Automatic Updates
The system automatically updates ship pieces when:
- A ship is built at a world
- A ship is consumed (e.g., survey probes)
- A ship is removed from a player's fleet
- Starting ships are added to players

### Current Limitations
- Only tracks ships at regular worlds (not orbit locations yet)
- Supports up to 4 players
- Ship pieces are simple instantiated prefabs without additional logic

## Integration Points

The system integrates with existing code at these points:
- `PlayAreaManager.OnPlayerBuiltShip()` - Updates when ships are built
- `PlayAreaManager.OnShipConsumed()` - Updates when ships are consumed
- `PlayerPlayArea.AddStartingShip()` - Updates when starting ships are added
- `Player.RemoveShip()` - Updates when ships are removed

## Future Enhancements

Planned features for orbit location support:
- Set `trackOrbitLocations = true` in the ShipMapTracker component
- Orbit locations will be handled in a future update
- Ship movement between worlds and orbits will trigger updates

## Troubleshooting

- **No ship pieces appear:** Check that ship prefabs are assigned in the ShipMapTrackerInitializer
- **Wrong colors:** Verify player color mapping matches your game setup
- **Ships not updating:** Ensure ShipMapTracker.Instance is not null when ship events occur
- **Performance issues:** The system recreates all ship pieces for a player when any change occurs - this could be optimized for large numbers of ships
